/**
 * React Query hooks for System Configuration operations
 * 
 * Provides type-safe hooks for electrical system configuration management
 * with IEEE/IEC standards compliance and user preference handling.
 * 
 * Features:
 * - System configuration CRUD operations
 * - Electrical standards management
 * - User preference management  
 * - Configuration template management
 * - Real-time validation and compliance checking
 * - Multi-standard support (IEC, IEEE, EN, etc.)
 */

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import type {
  SystemConfigurationCreate,
  SystemConfigurationUpdate,
  SystemConfigurationRead,
  ElectricalStandardCreate,
  ElectricalStandardUpdate,
  ElectricalStandardRead,
  UserPreferencesCreate,
  UserPreferencesUpdate,
  UserPreferencesRead,
  ConfigurationTemplateRead,
  SystemConfigurationOrganism,
  ConfigurationValidation,
  ConfigurationExport,
} from "@/types/systemConfiguration"
import type { ListQueryParams } from "@/types/api"
import { apiClient } from "@/lib/api/client"
import { toast } from "sonner"

// Query keys
export const systemConfigKeys = {
  all: ['system-configuration'] as const,
  configurations: (params?: ListQueryParams) => [...systemConfigKeys.all, 'configurations', params] as const,
  configuration: (configId: string) => [...systemConfigKeys.all, 'configuration', configId] as const,
  globalConfig: () => [...systemConfigKeys.all, 'global-config'] as const,
  projectConfig: (projectId: number) => [...systemConfigKeys.all, 'project-config', projectId] as const,
  userPreferences: (userId: number) => [...systemConfigKeys.all, 'user-preferences', userId] as const,
  electricalStandards: (params?: ListQueryParams) => [...systemConfigKeys.all, 'electrical-standards', params] as const,
  electricalStandard: (standardId: string) => [...systemConfigKeys.all, 'electrical-standard', standardId] as const,
  templates: (params?: ListQueryParams) => [...systemConfigKeys.all, 'templates', params] as const,
  template: (templateId: string) => [...systemConfigKeys.all, 'template', templateId] as const,
  organism: (scope: string, projectId?: number, userId?: number) => [...systemConfigKeys.all, 'organism', scope, projectId, userId] as const,
  validation: (configId: string) => [...systemConfigKeys.all, 'validation', configId] as const,
}

// System Configuration Hooks
export function useSystemConfigurations(params?: ListQueryParams) {
  return useQuery({
    queryKey: systemConfigKeys.configurations(params),
    queryFn: async () => {
      const response = await apiClient.get('/system-configurations', { params })
      return response.data as SystemConfigurationRead[]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useSystemConfiguration(configId: string) {
  return useQuery({
    queryKey: systemConfigKeys.configuration(configId),
    queryFn: async () => {
      const response = await apiClient.get(`/system-configurations/${configId}`)
      return response.data as SystemConfigurationRead
    },
    enabled: !!configId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useGlobalConfiguration() {
  return useQuery({
    queryKey: systemConfigKeys.globalConfig(),
    queryFn: async () => {
      const response = await apiClient.get('/system-configurations/global')
      return response.data as SystemConfigurationRead
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - global config changes less frequently
  })
}

export function useProjectConfiguration(projectId: number) {
  return useQuery({
    queryKey: systemConfigKeys.projectConfig(projectId),
    queryFn: async () => {
      const response = await apiClient.get(`/projects/${projectId}/configuration`)
      return response.data as SystemConfigurationRead
    },
    enabled: !!projectId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useCreateSystemConfiguration() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (configData: SystemConfigurationCreate) => {
      const response = await apiClient.post('/system-configurations', configData)
      return response.data as SystemConfigurationRead
    },
    onSuccess: (data, variables) => {
      // Invalidate relevant queries based on scope
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.configurations() })
      
      if (data.scope === 'global') {
        queryClient.invalidateQueries({ queryKey: systemConfigKeys.globalConfig() })
      } else if (data.scope === 'project' && data.project_id) {
        queryClient.invalidateQueries({ queryKey: systemConfigKeys.projectConfig(data.project_id) })
      }
      
      toast.success(`Configuration "${data.name}" created successfully`)
    },
    onError: (error: any) => {
      toast.error(`Failed to create configuration: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

export function useUpdateSystemConfiguration() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      configId, 
      configData 
    }: { 
      configId: string
      configData: SystemConfigurationUpdate 
    }) => {
      const response = await apiClient.put(`/system-configurations/${configId}`, configData)
      return response.data as SystemConfigurationRead
    },
    onMutate: async ({ configId, configData }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: systemConfigKeys.configuration(configId) })
      
      // Snapshot the previous value
      const previousConfig = queryClient.getQueryData(systemConfigKeys.configuration(configId))
      
      // Optimistically update to the new value
      queryClient.setQueryData(systemConfigKeys.configuration(configId), (old: SystemConfigurationRead | undefined) => 
        old ? { ...old, ...configData, updated_at: new Date().toISOString() } : old
      )
      
      // Return a context object with the snapshotted value
      return { previousConfig, configId }
    },
    onError: (error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousConfig) {
        queryClient.setQueryData(systemConfigKeys.configuration(context.configId), context.previousConfig)
      }
      toast.error(`Failed to update configuration: ${error?.response?.data?.detail || error.message}`)
    },
    onSuccess: (data, variables, context) => {
      // Update the specific configuration query
      queryClient.setQueryData(systemConfigKeys.configuration(variables.configId), data)
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.configurations() })
      
      if (data.scope === 'global') {
        queryClient.invalidateQueries({ queryKey: systemConfigKeys.globalConfig() })
      } else if (data.scope === 'project' && data.project_id) {
        queryClient.invalidateQueries({ queryKey: systemConfigKeys.projectConfig(data.project_id) })
      }
      
      // Invalidate organism data that depends on this configuration
      queryClient.invalidateQueries({ 
        queryKey: systemConfigKeys.organism(data.scope, data.project_id) 
      })
      
      toast.success(`Configuration "${data.name}" updated successfully`)
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.configuration(variables.configId) })
    },
  })
}

export function useDeleteSystemConfiguration() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (configId: string) => {
      await apiClient.delete(`/system-configurations/${configId}`)
    },
    onSuccess: (data, configId) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: systemConfigKeys.configuration(configId) })
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.configurations() })
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.globalConfig() })
      
      toast.success("Configuration deleted successfully")
    },
    onError: (error: any) => {
      toast.error(`Failed to delete configuration: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

// Electrical Standards Hooks
export function useElectricalStandards(params?: ListQueryParams) {
  return useQuery({
    queryKey: systemConfigKeys.electricalStandards(params),
    queryFn: async () => {
      const response = await apiClient.get('/electrical-standards', { params })
      return response.data as ElectricalStandardRead[]
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - standards change infrequently
  })
}

export function useElectricalStandard(standardId: string) {
  return useQuery({
    queryKey: systemConfigKeys.electricalStandard(standardId),
    queryFn: async () => {
      const response = await apiClient.get(`/electrical-standards/${standardId}`)
      return response.data as ElectricalStandardRead
    },
    enabled: !!standardId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useCreateElectricalStandard() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (standardData: ElectricalStandardCreate) => {
      const response = await apiClient.post('/electrical-standards', standardData)
      return response.data as ElectricalStandardRead
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.electricalStandards() })
      toast.success(`Standard "${data.standard_number}" created successfully`)
    },
    onError: (error: any) => {
      toast.error(`Failed to create standard: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

export function useUpdateElectricalStandard() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      standardId, 
      standardData 
    }: { 
      standardId: string
      standardData: ElectricalStandardUpdate 
    }) => {
      const response = await apiClient.put(`/electrical-standards/${standardId}`, standardData)
      return response.data as ElectricalStandardRead
    },
    onSuccess: (data, variables) => {
      // Update caches
      queryClient.setQueryData(systemConfigKeys.electricalStandard(variables.standardId), data)
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.electricalStandards() })
      
      toast.success(`Standard "${data.standard_number}" updated successfully`)
    },
    onError: (error: any) => {
      toast.error(`Failed to update standard: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

// User Preferences Hooks
export function useUserPreferences(userId: number) {
  return useQuery({
    queryKey: systemConfigKeys.userPreferences(userId),
    queryFn: async () => {
      const response = await apiClient.get(`/users/${userId}/preferences`)
      return response.data as UserPreferencesRead
    },
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useUpdateUserPreferences() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      userId, 
      preferencesData 
    }: { 
      userId: number
      preferencesData: UserPreferencesUpdate 
    }) => {
      const response = await apiClient.put(`/users/${userId}/preferences`, preferencesData)
      return response.data as UserPreferencesRead
    },
    onMutate: async ({ userId, preferencesData }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: systemConfigKeys.userPreferences(userId) })
      
      // Snapshot the previous value
      const previousPrefs = queryClient.getQueryData(systemConfigKeys.userPreferences(userId))
      
      // Optimistically update to the new value
      queryClient.setQueryData(systemConfigKeys.userPreferences(userId), (old: UserPreferencesRead | undefined) => 
        old ? { ...old, ...preferencesData, updated_at: new Date().toISOString() } : old
      )
      
      // Return a context object with the snapshotted value
      return { previousPrefs, userId }
    },
    onError: (error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousPrefs) {
        queryClient.setQueryData(systemConfigKeys.userPreferences(context.userId), context.previousPrefs)
      }
      toast.error(`Failed to update preferences: ${error?.response?.data?.detail || error.message}`)
    },
    onSuccess: (data, variables, context) => {
      queryClient.setQueryData(systemConfigKeys.userPreferences(variables.userId), data)
      
      // Invalidate organism data that depends on user preferences
      queryClient.invalidateQueries({ 
        queryKey: systemConfigKeys.organism('user', undefined, variables.userId) 
      })
      
      toast.success("Preferences updated successfully")
    },
  })
}

// Configuration Templates Hooks
export function useConfigurationTemplates(params?: ListQueryParams) {
  return useQuery({
    queryKey: systemConfigKeys.templates(params),
    queryFn: async () => {
      const response = await apiClient.get('/configuration-templates', { params })
      return response.data as ConfigurationTemplateRead[]
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useApplyConfigurationTemplate() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      templateId, 
      scope, 
      projectId 
    }: { 
      templateId: string
      scope: 'global' | 'project' | 'user'
      projectId?: number 
    }) => {
      const response = await apiClient.post('/configuration-templates/apply', {
        template_id: templateId,
        scope,
        project_id: projectId
      })
      return response.data as SystemConfigurationRead
    },
    onSuccess: (data, variables) => {
      // Invalidate relevant configuration queries
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.configurations() })
      
      if (variables.scope === 'global') {
        queryClient.invalidateQueries({ queryKey: systemConfigKeys.globalConfig() })
      } else if (variables.scope === 'project' && variables.projectId) {
        queryClient.invalidateQueries({ queryKey: systemConfigKeys.projectConfig(variables.projectId) })
      }
      
      // Invalidate organism data
      queryClient.invalidateQueries({ 
        queryKey: systemConfigKeys.organism(variables.scope, variables.projectId) 
      })
      
      toast.success("Configuration template applied successfully")
    },
    onError: (error: any) => {
      toast.error(`Failed to apply template: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

// System Configuration Organism Hook (for SystemConfiguration organism)
export function useSystemConfigurationOrganism(
  scope: 'global' | 'project' | 'user' = 'global', 
  projectId?: number,
  userId?: number
) {
  return useQuery({
    queryKey: systemConfigKeys.organism(scope, projectId, userId),
    queryFn: async () => {
      let endpoint = '/system-configuration/organism'
      const params = new URLSearchParams({ scope })
      
      if (projectId) params.append('project_id', projectId.toString())
      if (userId) params.append('user_id', userId.toString())
      
      const response = await apiClient.get(`${endpoint}?${params}`)
      return response.data as SystemConfigurationOrganism
    },
    enabled: scope === 'global' || (scope === 'project' && !!projectId) || (scope === 'user' && !!userId),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes for organism data freshness
  })
}

// Configuration Validation Hooks
export function useValidateConfiguration() {
  return useMutation({
    mutationFn: async (configId: string) => {
      const response = await apiClient.post(`/system-configurations/${configId}/validate`)
      return response.data as ConfigurationValidation
    },
    onSuccess: (data) => {
      if (data.valid) {
        toast.success("Configuration validation passed")
      } else {
        toast.warning(`Configuration has ${data.issues.length} validation issues`)
      }
    },
    onError: (error: any) => {
      toast.error(`Failed to validate configuration: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

export function useConfigurationValidation(configId: string) {
  return useQuery({
    queryKey: systemConfigKeys.validation(configId),
    queryFn: async () => {
      const response = await apiClient.get(`/system-configurations/${configId}/validation`)
      return response.data as ConfigurationValidation
    },
    enabled: !!configId,
    staleTime: 60 * 1000, // 1 minute - validation results can change quickly
  })
}

// Configuration Import/Export Hooks
export function useExportConfiguration() {
  return useMutation({
    mutationFn: async ({ 
      configId, 
      scope, 
      projectId 
    }: { 
      configId?: string
      scope: 'global' | 'project' | 'user'
      projectId?: number 
    }) => {
      let endpoint = '/system-configuration/export'
      const params = new URLSearchParams({ scope })
      
      if (configId) params.append('config_id', configId)
      if (projectId) params.append('project_id', projectId.toString())
      
      const response = await apiClient.get(`${endpoint}?${params}`)
      return response.data as ConfigurationExport
    },
    onSuccess: () => {
      toast.success("Configuration exported successfully")
    },
    onError: (error: any) => {
      toast.error(`Failed to export configuration: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

export function useImportConfiguration() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (configData: ConfigurationExport) => {
      const response = await apiClient.post('/system-configuration/import', configData)
      return response.data as SystemConfigurationRead
    },
    onSuccess: (data) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: systemConfigKeys.configurations() })
      
      if (data.scope === 'global') {
        queryClient.invalidateQueries({ queryKey: systemConfigKeys.globalConfig() })
      } else if (data.scope === 'project' && data.project_id) {
        queryClient.invalidateQueries({ queryKey: systemConfigKeys.projectConfig(data.project_id) })
      }
      
      toast.success("Configuration imported successfully")
    },
    onError: (error: any) => {
      toast.error(`Failed to import configuration: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

// Utility hooks for configuration management
export function useActiveStandards(scope: 'global' | 'project' = 'global', projectId?: number) {
  const { data: globalConfig } = useGlobalConfiguration()
  const { data: projectConfig } = useProjectConfiguration(projectId || 0)
  const { data: allStandards } = useElectricalStandards({ active: true })
  
  const config = scope === 'project' && projectConfig ? projectConfig : globalConfig
  const activeStandardIds = config?.standards_compliance || []
  
  return {
    activeStandards: allStandards?.filter(standard => 
      activeStandardIds.includes(`${standard.standard_type}_${standard.standard_number}`)
    ) || [],
    totalActiveStandards: activeStandardIds.length,
    config: config
  }
}

export function useComplianceStatus(projectId?: number) {
  const { data: organism } = useSystemConfigurationOrganism(
    projectId ? 'project' : 'global', 
    projectId
  )
  
  const complianceEntries = Object.entries(organism?.compliance_status || {})
  
  return {
    overallCompliance: complianceEntries.length > 0,
    complianceCount: complianceEntries.filter(([_, status]) => status === 'COMPLIANT').length,
    totalStandards: complianceEntries.length,
    nonCompliantStandards: complianceEntries.filter(([_, status]) => status === 'NON_COMPLIANT'),
    warningStandards: complianceEntries.filter(([_, status]) => status === 'WARNING'),
    compliancePercentage: complianceEntries.length > 0 
      ? Math.round((complianceEntries.filter(([_, status]) => status === 'COMPLIANT').length / complianceEntries.length) * 100)
      : 0
  }
}