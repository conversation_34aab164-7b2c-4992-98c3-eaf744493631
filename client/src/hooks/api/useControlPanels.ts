/**
 * Control Panels React Query Hooks
 * 
 * React Query hooks for Control Panels server state management.
 * Provides real-time control system data with safety interlock support.
 * 
 * Features:
 * - Real-time control equipment data fetching
 * - Safety interlock monitoring
 * - Control action mutations with confirmation
 * - WebSocket integration for live updates
 * - Error boundary integration
 * - Professional electrical engineering context
 * - TypeScript strict mode compliance
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query"
import { toast } from "sonner"

import { apiClient } from "@/lib/api"
import type {
  ControlGroup,
  ControlEquipment, 
  ControlSession,
  ControlAlert,
  ControlLogEntry,
  ControlAction,
  SafetyInterlock,
  ControlSystemType,
  ControlState,
  ControlFilters,
  ControlSequenceStep,
} from "@/components/organisms/ControlPanels/ControlPanelsTypes"

// Query Keys
const CONTROL_PANELS_KEYS = {
  all: ["control-panels"] as const,
  groups: () => [...CONTROL_PANELS_KEYS.all, "groups"] as const,
  group: (id: string) => [...CONTROL_PANELS_KEYS.groups(), id] as const,
  equipment: () => [...CONTROL_PANELS_KEYS.all, "equipment"] as const,
  equipmentItem: (id: string) => [...CONTROL_PANELS_KEYS.equipment(), id] as const,
  equipmentFiltered: (filters: ControlFilters) => [...CONTROL_PANELS_KEYS.equipment(), "filtered", filters] as const,
  interlocks: () => [...CONTROL_PANELS_KEYS.all, "interlocks"] as const,
  interlocksByEquipment: (equipmentId: string) => [...CONTROL_PANELS_KEYS.interlocks(), equipmentId] as const,
  alerts: () => [...CONTROL_PANELS_KEYS.all, "alerts"] as const,
  alertsActive: () => [...CONTROL_PANELS_KEYS.alerts(), "active"] as const,
  session: () => [...CONTROL_PANELS_KEYS.all, "session"] as const,
  logs: () => [...CONTROL_PANELS_KEYS.all, "logs"] as const,
  logsFiltered: (filters: Record<string, unknown>) => [...CONTROL_PANELS_KEYS.logs(), "filtered", filters] as const,
}

// API Types
interface ControlGroupsResponse {
  readonly controlGroups: ReadonlyArray<ControlGroup>
  readonly total: number
  readonly hasMore: boolean
}

interface ControlEquipmentResponse {
  readonly equipment: ReadonlyArray<ControlEquipment>
  readonly total: number
  readonly hasMore: boolean
}

interface ControlAlertsResponse {
  readonly alerts: ReadonlyArray<ControlAlert>
  readonly total: number
  readonly hasMore: boolean
}

interface ControlLogsResponse {
  readonly logs: ReadonlyArray<ControlLogEntry>
  readonly total: number
  readonly hasMore: boolean
}

interface ControlActionRequest {
  readonly equipmentId: string
  readonly action: ControlAction
  readonly confirmationCode?: string
  readonly overrideInterlocks?: boolean
  readonly reason?: string
}

interface BulkControlActionRequest {
  readonly equipmentIds: ReadonlyArray<string>
  readonly action: ControlAction
  readonly confirmationCode?: string
  readonly overrideInterlocks?: boolean
  readonly reason?: string
}

interface GroupActionRequest {
  readonly groupId: string
  readonly action: ControlAction
  readonly confirmationCode?: string
  readonly overrideInterlocks?: boolean
  readonly reason?: string
}

interface SequenceExecutionRequest {
  readonly groupId: string
  readonly sequence: ReadonlyArray<ControlSequenceStep>
  readonly confirmationCode?: string
  readonly reason?: string
}

interface InterlockBypassRequest {
  readonly interlockId: string
  readonly reason: string
  readonly duration?: number // minutes
}

interface EmergencyStopRequest {
  readonly reason: string
  readonly scope: "all" | "group" | "equipment"
  readonly targetId?: string
}

// Control Groups Query
export const useControlGroups = (
  filters?: ControlFilters,
  options?: {
    enabled?: boolean
    refetchInterval?: number
    staleTime?: number
  }
) => {
  return useQuery({
    queryKey: CONTROL_PANELS_KEYS.groups(),
    queryFn: async (): Promise<ControlGroupsResponse> => {
      const params = new URLSearchParams()
      
      if (filters?.systemType) {
        filters.systemType.forEach(type => params.append("systemType", type))
      }
      if (filters?.state) {
        filters.state.forEach(state => params.append("state", state))
      }
      if (filters?.location) {
        params.append("location", filters.location)
      }
      if (filters?.hasInterlocks !== undefined) {
        params.append("hasInterlocks", filters.hasInterlocks.toString())
      }
      if (filters?.isControllable !== undefined) {
        params.append("isControllable", filters.isControllable.toString())
      }
      if (filters?.isOnline !== undefined) {
        params.append("isOnline", filters.isOnline.toString())
      }
      if (filters?.searchQuery) {
        params.append("search", filters.searchQuery)
      }
      
      const queryString = params.toString()
      const response = await apiClient.get<ControlGroupsResponse>(
        `/api/v1/control-groups${queryString ? `?${queryString}` : ""}`
      )
      return response.data
    },
    enabled: options?.enabled ?? true,
    refetchInterval: options?.refetchInterval ?? 10000, // 10 seconds for control systems
    staleTime: options?.staleTime ?? 5000, // 5 seconds
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  })
}

// Single Control Group Query
export const useControlGroup = (
  groupId: string,
  options?: {
    enabled?: boolean
  }
) => {
  return useQuery({
    queryKey: CONTROL_PANELS_KEYS.group(groupId),
    queryFn: async (): Promise<ControlGroup> => {
      const response = await apiClient.get<ControlGroup>(`/api/v1/control-groups/${groupId}`)
      return response.data
    },
    enabled: (options?.enabled ?? true) && Boolean(groupId),
    staleTime: 5000,
  })
}

// Control Equipment Query  
export const useControlEquipment = (
  filters?: ControlFilters,
  options?: {
    enabled?: boolean
    refetchInterval?: number
  }
) => {
  return useQuery({
    queryKey: CONTROL_PANELS_KEYS.equipmentFiltered(filters || {}),
    queryFn: async (): Promise<ControlEquipmentResponse> => {
      const params = new URLSearchParams()
      
      if (filters?.systemType) {
        filters.systemType.forEach(type => params.append("systemType", type))
      }
      if (filters?.state) {
        filters.state.forEach(state => params.append("state", state))
      }
      if (filters?.location) {
        params.append("location", filters.location)
      }
      if (filters?.hasInterlocks !== undefined) {
        params.append("hasInterlocks", filters.hasInterlocks.toString())
      }
      if (filters?.isControllable !== undefined) {
        params.append("isControllable", filters.isControllable.toString())
      }
      if (filters?.isOnline !== undefined) {
        params.append("isOnline", filters.isOnline.toString())
      }
      if (filters?.searchQuery) {
        params.append("search", filters.searchQuery)
      }
      
      const queryString = params.toString()
      const response = await apiClient.get<ControlEquipmentResponse>(
        `/api/v1/control-equipment${queryString ? `?${queryString}` : ""}`
      )
      return response.data
    },
    enabled: options?.enabled ?? true,
    refetchInterval: options?.refetchInterval ?? 10000, // 10 seconds
    staleTime: 5000,
    refetchOnWindowFocus: true,
  })
}

// Single Equipment Query
export const useControlEquipmentItem = (
  equipmentId: string,
  options?: {
    enabled?: boolean
  }
) => {
  return useQuery({
    queryKey: CONTROL_PANELS_KEYS.equipmentItem(equipmentId),
    queryFn: async (): Promise<ControlEquipment> => {
      const response = await apiClient.get<ControlEquipment>(`/api/v1/control-equipment/${equipmentId}`)
      return response.data
    },
    enabled: (options?.enabled ?? true) && Boolean(equipmentId),
    staleTime: 5000,
  })
}

// Safety Interlocks Query
export const useControlInterlocks = (
  equipmentId?: string,
  options?: {
    enabled?: boolean
  }
) => {
  return useQuery({
    queryKey: equipmentId 
      ? CONTROL_PANELS_KEYS.interlocksByEquipment(equipmentId)
      : CONTROL_PANELS_KEYS.interlocks(),
    queryFn: async (): Promise<ReadonlyArray<SafetyInterlock>> => {
      const endpoint = equipmentId 
        ? `/api/v1/control-interlocks/equipment/${equipmentId}`
        : "/api/v1/control-interlocks"
      const response = await apiClient.get<ReadonlyArray<SafetyInterlock>>(endpoint)
      return response.data
    },
    enabled: options?.enabled ?? true,
    refetchInterval: 15000, // 15 seconds for safety-critical data
    staleTime: 5000,
  })
}

// Control Alerts Query
export const useControlAlerts = (
  activeOnly = true,
  options?: {
    enabled?: boolean
  }
) => {
  return useQuery({
    queryKey: activeOnly ? CONTROL_PANELS_KEYS.alertsActive() : CONTROL_PANELS_KEYS.alerts(),
    queryFn: async (): Promise<ControlAlertsResponse> => {
      const params = activeOnly ? "?active=true" : ""
      const response = await apiClient.get<ControlAlertsResponse>(`/api/v1/control-alerts${params}`)
      return response.data
    },
    enabled: options?.enabled ?? true,
    refetchInterval: 5000, // 5 seconds for alerts
    staleTime: 2000,
    refetchOnWindowFocus: true,
  })
}

// Control Session Query
export const useControlSession = (
  options?: {
    enabled?: boolean
  }
) => {
  return useQuery({
    queryKey: CONTROL_PANELS_KEYS.session(),
    queryFn: async (): Promise<ControlSession | null> => {
      try {
        const response = await apiClient.get<ControlSession>("/api/v1/control-session")
        return response.data
      } catch (error: any) {
        if (error.response?.status === 404) {
          return null // No active session
        }
        throw error
      }
    },
    enabled: options?.enabled ?? true,
    refetchInterval: 60000, // 1 minute
    staleTime: 30000, // 30 seconds
    retry: false, // Don't retry session queries
  })
}

// Control Logs Query (with infinite loading)
export const useControlLogs = (
  filters?: Record<string, unknown>,
  options?: {
    enabled?: boolean
  }
) => {
  return useInfiniteQuery({
    queryKey: CONTROL_PANELS_KEYS.logsFiltered(filters || {}),
    queryFn: async ({ pageParam = 0 }): Promise<ControlLogsResponse> => {
      const params = new URLSearchParams()
      params.append("page", pageParam.toString())
      params.append("limit", "50")
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined) {
            params.append(key, String(value))
          }
        })
      }
      
      const response = await apiClient.get<ControlLogsResponse>(`/api/v1/control-logs?${params.toString()}`)
      return response.data
    },
    getNextPageParam: (lastPage, pages) => 
      lastPage.hasMore ? pages.length : undefined,
    enabled: options?.enabled ?? true,
    staleTime: 30000, // 30 seconds for logs
  })
}

// Control Action Mutations
export const useControlAction = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (request: ControlActionRequest): Promise<void> => {
      await apiClient.post("/api/v1/control-actions", request)
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.groups() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.equipment() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.equipmentItem(variables.equipmentId) })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.logs() })
      
      toast.success(`Control action "${variables.action}" executed successfully`)
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Control action failed"
      toast.error(`Control Action Failed: ${message}`)
    },
  })
}

// Bulk Control Action Mutation
export const useBulkControlAction = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (request: BulkControlActionRequest): Promise<void> => {
      await apiClient.post("/api/v1/control-actions/bulk", request)
    },
    onSuccess: (_, variables) => {
      // Invalidate all relevant queries
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.groups() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.equipment() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.logs() })
      
      toast.success(`Bulk action "${variables.action}" executed on ${variables.equipmentIds.length} equipment`)
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Bulk action failed"
      toast.error(`Bulk Action Failed: ${message}`)
    },
  })
}

// Group Action Mutation
export const useGroupAction = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (request: GroupActionRequest): Promise<void> => {
      await apiClient.post("/api/v1/control-groups/action", request)
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.groups() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.group(variables.groupId) })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.logs() })
      
      toast.success(`Group action "${variables.action}" executed successfully`)
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Group action failed"
      toast.error(`Group Action Failed: ${message}`)
    },
  })
}

// Sequence Execution Mutation
export const useSequenceExecution = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (request: SequenceExecutionRequest): Promise<void> => {
      await apiClient.post("/api/v1/control-sequences/execute", request)
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.groups() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.group(variables.groupId) })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.logs() })
      
      toast.success("Control sequence executed successfully")
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Sequence execution failed"
      toast.error(`Sequence Failed: ${message}`)
    },
  })
}

// Emergency Stop Mutation
export const useEmergencyStop = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (request: EmergencyStopRequest): Promise<void> => {
      await apiClient.post("/api/v1/emergency-stop", request)
    },
    onSuccess: () => {
      // Invalidate all control-related queries
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.all })
      
      toast.error("EMERGENCY STOP ACTIVATED", {
        duration: 10000,
        style: {
          backgroundColor: "#dc2626",
          color: "white",
          border: "2px solid #991b1b",
        },
      })
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Emergency stop failed"
      toast.error(`Emergency Stop Failed: ${message}`)
    },
  })
}

// Interlock Bypass Mutation
export const useInterlockBypass = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (request: InterlockBypassRequest): Promise<void> => {
      await apiClient.post("/api/v1/control-interlocks/bypass", request)
    },
    onSuccess: () => {
      // Invalidate interlocks and related queries
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.interlocks() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.groups() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.equipment() })
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.logs() })
      
      toast.warning("Safety interlock bypassed", {
        description: "This action has been logged for safety compliance",
      })
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Interlock bypass failed"
      toast.error(`Interlock Bypass Failed: ${message}`)
    },
  })
}

// Alert Acknowledgment Mutation
export const useAcknowledgeControlAlert = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (alertId: string): Promise<void> => {
      await apiClient.post(`/api/v1/control-alerts/${alertId}/acknowledge`)
    },
    onSuccess: () => {
      // Invalidate alerts queries
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.alerts() })
      
      toast.success("Alert acknowledged")
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Alert acknowledgment failed"
      toast.error(`Failed to acknowledge alert: ${message}`)
    },
  })
}

// Mark All Alerts Read Mutation
export const useMarkAllControlAlertsRead = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (): Promise<void> => {
      await apiClient.post("/api/v1/control-alerts/acknowledge-all")
    },
    onSuccess: () => {
      // Invalidate alerts queries
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.alerts() })
      
      toast.success("All alerts marked as read")
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Failed to mark alerts as read"
      toast.error(message)
    },
  })
}

// Session Management Mutations
export const useCreateControlSession = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (): Promise<ControlSession> => {
      const response = await apiClient.post<ControlSession>("/api/v1/control-session")
      return response.data
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.session() })
      toast.success("Control session started")
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Failed to start session"
      toast.error(`Session Error: ${message}`)
    },
  })
}

export const useEndControlSession = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async (): Promise<void> => {
      await apiClient.delete("/api/v1/control-session")
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONTROL_PANELS_KEYS.session() })
      toast.info("Control session ended")
    },
    onError: (error: any) => {
      const message = error.response?.data?.message || error.message || "Failed to end session"
      toast.error(`Session Error: ${message}`)
    },
  })
}

// Export query keys for external use
export { CONTROL_PANELS_KEYS }