/**
 * Equipment API Hooks
 * 
 * React Query hooks for Equipment Dashboard server state management,
 * providing real-time electrical equipment data with caching and synchronization.
 * 
 * Features:
 * - Real-time equipment monitoring
 * - Automatic background updates
 * - Optimistic updates for actions
 * - Error handling and retry logic
 * - TypeScript strict mode compliance
 * - WebSocket integration for live data
 */

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query"
import { toast } from "sonner"

import { apiClient } from "@/lib/api/client"
import type { 
  Equipment, 
  EquipmentAlert, 
  EquipmentFilters,
  ElectricalMeasurement,
  MaintenanceInfo,
  EquipmentActions
} from "@/components/organisms/EquipmentDashboard/EquipmentDashboardTypes"

// Query Keys
export const equipmentQueryKeys = {
  all: ["equipment"] as const,
  lists: () => [...equipmentQueryKeys.all, "list"] as const,
  list: (filters: EquipmentFilters) => [...equipmentQueryKeys.lists(), { filters }] as const,
  details: () => [...equipmentQueryKeys.all, "detail"] as const,
  detail: (id: string) => [...equipmentQueryKeys.details(), id] as const,
  measurements: () => [...equipmentQueryKeys.all, "measurements"] as const,
  measurement: (id: string, timeRange?: string) => [...equipmentQueryKeys.measurements(), id, timeRange] as const,
  alerts: () => [...equipmentQueryKeys.all, "alerts"] as const,
  alertsList: (filters?: Partial<EquipmentAlert>) => [...equipmentQueryKeys.alerts(), { filters }] as const,
  health: () => [...equipmentQueryKeys.all, "health"] as const,
  healthSummary: () => [...equipmentQueryKeys.health(), "summary"] as const,
  maintenance: () => [...equipmentQueryKeys.all, "maintenance"] as const,
  maintenanceSchedule: (equipmentId?: string) => [...equipmentQueryKeys.maintenance(), equipmentId] as const,
} as const

// API Response Types
interface EquipmentListResponse {
  equipment: ReadonlyArray<Equipment>
  total: number
  page: number
  limit: number
  hasMore: boolean
}

interface EquipmentMeasurementsResponse {
  equipmentId: string
  measurements: ReadonlyArray<ElectricalMeasurement>
  timeRange: string
  interval: string
}

interface EquipmentAlertsResponse {
  alerts: ReadonlyArray<EquipmentAlert>
  total: number
  unacknowledged: number
}

interface EquipmentHealthSummary {
  total: number
  healthy: number
  warning: number
  critical: number
  offline: number
  averageHealth: number
}

// Equipment List Hook
export function useEquipmentList(
  filters: EquipmentFilters = {},
  options: {
    enabled?: boolean
    refetchInterval?: number
    staleTime?: number
  } = {}
) {
  const {
    enabled = true,
    refetchInterval = 30000, // 30 seconds
    staleTime = 10000, // 10 seconds
  } = options

  return useQuery({
    queryKey: equipmentQueryKeys.list(filters),
    queryFn: async (): Promise<EquipmentListResponse> => {
      const params = new URLSearchParams()
      
      // Add filter parameters
      if (filters.status?.length) {
        params.append("status", filters.status.join(","))
      }
      if (filters.type?.length) {
        params.append("type", filters.type.join(","))
      }
      if (filters.priority?.length) {
        params.append("priority", filters.priority.join(","))
      }
      if (filters.voltageClass?.length) {
        params.append("voltage_class", filters.voltageClass.join(","))
      }
      if (filters.location) {
        params.append("location", filters.location)
      }
      if (filters.healthStatus?.length) {
        params.append("health_status", filters.healthStatus.join(","))
      }
      if (filters.isOnline !== undefined) {
        params.append("is_online", filters.isOnline.toString())
      }
      if (filters.maintenanceDue !== undefined) {
        params.append("maintenance_due", filters.maintenanceDue.toString())
      }
      if (filters.searchQuery) {
        params.append("q", filters.searchQuery)
      }
      
      const response = await apiClient.get(`/equipment?${params.toString()}`)
      return response.data
    },
    enabled,
    refetchInterval,
    staleTime,
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
  })
}

// Equipment Detail Hook
export function useEquipmentDetail(
  equipmentId: string | null,
  options: {
    enabled?: boolean
    refetchInterval?: number
  } = {}
) {
  const {
    enabled = true,
    refetchInterval = 15000, // 15 seconds for detailed monitoring
  } = options

  return useQuery({
    queryKey: equipmentQueryKeys.detail(equipmentId || ""),
    queryFn: async (): Promise<Equipment> => {
      if (!equipmentId) throw new Error("Equipment ID is required")
      
      const response = await apiClient.get(`/equipment/${equipmentId}`)
      return response.data
    },
    enabled: enabled && !!equipmentId,
    refetchInterval,
    staleTime: 5000, // 5 seconds for real-time data
  })
}

// Equipment Measurements Hook
export function useEquipmentMeasurements(
  equipmentId: string | null,
  timeRange: string = "1h",
  options: {
    enabled?: boolean
    refetchInterval?: number
  } = {}
) {
  const {
    enabled = true,
    refetchInterval = 10000, // 10 seconds for measurements
  } = options

  return useQuery({
    queryKey: equipmentQueryKeys.measurement(equipmentId || "", timeRange),
    queryFn: async (): Promise<EquipmentMeasurementsResponse> => {
      if (!equipmentId) throw new Error("Equipment ID is required")
      
      const response = await apiClient.get(`/equipment/${equipmentId}/measurements?timeRange=${timeRange}`)
      return response.data
    },
    enabled: enabled && !!equipmentId,
    refetchInterval,
    staleTime: 5000,
  })
}

// Equipment Alerts Hook
export function useEquipmentAlerts(
  filters: Partial<EquipmentAlert> = {},
  options: {
    enabled?: boolean
    refetchInterval?: number
  } = {}
) {
  const {
    enabled = true,
    refetchInterval = 30000, // 30 seconds
  } = options

  return useQuery({
    queryKey: equipmentQueryKeys.alertsList(filters),
    queryFn: async (): Promise<EquipmentAlertsResponse> => {
      const params = new URLSearchParams()
      
      if (filters.severity) {
        params.append("severity", filters.severity)
      }
      if (filters.type) {
        params.append("type", filters.type)
      }
      if (filters.acknowledged !== undefined) {
        params.append("acknowledged", filters.acknowledged.toString())
      }
      
      const response = await apiClient.get(`/equipment/alerts?${params.toString()}`)
      return response.data
    },
    enabled,
    refetchInterval,
    staleTime: 15000,
  })
}

// Equipment Health Summary Hook
export function useEquipmentHealthSummary(
  options: {
    enabled?: boolean
    refetchInterval?: number
  } = {}
) {
  const {
    enabled = true,
    refetchInterval = 60000, // 1 minute
  } = options

  return useQuery({
    queryKey: equipmentQueryKeys.healthSummary(),
    queryFn: async (): Promise<EquipmentHealthSummary> => {
      const response = await apiClient.get("/equipment/health/summary")
      return response.data
    },
    enabled,
    refetchInterval,
    staleTime: 30000,
  })
}

// Equipment Action Mutation
export function useEquipmentAction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (params: {
      equipmentId: string
      action: EquipmentActions
      parameters?: Record<string, unknown>
    }) => {
      const response = await apiClient.post(
        `/equipment/${params.equipmentId}/actions/${params.action}`,
        { parameters: params.parameters }
      )
      return response.data
    },
    onSuccess: (data, variables) => {
      // Optimistically update equipment status
      queryClient.setQueryData(
        equipmentQueryKeys.detail(variables.equipmentId),
        (oldData: Equipment | undefined) => {
          if (!oldData) return oldData
          
          // Update status based on action
          let newStatus = oldData.status
          switch (variables.action) {
            case "start":
              newStatus = "operational"
              break
            case "stop":
              newStatus = "deenergized"
              break
            case "reset":
              newStatus = "operational"
              break
            case "test":
              newStatus = "testing"
              break
            case "maintenance":
              newStatus = "maintenance"
              break
            case "isolate":
              newStatus = "deenergized"
              break
            case "energize":
              newStatus = "energized"
              break
          }
          
          return {
            ...oldData,
            status: newStatus,
            lastUpdate: new Date(),
          }
        }
      )
      
      // Invalidate related queries to refetch fresh data
      queryClient.invalidateQueries({ queryKey: equipmentQueryKeys.lists() })
      queryClient.invalidateQueries({ queryKey: equipmentQueryKeys.detail(variables.equipmentId) })
      
      toast.success(`Equipment action "${variables.action}" completed successfully`)
    },
    onError: (error, variables) => {
      console.error(`Equipment action failed:`, error)
      toast.error(`Failed to execute "${variables.action}" on equipment`)
      
      // Invalidate queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: equipmentQueryKeys.detail(variables.equipmentId) })
    },
  })
}

// Alert Acknowledgment Mutation
export function useAcknowledgeAlert() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (alertId: string) => {
      const response = await apiClient.patch(`/equipment/alerts/${alertId}/acknowledge`)
      return response.data
    },
    onSuccess: (data, alertId) => {
      // Update alerts cache
      queryClient.invalidateQueries({ queryKey: equipmentQueryKeys.alerts() })
      toast.success("Alert acknowledged")
    },
    onError: (error) => {
      console.error("Failed to acknowledge alert:", error)
      toast.error("Failed to acknowledge alert")
    },
  })
}

// Bulk Equipment Actions Mutation
export function useBulkEquipmentAction() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (params: {
      equipmentIds: ReadonlyArray<string>
      action: EquipmentActions
      parameters?: Record<string, unknown>
    }) => {
      const response = await apiClient.post("/equipment/bulk-actions", {
        equipmentIds: params.equipmentIds,
        action: params.action,
        parameters: params.parameters,
      })
      return response.data
    },
    onSuccess: (data, variables) => {
      // Invalidate all equipment queries to ensure fresh data
      queryClient.invalidateQueries({ queryKey: equipmentQueryKeys.all })
      
      toast.success(
        `Bulk action "${variables.action}" completed on ${variables.equipmentIds.length} equipment items`
      )
    },
    onError: (error, variables) => {
      console.error("Bulk equipment action failed:", error)
      toast.error(`Failed to execute bulk action "${variables.action}"`)
    },
  })
}

// Maintenance Schedule Hook
export function useMaintenanceSchedule(
  equipmentId?: string,
  options: {
    enabled?: boolean
    refetchInterval?: number
  } = {}
) {
  const {
    enabled = true,
    refetchInterval = 300000, // 5 minutes
  } = options

  return useQuery({
    queryKey: equipmentQueryKeys.maintenanceSchedule(equipmentId),
    queryFn: async (): Promise<ReadonlyArray<MaintenanceInfo>> => {
      const url = equipmentId
        ? `/equipment/${equipmentId}/maintenance/schedule`
        : "/equipment/maintenance/schedule"
      
      const response = await apiClient.get(url)
      return response.data
    },
    enabled,
    refetchInterval,
    staleTime: 60000, // 1 minute
  })
}

// Real-time Equipment Data Hook using WebSocket
export function useRealTimeEquipmentData(equipmentId: string | null, enabled: boolean = true) {
  const queryClient = useQueryClient()

  // This would integrate with WebSocket for real-time updates
  // For now, we'll use polling with shorter intervals
  return useQuery({
    queryKey: [...equipmentQueryKeys.detail(equipmentId || ""), "realtime"],
    queryFn: async (): Promise<ElectricalMeasurement> => {
      if (!equipmentId) throw new Error("Equipment ID is required")
      
      const response = await apiClient.get(`/equipment/${equipmentId}/realtime`)
      return response.data
    },
    enabled: enabled && !!equipmentId,
    refetchInterval: 5000, // 5 seconds for real-time data
    staleTime: 1000, // Very fresh data
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,
    onSuccess: (newMeasurement) => {
      // Update the equipment detail cache with new measurement
      queryClient.setQueryData(
        equipmentQueryKeys.detail(equipmentId || ""),
        (oldData: Equipment | undefined) => {
          if (!oldData) return oldData
          
          return {
            ...oldData,
            measurements: newMeasurement,
            lastUpdate: new Date(),
          }
        }
      )
    },
  })
}