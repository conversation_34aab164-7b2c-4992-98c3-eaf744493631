/**
 * React Query hooks for Project Phase operations
 * 
 * Provides type-safe hooks for project lifecycle management with IEEE/IEC compliance,
 * milestone tracking, and template management.
 * 
 * Features:
 * - Project phase CRUD operations
 * - Milestone management within phases
 * - Project template application
 * - Real-time collaboration support
 * - Optimistic updates for better UX
 */

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import type { 
  ProjectPhaseCreate,
  ProjectPhaseUpdate,
  ProjectPhaseRead,
  ProjectMilestoneCreate,
  ProjectMilestoneUpdate,
  ProjectMilestoneRead,
  ProjectTemplateRead,
  ProjectNavigation,
} from "@/types/projectPhase"
import type { ListQueryParams } from "@/types/api"
import { apiClient } from "@/lib/api/client"
import { toast } from "sonner"

// Query keys
export const projectPhaseKeys = {
  all: ['project-phases'] as const,
  phases: (projectId: number) => [...projectPhaseKeys.all, 'phases', projectId] as const,
  phase: (phaseId: string) => [...projectPhaseKeys.all, 'phase', phaseId] as const,
  milestones: (phaseId: number) => [...projectPhaseKeys.all, 'milestones', phaseId] as const,
  milestone: (milestoneId: string) => [...projectPhaseKeys.all, 'milestone', milestoneId] as const,
  navigation: (projectId: number) => [...projectPhaseKeys.all, 'navigation', projectId] as const,
  templates: () => [...projectPhaseKeys.all, 'templates'] as const,
}

// Project Phase Hooks
export function useProjectPhases(projectId: number, activeOnly: boolean = false) {
  return useQuery({
    queryKey: projectPhaseKeys.phases(projectId),
    queryFn: async () => {
      const response = await apiClient.get(`/projects/${projectId}/phases`, {
        params: { active_only: activeOnly }
      })
      return response.data as ProjectPhaseRead[]
    },
    enabled: !!projectId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute for real-time updates
  })
}

export function useProjectPhase(phaseId: string) {
  return useQuery({
    queryKey: projectPhaseKeys.phase(phaseId),
    queryFn: async () => {
      const response = await apiClient.get(`/project-phases/${phaseId}`)
      return response.data as ProjectPhaseRead
    },
    enabled: !!phaseId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function useCreateProjectPhase() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ projectId, phaseData }: { 
      projectId: number
      phaseData: ProjectPhaseCreate 
    }) => {
      const response = await apiClient.post(`/projects/${projectId}/phases`, phaseData)
      return response.data as ProjectPhaseRead
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch project phases
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.phases(variables.projectId) })
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.navigation(variables.projectId) })
      
      toast.success(`Phase "${data.name}" created successfully`)
    },
    onError: (error: any) => {
      toast.error(`Failed to create phase: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

export function useUpdateProjectPhase() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      projectId, 
      phaseId, 
      phaseData 
    }: { 
      projectId: number
      phaseId: string
      phaseData: ProjectPhaseUpdate 
    }) => {
      const response = await apiClient.put(`/projects/${projectId}/phases/${phaseId}`, phaseData)
      return response.data as ProjectPhaseRead
    },
    onMutate: async ({ projectId, phaseId, phaseData }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: projectPhaseKeys.phase(phaseId) })
      
      // Snapshot the previous value
      const previousPhase = queryClient.getQueryData(projectPhaseKeys.phase(phaseId))
      
      // Optimistically update to the new value
      queryClient.setQueryData(projectPhaseKeys.phase(phaseId), (old: ProjectPhaseRead | undefined) => 
        old ? { ...old, ...phaseData, updated_at: new Date().toISOString() } : old
      )
      
      // Return a context object with the snapshotted value
      return { previousPhase, projectId, phaseId }
    },
    onError: (error, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousPhase) {
        queryClient.setQueryData(projectPhaseKeys.phase(context.phaseId), context.previousPhase)
      }
      toast.error(`Failed to update phase: ${error?.response?.data?.detail || error.message}`)
    },
    onSuccess: (data, variables, context) => {
      // Update the specific phase query
      queryClient.setQueryData(projectPhaseKeys.phase(variables.phaseId), data)
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.phases(variables.projectId) })
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.navigation(variables.projectId) })
      
      toast.success(`Phase "${data.name}" updated successfully`)
    },
    onSettled: (data, error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.phase(variables.phaseId) })
    },
  })
}

export function useDeleteProjectPhase() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ projectId, phaseId }: { projectId: number; phaseId: string }) => {
      await apiClient.delete(`/projects/${projectId}/phases/${phaseId}`)
    },
    onSuccess: (data, variables) => {
      // Remove from cache and invalidate lists
      queryClient.removeQueries({ queryKey: projectPhaseKeys.phase(variables.phaseId) })
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.phases(variables.projectId) })
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.navigation(variables.projectId) })
      
      toast.success("Phase deleted successfully")
    },
    onError: (error: any) => {
      toast.error(`Failed to delete phase: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

// Project Milestone Hooks
export function useProjectMilestones(phaseId: number) {
  return useQuery({
    queryKey: projectPhaseKeys.milestones(phaseId),
    queryFn: async () => {
      const response = await apiClient.get(`/project-phases/${phaseId}/milestones`)
      return response.data as ProjectMilestoneRead[]
    },
    enabled: !!phaseId,
    staleTime: 30 * 1000, // 30 seconds
  })
}

export function useCreateProjectMilestone() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      projectId, 
      phaseId, 
      milestoneData 
    }: { 
      projectId: number
      phaseId: number
      milestoneData: ProjectMilestoneCreate 
    }) => {
      const response = await apiClient.post(
        `/projects/${projectId}/phases/${phaseId}/milestones`, 
        milestoneData
      )
      return response.data as ProjectMilestoneRead
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch milestones
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.milestones(variables.phaseId) })
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.navigation(variables.projectId) })
      
      toast.success(`Milestone "${data.title}" created successfully`)
    },
    onError: (error: any) => {
      toast.error(`Failed to create milestone: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

export function useUpdateProjectMilestone() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      milestoneId, 
      milestoneData 
    }: { 
      milestoneId: string
      milestoneData: ProjectMilestoneUpdate 
    }) => {
      const response = await apiClient.put(`/project-milestones/${milestoneId}`, milestoneData)
      return response.data as ProjectMilestoneRead
    },
    onSuccess: (data, variables) => {
      // Update milestone cache
      queryClient.setQueryData(projectPhaseKeys.milestone(variables.milestoneId), data)
      
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.milestones(data.phase_id) })
      
      toast.success(`Milestone "${data.title}" updated successfully`)
    },
    onError: (error: any) => {
      toast.error(`Failed to update milestone: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

// Project Template Hooks
export function useProjectTemplates(params?: ListQueryParams) {
  return useQuery({
    queryKey: [...projectPhaseKeys.templates(), params],
    queryFn: async () => {
      const response = await apiClient.get('/project-templates', { params })
      return response.data as ProjectTemplateRead[]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useApplyProjectTemplate() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: async ({ 
      projectId, 
      templateId 
    }: { 
      projectId: number
      templateId: string 
    }) => {
      const response = await apiClient.post(`/projects/${projectId}/apply-template`, {
        template_id: templateId
      })
      return response.data as ProjectPhaseRead[]
    },
    onSuccess: (data, variables) => {
      // Invalidate project-related queries
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.phases(variables.projectId) })
      queryClient.invalidateQueries({ queryKey: projectPhaseKeys.navigation(variables.projectId) })
      
      toast.success(`Template applied successfully. Created ${data.length} phases.`)
    },
    onError: (error: any) => {
      toast.error(`Failed to apply template: ${error?.response?.data?.detail || error.message}`)
    },
  })
}

// Project Navigation Hook (for ProjectNavigation organism)
export function useProjectNavigation(projectId: number) {
  return useQuery({
    queryKey: projectPhaseKeys.navigation(projectId),
    queryFn: async () => {
      const response = await apiClient.get(`/projects/${projectId}/navigation`)
      return response.data as ProjectNavigation
    },
    enabled: !!projectId,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // 1 minute for real-time collaboration
  })
}

// Utility hooks for phase management
export function usePhaseProgress(projectId: number) {
  const { data: phases } = useProjectPhases(projectId)
  
  return {
    totalPhases: phases?.length || 0,
    completedPhases: phases?.filter(phase => phase.progress_percentage === 100).length || 0,
    activePhase: phases?.find(phase => phase.is_active),
    overallProgress: phases?.length 
      ? Math.round(phases.reduce((sum, phase) => sum + phase.progress_percentage, 0) / phases.length)
      : 0
  }
}

export function useUpcomingMilestones(projectId: number, days: number = 30) {
  const { data: navigation } = useProjectNavigation(projectId)
  
  const upcomingDeadline = new Date()
  upcomingDeadline.setDate(upcomingDeadline.getDate() + days)
  
  return navigation?.upcoming_milestones?.filter(
    milestone => new Date(milestone.due_date) <= upcomingDeadline && 
                milestone.status !== "COMPLETED" && 
                milestone.status !== "CANCELLED"
  ) || []
}