/**
 * Project Phase, Milestone, and Template Types
 * 
 * TypeScript type definitions for project lifecycle management
 * with IEEE/IEC electrical engineering standards compliance.
 * 
 * Features:
 * - Project phase management with electrical engineering workflows
 * - Milestone tracking with dependencies and completion criteria
 * - Project templates for standardized electrical system types
 * - Real-time collaboration support
 * - IEEE/IEC standards compliance validation
 */

import { z } from "zod"
import { 
  BaseSchema, 
  CreatePaginatedResponseSchema, 
  TimestampMixinSchema 
} from "./schemas/baseSchemas"

// Enums matching backend
export const ProjectPhaseTypeSchema = z.enum([
  "CONCEPTUAL_DESIGN",
  "SCHEMATIC_DESIGN", 
  "DESIGN_DEVELOPMENT",
  "CONSTRUCTION_DOCUMENTS",
  "BIDDING_NEGOTIATION",
  "CONSTRUCTION_ADMINISTRATION",
  "TESTING_COMMISSIONING",
  "CLOSEOUT",
  "PLANNING",
  "PROCUREMENT", 
  "INSTALLATION",
  "VERIFICATION",
])

export const MilestoneStatusSchema = z.enum([
  "NOT_STARTED",
  "PLANNED",
  "IN_PROGRESS",
  "ON_TRACK",
  "AT_RISK",
  "DELAYED",
  "COMPLETED",
  "CANCELLED",
])

export const ProjectTemplateTypeSchema = z.enum([
  "INDUSTRIAL_POWER_DISTRIBUTION",
  "MOTOR_CONTROL_SYSTEMS",
  "LIGHTING_SYSTEMS",
  "UPS_BACKUP_POWER",
  "HEAT_TRACING_SYSTEMS",
  "BUILDING_ELECTRICAL",
  "SWITCHGEAR_DESIGN",
  "PANEL_BOARD_DESIGN",
  "INSTRUMENTATION_POWER",
  "HAZARDOUS_LOCATION",
])

// Project Phase Schemas
export const ProjectPhaseBaseSchema = BaseSchema.extend({
  phase_type: ProjectPhaseTypeSchema.describe("Type of project phase"),
  start_date: z.coerce.date().describe("Phase start date"),
  end_date: z.coerce.date().optional().nullable().describe("Phase end date"),
  progress_percentage: z.number().int().min(0).max(100).default(0).describe("Completion percentage"),
  is_active: z.boolean().default(false).describe("Whether phase is currently active"),
  prerequisites: z.array(z.string().uuid()).default([]).describe("List of prerequisite phase IDs"),
  deliverables: z.array(z.record(z.any())).default([]).describe("Expected deliverables"),
  notes: z.string().max(2000).optional().nullable().describe("Phase notes"),
}).refine((data) => {
  if (data.end_date && data.start_date >= data.end_date) {
    return false
  }
  return true
}, {
  message: "End date must be after start date",
  path: ["end_date"]
})

export const ProjectPhaseCreateSchema = ProjectPhaseBaseSchema.extend({
  project_id: z.number().int().describe("Project ID"),
})

export const ProjectPhaseUpdateSchema = z.object({
  name: z.string().min(1).max(255).optional().describe("Phase name"),
  start_date: z.coerce.date().optional().describe("Phase start date"),
  end_date: z.coerce.date().optional().nullable().describe("Phase end date"),
  progress_percentage: z.number().int().min(0).max(100).optional().describe("Completion percentage"),
  is_active: z.boolean().optional().describe("Whether phase is currently active"),
  prerequisites: z.array(z.string().uuid()).optional().describe("List of prerequisite phase IDs"),
  deliverables: z.array(z.record(z.any())).optional().describe("Expected deliverables"),
  notes: z.string().max(2000).optional().nullable().describe("Phase notes"),
})

export const ProjectPhaseReadSchema = ProjectPhaseBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().describe("Phase database ID"),
  phase_id: z.string().uuid().describe("Phase UUID"),
  project_id: z.number().int().describe("Project ID"),
})

// Project Milestone Schemas  
export const ProjectMilestoneBaseSchema = BaseSchema.extend({
  title: z.string().min(1).max(255).describe("Milestone title"),
  description: z.string().max(2000).optional().nullable().describe("Milestone description"),
  due_date: z.coerce.date().describe("Milestone due date"),
  completion_date: z.coerce.date().optional().nullable().describe("Actual completion date"),
  status: MilestoneStatusSchema.default("NOT_STARTED").describe("Milestone status"),
  assigned_user_id: z.number().int().optional().nullable().describe("Assigned user ID"),
  dependencies: z.array(z.string().uuid()).default([]).describe("Dependent milestone IDs"),
  completion_criteria: z.array(z.record(z.any())).default([]).describe("Completion criteria"),
})

export const ProjectMilestoneCreateSchema = ProjectMilestoneBaseSchema.extend({
  phase_id: z.number().int().describe("Phase ID"),
})

export const ProjectMilestoneUpdateSchema = z.object({
  title: z.string().min(1).max(255).optional().describe("Milestone title"),
  description: z.string().max(2000).optional().nullable().describe("Milestone description"),
  due_date: z.coerce.date().optional().describe("Milestone due date"),
  completion_date: z.coerce.date().optional().nullable().describe("Actual completion date"),
  status: MilestoneStatusSchema.optional().describe("Milestone status"),
  assigned_user_id: z.number().int().optional().nullable().describe("Assigned user ID"),
  dependencies: z.array(z.string().uuid()).optional().describe("Dependent milestone IDs"),
  completion_criteria: z.array(z.record(z.any())).optional().describe("Completion criteria"),
})

export const ProjectMilestoneReadSchema = ProjectMilestoneBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().describe("Milestone database ID"),
  milestone_id: z.string().uuid().describe("Milestone UUID"),
  phase_id: z.number().int().describe("Phase ID"),
})

// Project Template Schemas
export const ProjectTemplateBaseSchema = BaseSchema.extend({
  template_type: ProjectTemplateTypeSchema.describe("Template type"),
  category: z.string().min(1).max(100).describe("Template category"),
  description: z.string().max(2000).optional().nullable().describe("Template description"),
  phases_config: z.array(z.record(z.any())).default([]).describe("Phase configuration"),
  milestones_config: z.array(z.record(z.any())).default([]).describe("Milestone configuration"),
  default_settings: z.record(z.any()).default({}).describe("Default project settings"),
  compliance_standards: z.array(z.string()).default([]).describe("Applicable standards"),
  is_public: z.boolean().default(false).describe("Whether template is public"),
})

export const ProjectTemplateCreateSchema = ProjectTemplateBaseSchema.extend({
  created_by_user_id: z.number().int().optional().nullable().describe("Creator user ID"),
})

export const ProjectTemplateUpdateSchema = z.object({
  name: z.string().min(1).max(255).optional().describe("Template name"),
  description: z.string().max(2000).optional().nullable().describe("Template description"),
  phases_config: z.array(z.record(z.any())).optional().describe("Phase configuration"),
  milestones_config: z.array(z.record(z.any())).optional().describe("Milestone configuration"),
  default_settings: z.record(z.any()).optional().describe("Default project settings"),
  compliance_standards: z.array(z.string()).optional().describe("Applicable standards"),
  is_public: z.boolean().optional().describe("Whether template is public"),
})

export const ProjectTemplateReadSchema = ProjectTemplateBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().describe("Template database ID"),
  template_id: z.string().uuid().describe("Template UUID"),
  created_by_user_id: z.number().int().optional().nullable().describe("Creator user ID"),
})

// Navigation-specific schemas for the frontend organism
export const ProjectNavigationPhaseSchema = z.object({
  id: z.number().int().describe("Phase ID"),
  phase_id: z.string().uuid().describe("Phase UUID"),
  name: z.string().describe("Phase name"),
  phase_type: ProjectPhaseTypeSchema.describe("Phase type"),
  progress_percentage: z.number().int().describe("Progress percentage"),
  is_active: z.boolean().describe("Is currently active"),
  start_date: z.coerce.date().describe("Start date"),
  end_date: z.coerce.date().optional().nullable().describe("End date"),
})

export const ProjectNavigationMilestoneSchema = z.object({
  id: z.number().int().describe("Milestone ID"),
  milestone_id: z.string().uuid().describe("Milestone UUID"),
  title: z.string().describe("Milestone title"),
  status: MilestoneStatusSchema.describe("Status"),
  due_date: z.coerce.date().describe("Due date"),
  completion_date: z.coerce.date().optional().nullable().describe("Completion date"),
  assigned_user_id: z.number().int().optional().nullable().describe("Assigned user"),
})

export const ProjectNavigationSchema = z.object({
  project: z.record(z.any()).describe("Project summary data"),
  phases: z.array(ProjectNavigationPhaseSchema).default([]).describe("Project phases"),
  active_phase: ProjectNavigationPhaseSchema.optional().nullable().describe("Current active phase"),
  upcoming_milestones: z.array(ProjectNavigationMilestoneSchema).default([]).describe("Upcoming milestones"),
  recent_activity: z.array(z.record(z.any())).default([]).describe("Recent project activity"),
  collaboration_status: z.record(z.any()).default({}).describe("Real-time collaboration status"),
})

// Response schemas
export const ProjectPhaseListResponseSchema = CreatePaginatedResponseSchema(ProjectPhaseReadSchema)
export const ProjectMilestoneListResponseSchema = CreatePaginatedResponseSchema(ProjectMilestoneReadSchema)
export const ProjectTemplateListResponseSchema = CreatePaginatedResponseSchema(ProjectTemplateReadSchema)

// Type definitions
export type ProjectPhaseType = z.infer<typeof ProjectPhaseTypeSchema>
export type MilestoneStatus = z.infer<typeof MilestoneStatusSchema>
export type ProjectTemplateType = z.infer<typeof ProjectTemplateTypeSchema>

export type ProjectPhase = z.infer<typeof ProjectPhaseBaseSchema>
export type ProjectPhaseCreate = z.infer<typeof ProjectPhaseCreateSchema>
export type ProjectPhaseUpdate = z.infer<typeof ProjectPhaseUpdateSchema>
export type ProjectPhaseRead = z.infer<typeof ProjectPhaseReadSchema>

export type ProjectMilestone = z.infer<typeof ProjectMilestoneBaseSchema>
export type ProjectMilestoneCreate = z.infer<typeof ProjectMilestoneCreateSchema>
export type ProjectMilestoneUpdate = z.infer<typeof ProjectMilestoneUpdateSchema>
export type ProjectMilestoneRead = z.infer<typeof ProjectMilestoneReadSchema>

export type ProjectTemplate = z.infer<typeof ProjectTemplateBaseSchema>
export type ProjectTemplateCreate = z.infer<typeof ProjectTemplateCreateSchema>
export type ProjectTemplateUpdate = z.infer<typeof ProjectTemplateUpdateSchema>
export type ProjectTemplateRead = z.infer<typeof ProjectTemplateReadSchema>

export type ProjectNavigationPhase = z.infer<typeof ProjectNavigationPhaseSchema>
export type ProjectNavigationMilestone = z.infer<typeof ProjectNavigationMilestoneSchema>
export type ProjectNavigation = z.infer<typeof ProjectNavigationSchema>

export type ProjectPhaseListResponse = z.infer<typeof ProjectPhaseListResponseSchema>
export type ProjectMilestoneListResponse = z.infer<typeof ProjectMilestoneListResponseSchema>
export type ProjectTemplateListResponse = z.infer<typeof ProjectTemplateListResponseSchema>

// Status mapping utilities
export const PROJECT_PHASE_TYPE_LABELS: Record<ProjectPhaseType, string> = {
  CONCEPTUAL_DESIGN: "Conceptual Design",
  SCHEMATIC_DESIGN: "Schematic Design", 
  DESIGN_DEVELOPMENT: "Design Development",
  CONSTRUCTION_DOCUMENTS: "Construction Documents",
  BIDDING_NEGOTIATION: "Bidding/Negotiation",
  CONSTRUCTION_ADMINISTRATION: "Construction Administration",
  TESTING_COMMISSIONING: "Testing & Commissioning",
  CLOSEOUT: "Project Closeout",
  PLANNING: "Planning",
  PROCUREMENT: "Procurement",
  INSTALLATION: "Installation",
  VERIFICATION: "Verification",
}

export const MILESTONE_STATUS_COLORS: Record<MilestoneStatus, string> = {
  NOT_STARTED: "text-gray-500",
  PLANNED: "text-blue-500",
  IN_PROGRESS: "text-orange-500",
  ON_TRACK: "text-green-500",
  AT_RISK: "text-yellow-500",
  DELAYED: "text-red-500",
  COMPLETED: "text-green-600",
  CANCELLED: "text-gray-400",
}

export const PROJECT_TEMPLATE_TYPE_LABELS: Record<ProjectTemplateType, string> = {
  INDUSTRIAL_POWER_DISTRIBUTION: "Industrial Power Distribution",
  MOTOR_CONTROL_SYSTEMS: "Motor Control Systems",
  LIGHTING_SYSTEMS: "Lighting Systems",
  UPS_BACKUP_POWER: "UPS and Backup Power",
  HEAT_TRACING_SYSTEMS: "Heat Tracing Systems", 
  BUILDING_ELECTRICAL: "Building Electrical Systems",
  SWITCHGEAR_DESIGN: "Switchgear Design",
  PANEL_BOARD_DESIGN: "Panel Board Design",
  INSTRUMENTATION_POWER: "Instrumentation Power",
  HAZARDOUS_LOCATION: "Hazardous Location Systems",
}