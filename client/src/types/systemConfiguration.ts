/**
 * System Configuration Types
 * 
 * TypeScript type definitions for electrical system configuration management
 * with IEEE/IEC standards compliance and user preference management.
 * 
 * Features:
 * - Global and project-specific configuration settings
 * - Electrical standards (IEC, IEEE, EN) compliance management  
 * - User preference customization
 * - Configuration templates for different regions and standards
 * - Validation and compliance checking
 */

import { z } from "zod"
import { 
  BaseSchema, 
  CreatePaginatedResponseSchema, 
  TimestampMixinSchema 
} from "./schemas/baseSchemas"

// Enums matching backend
export const TemperatureUnitSchema = z.enum(["CELSIUS", "FAHRENHEIT", "KELVIN"])

export const FrequencyTypeSchema = z.enum(["HZ_50", "HZ_60", "DC"])

export const VoltageClassSchema = z.enum(["LV", "MV", "HV", "EHV"])

export const ElectricalStandardTypeSchema = z.enum([
  "IEC",
  "IEEE", 
  "EN",
  "NEMA",
  "NEC",
  "CEC",
  "AS_NZS",
  "JIS",
  "GB",
  "ANSI",
  "UL",
  "CSA",
])

export const ComplianceLevelSchema = z.enum([
  "COMPLIANT",
  "NON_COMPLIANT",
  "WARNING",
  "NOT_APPLICABLE",
  "UNKNOWN",
  "PENDING_REVIEW",
])

// System Configuration Schemas
export const SystemConfigurationBaseSchema = BaseSchema.extend({
  scope: z.string().default("global").describe("Configuration scope (global, project, user)"),
  category: z.string().min(1).max(100).describe("Configuration category"),
  voltage_system: z.string().optional().nullable().describe("Default voltage system"),
  frequency_system: FrequencyTypeSchema.optional().nullable().describe("Default frequency"),
  temperature_unit: TemperatureUnitSchema.default("CELSIUS").describe("Temperature unit"),
  calculation_precision: z.number().int().min(0).max(10).default(2).describe("Calculation decimal places"),
  rounding_method: z.string().default("round_half_up").describe("Rounding method"),
  safety_factors: z.record(z.number().positive()).default({}).describe("Safety factors by equipment type"),
  environmental_conditions: z.record(z.any()).default({}).describe("Environmental parameters"),
  standards_compliance: z.array(z.string()).default([]).describe("Applicable standards"),
  validation_rules: z.record(z.any()).default({}).describe("Validation rule configuration"),
  is_active: z.boolean().default(true).describe("Whether configuration is active"),
}).refine((data) => {
  const validScopes = ["global", "project", "user"]
  return validScopes.includes(data.scope)
}, {
  message: "Scope must be one of: global, project, user",
  path: ["scope"]
})

export const SystemConfigurationCreateSchema = SystemConfigurationBaseSchema.extend({
  project_id: z.number().int().optional().nullable().describe("Project ID for project-specific config"),
})

export const SystemConfigurationUpdateSchema = z.object({
  name: z.string().min(1).max(255).optional().describe("Configuration name"),
  voltage_system: z.string().optional().nullable().describe("Default voltage system"),
  frequency_system: FrequencyTypeSchema.optional().nullable().describe("Default frequency"),
  temperature_unit: TemperatureUnitSchema.optional().nullable().describe("Temperature unit"),
  calculation_precision: z.number().int().min(0).max(10).optional().describe("Calculation decimal places"),
  rounding_method: z.string().optional().describe("Rounding method"),
  safety_factors: z.record(z.number().positive()).optional().describe("Safety factors"),
  environmental_conditions: z.record(z.any()).optional().describe("Environmental parameters"),
  standards_compliance: z.array(z.string()).optional().describe("Applicable standards"),
  validation_rules: z.record(z.any()).optional().describe("Validation rules"),
  is_active: z.boolean().optional().describe("Whether configuration is active"),
})

export const SystemConfigurationReadSchema = SystemConfigurationBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().describe("Configuration database ID"),
  config_id: z.string().uuid().describe("Configuration UUID"),
  project_id: z.number().int().optional().nullable().describe("Project ID if project-specific"),
})

// Electrical Standard Schemas
export const ElectricalStandardBaseSchema = BaseSchema.extend({
  standard_type: ElectricalStandardTypeSchema.describe("Standard type"),
  standard_number: z.string().min(1).max(50).describe("Standard number"),
  title: z.string().min(1).max(500).describe("Standard title"),
  version: z.string().max(50).optional().nullable().describe("Standard version"),
  publication_date: z.coerce.date().optional().nullable().describe("Publication date"),
  compliance_level: ComplianceLevelSchema.describe("Compliance level"),
  applicable_regions: z.array(z.string()).default([]).describe("Applicable regions"),
  scope_description: z.string().max(2000).optional().nullable().describe("Scope description"),
  calculation_methods: z.record(z.any()).default({}).describe("Calculation methods"),
  validation_rules: z.record(z.any()).default({}).describe("Validation rules"),
  parameter_limits: z.record(z.any()).default({}).describe("Parameter limits"),
  is_active: z.boolean().default(true).describe("Whether standard is active"),
})

export const ElectricalStandardCreateSchema = ElectricalStandardBaseSchema

export const ElectricalStandardUpdateSchema = z.object({
  title: z.string().min(1).max(500).optional().describe("Standard title"),
  version: z.string().max(50).optional().nullable().describe("Standard version"),
  publication_date: z.coerce.date().optional().nullable().describe("Publication date"),
  compliance_level: ComplianceLevelSchema.optional().describe("Compliance level"),
  applicable_regions: z.array(z.string()).optional().describe("Applicable regions"),
  scope_description: z.string().max(2000).optional().nullable().describe("Scope description"),
  calculation_methods: z.record(z.any()).optional().describe("Calculation methods"),
  validation_rules: z.record(z.any()).optional().describe("Validation rules"),
  parameter_limits: z.record(z.any()).optional().describe("Parameter limits"),
  is_active: z.boolean().optional().describe("Whether standard is active"),
})

export const ElectricalStandardReadSchema = ElectricalStandardBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().describe("Standard database ID"),
  standard_id: z.string().uuid().describe("Standard UUID"),
})

// User Preferences Schemas
export const UserPreferencesBaseSchema = BaseSchema.extend({
  category: z.string().min(1).max(100).describe("Preference category"),
  default_units: z.record(z.string()).default({}).describe("Preferred units"),
  calculation_preferences: z.record(z.any()).default({}).describe("Calculation preferences"),
  ui_preferences: z.record(z.any()).default({}).describe("UI preferences"),
  notification_settings: z.record(z.any()).default({}).describe("Notification settings"),
  workflow_preferences: z.record(z.any()).default({}).describe("Workflow preferences"),
  is_active: z.boolean().default(true).describe("Whether preferences are active"),
})

export const UserPreferencesCreateSchema = UserPreferencesBaseSchema.extend({
  user_id: z.number().int().describe("User ID"),
})

export const UserPreferencesUpdateSchema = z.object({
  default_units: z.record(z.string()).optional().describe("Preferred units"),
  calculation_preferences: z.record(z.any()).optional().describe("Calculation preferences"),
  ui_preferences: z.record(z.any()).optional().describe("UI preferences"),
  notification_settings: z.record(z.any()).optional().describe("Notification settings"),
  workflow_preferences: z.record(z.any()).optional().describe("Workflow preferences"),
  is_active: z.boolean().optional().describe("Whether preferences are active"),
})

export const UserPreferencesReadSchema = UserPreferencesBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().describe("Preference database ID"),
  preference_id: z.string().uuid().describe("Preference UUID"),
  user_id: z.number().int().describe("User ID"),
})

// Configuration Template Schemas
export const ConfigurationTemplateBaseSchema = BaseSchema.extend({
  template_type: z.string().min(1).max(100).describe("Template type"),
  region: z.string().max(100).optional().nullable().describe("Target region"),
  standards_set: z.array(z.string()).default([]).describe("Applicable standards"),
  default_values: z.record(z.any()).default({}).describe("Default values"),
  validation_rules: z.record(z.any()).default({}).describe("Validation rules"),
  description: z.string().max(2000).optional().nullable().describe("Template description"),
  is_public: z.boolean().default(false).describe("Whether template is public"),
})

export const ConfigurationTemplateCreateSchema = ConfigurationTemplateBaseSchema.extend({
  created_by_user_id: z.number().int().optional().nullable().describe("Creator user ID"),
})

export const ConfigurationTemplateUpdateSchema = z.object({
  template_type: z.string().min(1).max(100).optional().describe("Template type"),
  region: z.string().max(100).optional().nullable().describe("Target region"),
  standards_set: z.array(z.string()).optional().describe("Applicable standards"),
  default_values: z.record(z.any()).optional().describe("Default values"),
  validation_rules: z.record(z.any()).optional().describe("Validation rules"),
  description: z.string().max(2000).optional().nullable().describe("Template description"),
  is_public: z.boolean().optional().describe("Whether template is public"),
})

export const ConfigurationTemplateReadSchema = ConfigurationTemplateBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().describe("Template database ID"),
  template_id: z.string().uuid().describe("Template UUID"),
  created_by_user_id: z.number().int().optional().nullable().describe("Creator user ID"),
})

// System Configuration Organism-specific schemas
export const SystemConfigurationSectionSchema = z.object({
  section_id: z.string().describe("Section identifier"),
  title: z.string().describe("Section title"),
  description: z.string().optional().nullable().describe("Section description"),
  settings: z.array(z.record(z.any())).default([]).describe("Section settings"),
  validation_status: z.string().default("valid").describe("Validation status"),
  compliance_standards: z.array(z.string()).default([]).describe("Applicable standards"),
})

export const SystemConfigurationOrganismSchema = z.object({
  global_configuration: z.record(z.any()).default({}).describe("Global configuration settings"),
  project_configuration: z.record(z.any()).optional().nullable().describe("Project-specific overrides"),
  user_preferences: z.record(z.any()).default({}).describe("User preference settings"),
  configuration_sections: z.array(SystemConfigurationSectionSchema).default([]).describe("Configuration sections"),
  available_templates: z.array(ConfigurationTemplateReadSchema).default([]).describe("Available templates"),
  electrical_standards: z.array(ElectricalStandardReadSchema).default([]).describe("Applicable standards"),
  validation_results: z.record(z.any()).default({}).describe("Configuration validation results"),
  compliance_status: z.record(z.string()).default({}).describe("Standards compliance status"),
})

// Configuration validation schemas
export const ConfigurationValidationSchema = z.object({
  valid: z.boolean().describe("Whether configuration is valid"),
  issues: z.array(z.object({
    field: z.string().describe("Field with issue"),
    message: z.string().describe("Issue description"),
    severity: z.enum(["error", "warning", "info"]).describe("Issue severity"),
  })).default([]).describe("Validation issues"),
  warnings: z.array(z.string()).default([]).describe("Validation warnings"),
  compliance: z.record(z.string()).default({}).describe("Standards compliance status"),
  validated_at: z.string().describe("Validation timestamp"),
  validated_by: z.number().int().optional().describe("User who validated"),
})

export const ConfigurationExportSchema = z.object({
  export_metadata: z.object({
    version: z.string().describe("Export format version"),
    exported_at: z.string().describe("Export timestamp"),
    exported_by: z.number().int().describe("User who exported"),
    scope: z.string().describe("Configuration scope"),
    project_id: z.number().int().optional().nullable().describe("Project ID if project-specific"),
  }).describe("Export metadata"),
  configuration: z.record(z.any()).describe("Configuration data"),
})

// Response schemas
export const SystemConfigurationListResponseSchema = CreatePaginatedResponseSchema(SystemConfigurationReadSchema)
export const ElectricalStandardListResponseSchema = CreatePaginatedResponseSchema(ElectricalStandardReadSchema)
export const UserPreferencesListResponseSchema = CreatePaginatedResponseSchema(UserPreferencesReadSchema)
export const ConfigurationTemplateListResponseSchema = CreatePaginatedResponseSchema(ConfigurationTemplateReadSchema)

// Type definitions
export type TemperatureUnit = z.infer<typeof TemperatureUnitSchema>
export type FrequencyType = z.infer<typeof FrequencyTypeSchema>
export type VoltageClass = z.infer<typeof VoltageClassSchema>
export type ElectricalStandardType = z.infer<typeof ElectricalStandardTypeSchema>
export type ComplianceLevel = z.infer<typeof ComplianceLevelSchema>

export type SystemConfiguration = z.infer<typeof SystemConfigurationBaseSchema>
export type SystemConfigurationCreate = z.infer<typeof SystemConfigurationCreateSchema>
export type SystemConfigurationUpdate = z.infer<typeof SystemConfigurationUpdateSchema>
export type SystemConfigurationRead = z.infer<typeof SystemConfigurationReadSchema>

export type ElectricalStandard = z.infer<typeof ElectricalStandardBaseSchema>
export type ElectricalStandardCreate = z.infer<typeof ElectricalStandardCreateSchema>
export type ElectricalStandardUpdate = z.infer<typeof ElectricalStandardUpdateSchema>
export type ElectricalStandardRead = z.infer<typeof ElectricalStandardReadSchema>

export type UserPreferences = z.infer<typeof UserPreferencesBaseSchema>
export type UserPreferencesCreate = z.infer<typeof UserPreferencesCreateSchema>
export type UserPreferencesUpdate = z.infer<typeof UserPreferencesUpdateSchema>
export type UserPreferencesRead = z.infer<typeof UserPreferencesReadSchema>

export type ConfigurationTemplate = z.infer<typeof ConfigurationTemplateBaseSchema>
export type ConfigurationTemplateCreate = z.infer<typeof ConfigurationTemplateCreateSchema>
export type ConfigurationTemplateUpdate = z.infer<typeof ConfigurationTemplateUpdateSchema>
export type ConfigurationTemplateRead = z.infer<typeof ConfigurationTemplateReadSchema>

export type SystemConfigurationSection = z.infer<typeof SystemConfigurationSectionSchema>
export type SystemConfigurationOrganism = z.infer<typeof SystemConfigurationOrganismSchema>
export type ConfigurationValidation = z.infer<typeof ConfigurationValidationSchema>
export type ConfigurationExport = z.infer<typeof ConfigurationExportSchema>

export type SystemConfigurationListResponse = z.infer<typeof SystemConfigurationListResponseSchema>
export type ElectricalStandardListResponse = z.infer<typeof ElectricalStandardListResponseSchema>
export type UserPreferencesListResponse = z.infer<typeof UserPreferencesListResponseSchema>
export type ConfigurationTemplateListResponse = z.infer<typeof ConfigurationTemplateListResponseSchema>

// Utility constants and labels
export const TEMPERATURE_UNIT_LABELS: Record<TemperatureUnit, string> = {
  CELSIUS: "°C",
  FAHRENHEIT: "°F",
  KELVIN: "K",
}

export const FREQUENCY_TYPE_LABELS: Record<FrequencyType, string> = {
  HZ_50: "50Hz",
  HZ_60: "60Hz",
  DC: "DC",
}

export const VOLTAGE_CLASS_LABELS: Record<VoltageClass, string> = {
  LV: "Low Voltage (≤1kV)",
  MV: "Medium Voltage (1-35kV)",
  HV: "High Voltage (35-100kV)",
  EHV: "Extra High Voltage (>100kV)",
}

export const ELECTRICAL_STANDARD_TYPE_LABELS: Record<ElectricalStandardType, string> = {
  IEC: "IEC",
  IEEE: "IEEE",
  EN: "EN",
  NEMA: "NEMA",
  NEC: "NEC",
  CEC: "CEC",
  AS_NZS: "AS/NZS",
  JIS: "JIS", 
  GB: "GB",
  ANSI: "ANSI",
  UL: "UL",
  CSA: "CSA",
}

export const COMPLIANCE_LEVEL_COLORS: Record<ComplianceLevel, string> = {
  COMPLIANT: "text-green-600",
  NON_COMPLIANT: "text-red-600",
  WARNING: "text-yellow-600",
  NOT_APPLICABLE: "text-gray-500",
  UNKNOWN: "text-gray-400",
  PENDING_REVIEW: "text-blue-500",
}

export const COMPLIANCE_LEVEL_LABELS: Record<ComplianceLevel, string> = {
  COMPLIANT: "Compliant",
  NON_COMPLIANT: "Non-Compliant",
  WARNING: "Warning",
  NOT_APPLICABLE: "Not Applicable",
  UNKNOWN: "Unknown",
  PENDING_REVIEW: "Pending Review",
}

// Default configuration values
export const DEFAULT_SYSTEM_CONFIGURATION: Partial<SystemConfiguration> = {
  voltage_system: "3-phase",
  frequency_system: "HZ_50",
  temperature_unit: "CELSIUS",
  calculation_precision: 2,
  rounding_method: "round_half_up",
  safety_factors: {
    motor_starting: 1.25,
    continuous_load: 1.0,
    emergency_load: 1.5,
  },
  environmental_conditions: {
    default_ambient_temp: 25.0,
    default_humidity: 60.0,
    default_altitude: 0.0,
  },
  standards_compliance: [
    "IEC 60364",
    "IEEE C2", 
    "EN 50110",
  ],
  validation_rules: {
    voltage_drop_max: 5.0,
    load_factor_max: 0.8,
    temperature_derating: true,
  },
}