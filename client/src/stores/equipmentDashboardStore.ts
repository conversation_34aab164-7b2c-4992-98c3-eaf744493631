"use client"

/**
 * Equipment Dashboard Zustand Store
 * 
 * Client state management for Equipment Dashboard organism,
 * handling UI interactions, filters, layouts, and real-time updates.
 * 
 * Features:
 * - TypeScript strict mode compliance
 * - Persistent state management
 * - Real-time state synchronization
 * - Professional electrical engineering state
 * - Performance optimized operations
 */

import { create } from "zustand"
import { createJSONStorage, persist } from "zustand/middleware"

import type { 
  Equipment,
  EquipmentFilters,
  DashboardLayout, 
  DashboardConfig,
  EquipmentAlert,
  EquipmentStatus,
  ElectricalSystemType,
  EquipmentPriority,
  VoltageClass,
  DEFAULT_DASHBOARD_LAYOUT,
  DEFAULT_DASHBOARD_CONFIG
} from "@/components/organisms/EquipmentDashboard/EquipmentDashboardTypes"

interface EquipmentDashboardState {
  // UI State
  selectedEquipmentId: string | null
  expandedEquipmentIds: ReadonlyArray<string>
  layout: DashboardLayout
  config: DashboardConfig
  filters: EquipmentFilters
  isFilterPanelOpen: boolean
  isRefreshing: boolean
  
  // Alert State
  unacknowledgedAlerts: ReadonlyArray<string>
  mutedAlerts: ReadonlyArray<string>
  lastAlertCheck: Date | null
  
  // Search and Selection
  searchQuery: string
  selectedEquipmentIds: ReadonlyArray<string>
  bulkActionMode: boolean
  
  // Real-time Data State
  lastDataUpdate: Date | null
  connectionStatus: "connected" | "disconnected" | "reconnecting"
  dataQuality: "good" | "fair" | "poor"
  
  // Actions - UI Management
  setSelectedEquipment: (equipmentId: string | null) => void
  toggleEquipmentExpanded: (equipmentId: string) => void
  setLayout: (layout: Partial<DashboardLayout>) => void
  setConfig: (config: Partial<DashboardConfig>) => void
  setFilters: (filters: Partial<EquipmentFilters>) => void
  resetFilters: () => void
  toggleFilterPanel: () => void
  setRefreshing: (refreshing: boolean) => void
  
  // Actions - Alert Management
  acknowledgeAlert: (alertId: string) => void
  muteAlert: (alertId: string) => void
  unmuteAlert: (alertId: string) => void
  markAllAlertsRead: () => void
  setLastAlertCheck: () => void
  
  // Actions - Search and Selection
  setSearchQuery: (query: string) => void
  toggleEquipmentSelection: (equipmentId: string) => void
  selectAllEquipment: (equipmentIds: ReadonlyArray<string>) => void
  clearSelection: () => void
  setBulkActionMode: (enabled: boolean) => void
  
  // Actions - Real-time Data
  updateDataTimestamp: () => void
  setConnectionStatus: (status: "connected" | "disconnected" | "reconnecting") => void
  setDataQuality: (quality: "good" | "fair" | "poor") => void
  
  // Utility Actions
  resetDashboard: () => void
}

// Default state values
const DEFAULT_FILTERS: EquipmentFilters = {
  status: undefined,
  type: undefined,
  priority: undefined,
  voltageClass: undefined,
  location: undefined,
  healthStatus: undefined,
  isOnline: undefined,
  maintenanceDue: undefined,
  searchQuery: undefined,
} as const

const initialState = {
  // UI State
  selectedEquipmentId: null,
  expandedEquipmentIds: [],
  layout: DEFAULT_DASHBOARD_LAYOUT,
  config: DEFAULT_DASHBOARD_CONFIG,
  filters: DEFAULT_FILTERS,
  isFilterPanelOpen: false,
  isRefreshing: false,
  
  // Alert State  
  unacknowledgedAlerts: [],
  mutedAlerts: [],
  lastAlertCheck: null,
  
  // Search and Selection
  searchQuery: "",
  selectedEquipmentIds: [],
  bulkActionMode: false,
  
  // Real-time Data State
  lastDataUpdate: null,
  connectionStatus: "disconnected" as const,
  dataQuality: "good" as const,
}

export const useEquipmentDashboardStore = create<EquipmentDashboardState>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // UI Management Actions
      setSelectedEquipment: (equipmentId: string | null) => {
        set({ selectedEquipmentId: equipmentId })
      },
      
      toggleEquipmentExpanded: (equipmentId: string) => {
        const { expandedEquipmentIds } = get()
        const isExpanded = expandedEquipmentIds.includes(equipmentId)
        
        set({
          expandedEquipmentIds: isExpanded
            ? expandedEquipmentIds.filter(id => id !== equipmentId)
            : [...expandedEquipmentIds, equipmentId]
        })
      },
      
      setLayout: (layoutUpdate: Partial<DashboardLayout>) => {
        const { layout } = get()
        set({ layout: { ...layout, ...layoutUpdate } })
      },
      
      setConfig: (configUpdate: Partial<DashboardConfig>) => {
        const { config } = get()
        set({ config: { ...config, ...configUpdate } })
      },
      
      setFilters: (filterUpdate: Partial<EquipmentFilters>) => {
        const { filters } = get()
        set({ filters: { ...filters, ...filterUpdate } })
      },
      
      resetFilters: () => {
        set({ filters: DEFAULT_FILTERS, searchQuery: "" })
      },
      
      toggleFilterPanel: () => {
        const { isFilterPanelOpen } = get()
        set({ isFilterPanelOpen: !isFilterPanelOpen })
      },
      
      setRefreshing: (refreshing: boolean) => {
        set({ isRefreshing: refreshing })
      },
      
      // Alert Management Actions
      acknowledgeAlert: (alertId: string) => {
        const { unacknowledgedAlerts } = get()
        set({
          unacknowledgedAlerts: unacknowledgedAlerts.filter(id => id !== alertId)
        })
      },
      
      muteAlert: (alertId: string) => {
        const { mutedAlerts, unacknowledgedAlerts } = get()
        if (!mutedAlerts.includes(alertId)) {
          set({
            mutedAlerts: [...mutedAlerts, alertId],
            unacknowledgedAlerts: unacknowledgedAlerts.filter(id => id !== alertId)
          })
        }
      },
      
      unmuteAlert: (alertId: string) => {
        const { mutedAlerts } = get()
        set({
          mutedAlerts: mutedAlerts.filter(id => id !== alertId)
        })
      },
      
      markAllAlertsRead: () => {
        set({ unacknowledgedAlerts: [] })
      },
      
      setLastAlertCheck: () => {
        set({ lastAlertCheck: new Date() })
      },
      
      // Search and Selection Actions
      setSearchQuery: (query: string) => {
        set({ searchQuery: query })
      },
      
      toggleEquipmentSelection: (equipmentId: string) => {
        const { selectedEquipmentIds } = get()
        const isSelected = selectedEquipmentIds.includes(equipmentId)
        
        set({
          selectedEquipmentIds: isSelected
            ? selectedEquipmentIds.filter(id => id !== equipmentId)
            : [...selectedEquipmentIds, equipmentId]
        })
      },
      
      selectAllEquipment: (equipmentIds: ReadonlyArray<string>) => {
        set({ selectedEquipmentIds: [...equipmentIds] })
      },
      
      clearSelection: () => {
        set({ selectedEquipmentIds: [], bulkActionMode: false })
      },
      
      setBulkActionMode: (enabled: boolean) => {
        set({ 
          bulkActionMode: enabled,
          selectedEquipmentIds: enabled ? get().selectedEquipmentIds : []
        })
      },
      
      // Real-time Data Actions
      updateDataTimestamp: () => {
        set({ lastDataUpdate: new Date() })
      },
      
      setConnectionStatus: (status: "connected" | "disconnected" | "reconnecting") => {
        set({ connectionStatus: status })
      },
      
      setDataQuality: (quality: "good" | "fair" | "poor") => {
        set({ dataQuality: quality })
      },
      
      // Utility Actions
      resetDashboard: () => {
        set({
          ...initialState,
          // Keep some persistent settings
          layout: get().layout,
          config: get().config,
        })
      },
    }),
    {
      name: "equipment-dashboard-storage",
      storage: createJSONStorage(() => localStorage),
      // Only persist user preferences, not transient state
      partialize: (state) => ({
        layout: state.layout,
        config: state.config,
        mutedAlerts: state.mutedAlerts,
        isFilterPanelOpen: state.isFilterPanelOpen,
      }),
    }
  )
)

// Selectors for computed state
export const useEquipmentDashboardSelectors = () => {
  const store = useEquipmentDashboardStore()
  
  return {
    // Get active filter count
    getActiveFilterCount: (): number => {
      const { filters } = store
      let count = 0
      
      if (filters.status?.length) count++
      if (filters.type?.length) count++
      if (filters.priority?.length) count++
      if (filters.voltageClass?.length) count++
      if (filters.location) count++
      if (filters.healthStatus?.length) count++
      if (filters.isOnline !== undefined) count++
      if (filters.maintenanceDue !== undefined) count++
      if (filters.searchQuery) count++
      
      return count
    },
    
    // Check if equipment matches current filters
    equipmentMatchesFilters: (equipment: Equipment): boolean => {
      const { filters, searchQuery } = store
      
      // Status filter
      if (filters.status?.length && !filters.status.includes(equipment.status)) {
        return false
      }
      
      // Type filter
      if (filters.type?.length && !filters.type.includes(equipment.type)) {
        return false
      }
      
      // Priority filter
      if (filters.priority?.length && !filters.priority.includes(equipment.priority)) {
        return false
      }
      
      // Voltage class filter
      if (filters.voltageClass?.length && !filters.voltageClass.includes(equipment.voltageClass)) {
        return false
      }
      
      // Location filter
      if (filters.location && !equipment.location.room.toLowerCase().includes(filters.location.toLowerCase())) {
        return false
      }
      
      // Health status filter
      if (filters.healthStatus?.length && !filters.healthStatus.includes(equipment.health)) {
        return false
      }
      
      // Online status filter
      if (filters.isOnline !== undefined && equipment.isOnline !== filters.isOnline) {
        return false
      }
      
      // Maintenance due filter
      if (filters.maintenanceDue !== undefined) {
        const isDue = equipment.maintenance.nextMaintenance <= new Date()
        if (isDue !== filters.maintenanceDue) {
          return false
        }
      }
      
      // Search query
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        const matchesName = equipment.name.toLowerCase().includes(query)
        const matchesModel = equipment.model?.toLowerCase().includes(query) ?? false
        const matchesSerial = equipment.serialNumber?.toLowerCase().includes(query) ?? false
        const matchesTags = equipment.tags?.some(tag => tag.toLowerCase().includes(query)) ?? false
        
        if (!matchesName && !matchesModel && !matchesSerial && !matchesTags) {
          return false
        }
      }
      
      return true
    },
    
    // Check if there are unacknowledged alerts
    hasUnacknowledgedAlerts: (): boolean => {
      return store.unacknowledgedAlerts.length > 0
    },
    
    // Get connection status indicator
    getConnectionStatusVariant: () => {
      switch (store.connectionStatus) {
        case "connected":
          return "operational" as const
        case "reconnecting":
          return "warning" as const
        case "disconnected":
          return "critical" as const
        default:
          return "offline" as const
      }
    },
  }
}

// Export store type for external use
export type EquipmentDashboardStore = typeof useEquipmentDashboardStore