/**
 * Unified Atomic Components - Main Exports
 *
 * Central export file for all unified atomic components
 * Provides clean imports and backward compatibility
 *
 * ENHANCED WITH ATOMIC DESIGN INTEGRATION
 * - New atomic design components available
 * - Full backward compatibility maintained
 * - Migration path documented
 */

// =============================================================================
// ATOMIC DESIGN COMPONENTS (NEW)
// =============================================================================

// Import and re-export atomic design components for easy access
export {
  // Atoms
  Button as AtomicButton,
  Input as AtomicInput,
  Label as AtomicLabel,
  Icon as AtomicIcon,
  Badge as AtomicBadge,

  // Atom Types
  type ButtonProps as AtomicButtonProps,
  type ButtonVariant as AtomicButtonVariant,
  type ButtonSize as AtomicButtonSize,
  type InputProps as AtomicInputProps,
  type InputSize as AtomicInputSize,
  type InputState as AtomicInputState,
  type InputVariant as AtomicInputVariant,
  type LabelProps as AtomicLabelProps,
  type IconProps as AtomicIconProps,
  type IconName as AtomicIconName,
  type BadgeProps as AtomicBadgeProps,
  type BadgeVariant as AtomicBadgeVariant,
} from "../atoms"

export {
  // Molecules
  InputField,
  ButtonGroup,
  ActionButtonGroup,

  // Molecule Types
  type InputFieldProps,
  type ButtonGroupProps,
  type ActionButtonGroupProps,
  type ButtonConfig,
} from "../molecules"

export {
  // Organisms
  AuthForm,

  // Organism Types
  type AuthFormProps,
  type AuthFormData,
} from "../organisms"

// =============================================================================
// BACKWARD COMPATIBILITY ALIASES
// =============================================================================

// Smart mapping for seamless migration
export { Button as AtomicButton_Compat } from "../atoms/Button"
export { Input as AtomicInput_Compat } from "../atoms/Input"
export { Label as AtomicLabel_Compat } from "../atoms/Label"
export { Icon as AtomicIcon_Compat } from "../atoms/Icon"
export { Badge as AtomicBadge_Compat } from "../atoms/Badge"

// =============================================================================
// UNIFIED BADGE SYSTEM (LEGACY - MAINTAINED)
// =============================================================================
export {
  // Main component
  UnifiedBadge,

  // Backward compatibility components
  ComponentBadge,
  StatusBadge,
  PriorityBadge,

  // Convenience components
  ActiveBadge,
  InactiveBadge,
  PreferredBadge,
  DraftBadge,
  CompletedBadge,
  HighPriorityBadge,
  CriticalPriorityBadge,

  // Hooks and utilities
  useComponentBadgeProps,
  useStatusBadgeProps,
  usePriorityBadgeProps,
  isValidComponentStatus,
  isValidProjectStatus,
  isValidProjectPriority,
  getBadgeVariantFromStatus,
  getPriorityLevel,

  // Configuration
  badgeTypeConfig,

  // Variants
  unifiedBadgeVariants,
} from "../atoms/unified-badge"

// =============================================================================
// UNIFIED BUTTON SYSTEM (LEGACY - MAINTAINED)
// =============================================================================
export {
  // Main component
  UnifiedButton,

  // Backward compatibility component
  ActionButton,

  // Convenience components
  CreateButton,
  EditButton,
  DeleteButton,
  SaveButton,
  CancelButton,
  MoreButton,
  BackButton,
  NextButton,

  // Utilities
  getActionConfig,
  isValidActionType,
  getActionsByCategory,

  // Configuration
  actionConfig,
  sizeConfig as buttonSizeConfig,
} from "../atoms/unified-button"

// =============================================================================
// UNIFIED FORM SYSTEM (LEGACY - MAINTAINED)
// =============================================================================
export {
  // Main component
  UnifiedFormField,

  // Backward compatibility component
  FormInput,

  // Specialized field components
  EmailField,
  PasswordField,
  NumberField,
  SearchField,
  TextAreaField,
  TelephoneField,
  UrlField,

  // Utilities
  validateField,
  getFieldTypeFromString,

  // Configuration
  sizeConfig as formSizeConfig,
  formFieldVariants,
} from "../atoms/unified-form"

// =============================================================================
// UNIFIED STATE SYSTEM (LEGACY - MAINTAINED)
// =============================================================================
export {
  // Main components
  UnifiedLoading,
  UnifiedEmptyState,
  UnifiedErrorState,

  // Backward compatibility components
  LoadingSpinner,
  EmptyState,

  // Specialized components
  LoadingOverlay,
  InlineLoading,
  EmptyProjectList,
  EmptySearchResults,
  EmptyTeamMembers,
  EmptyComponents,
  NetworkError,
  SkeletonLine,
  SkeletonCard,

  // Utilities
  getEmptyStateConfig,
  isValidEmptyStateVariant,
  isValidLoadingVariant,

  // Configuration
  loadingSizeConfig,
  emptyStateSizeConfig,
  emptyStateConfig,
  loadingVariants,
  emptyStateVariants,
} from "../atoms/unified-state"

// =============================================================================
// UNIFIED ICON SYSTEM (LEGACY - MAINTAINED)
// =============================================================================
export {
  // Main component
  UnifiedIcon,

  // Backward compatibility component
  ComponentIcon,

  // Electrical component convenience components
  ResistorIcon,
  CapacitorIcon,
  TransistorIcon,
  ICIcon,
  SensorIcon,
  SwitchIcon,
  ConnectorIcon,
  PowerSupplyIcon,
  MotorIcon,
  BatteryIcon,

  // UI convenience components
  AddIcon,
  EditIcon,
  DeleteIcon,
  SearchIcon,
  SettingsIcon,
  UserIcon,
  HomeIcon,

  // Category components
  ElectricalIcon,
  UIIcon,

  // Utilities
  getIconComponent,
  hasIcon,
  isElectricalComponent,
  isGeneralIcon,
  getIconsByCategory,
  useComponentIconProps,

  // Available types
  availableElectricalTypes,
  availableGeneralTypes,
  availableIconTypes,

  // Configuration
  electricalComponentIcons,
  generalIcons,
  allIcons,
  iconVariants,
} from "../atoms/unified-icon"

// =============================================================================
// TYPES AND INTERFACES
// =============================================================================
export type * from "../atoms/types"

// =============================================================================
// RE-EXPORTS FOR CONVENIENCE (LEGACY - MAINTAINED)
// =============================================================================

// Re-export commonly used existing UI components for convenience
export { Badge } from "./badge"
export { Button } from "./button"
export { Input } from "./input"
export { Label } from "./label"
export { Textarea } from "./textarea"

// =============================================================================
// MIGRATION GUIDE COMMENTS
// =============================================================================

/*
MIGRATION GUIDE: Unified Components → Atomic Design

RECOMMENDED MIGRATION PATH:
1. Use atomic components for new development:
   - import { Button, Input, Label, Icon, Badge } from "@/components/atoms"
   - import { InputField, ButtonGroup } from "@/components/molecules"
   - import { AuthForm } from "@/components/organisms"

2. Existing code continues to work:
   - All current imports remain functional
   - No breaking changes introduced
   - Gradual migration supported

ATOMIC DESIGN BENEFITS:
- Better component composition
- Improved maintainability
- Consistent design system
- Enhanced accessibility
- Performance optimization

EXAMPLES:

Old (still works):
  import { UnifiedButton } from "@/components/ui"
  <UnifiedButton action="save" />

New (recommended):
  import { Button } from "@/components/atoms"
  <Button>Save</Button>

Advanced (molecules/organisms):
  import { InputField } from "@/components/molecules"
  import { AuthForm } from "@/components/organisms"
  
  <InputField label="Email" value={email} onChange={setEmail} />
  <AuthForm formType="login" onSubmit={handleLogin} />
*/
