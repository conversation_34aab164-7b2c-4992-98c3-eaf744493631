/**
 * Unified State Components - Comprehensive Test Suite
 *
 * Tests following TDD methodology with complete coverage:
 * - Loading states and variants
 * - Empty state components and configurations
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Backward compatibility
 * - Performance and edge cases
 */

import React from "react"

import { render, screen } from "@testing-library/react"
import { vi } from "vitest"

import {
  EmptyState,
  InlineLoading,
  LoadingOverlay,
  LoadingSpinner,
  UnifiedEmptyState,
  UnifiedLoading,
} from "../../atoms/unified-state"

describe("UnifiedLoading", () => {
  // Basic rendering tests
  describe("Basic Rendering", () => {
    it("renders with default props", () => {
      render(<UnifiedLoading data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toBeInTheDocument()
      expect(loading).toHaveAttribute("role", "status")
      expect(loading).toHaveAttribute("aria-label", "Loading")
    })

    it("renders with custom text", () => {
      render(<UnifiedLoading text="Processing data..." data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toHaveAttribute("aria-label", "Processing data...")
      expect(screen.getByText("Processing data...")).toBeInTheDocument()
    })

    it("hides text when showText is false", () => {
      render(
        <UnifiedLoading
          text="Loading..."
          showText={false}
          data-testid="loading"
        />
      )
      expect(screen.queryByText("Loading...")).not.toBeInTheDocument()

      const loading = screen.getByTestId("loading")
      expect(loading).toHaveAttribute("aria-label", "Loading...")
    })
  })

  // Variant tests
  describe("Loading Variants", () => {
    it("renders spinner variant correctly", () => {
      render(<UnifiedLoading variant="spinner" data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toBeInTheDocument()

      // Should contain spinning animation
      const spinner = loading.querySelector(".animate-spin")
      expect(spinner).toBeInTheDocument()
    })

    it("renders dots variant correctly", () => {
      render(<UnifiedLoading variant="dots" data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toBeInTheDocument()

      // Should contain multiple dots
      const dots = loading.querySelectorAll(".animate-pulse")
      expect(dots.length).toBeGreaterThan(0)
    })

    it("renders bars variant correctly", () => {
      render(<UnifiedLoading variant="bars" data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toBeInTheDocument()

      // Should contain bars with staggered animation
      const bars = loading.querySelectorAll('[style*="animation-delay"]')
      expect(bars.length).toBeGreaterThan(0)
    })

    it("renders pulse variant correctly", () => {
      render(<UnifiedLoading variant="pulse" data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toBeInTheDocument()
      expect(loading).toHaveClass("animate-pulse")
    })

    it("renders refresh variant correctly", () => {
      render(<UnifiedLoading variant="refresh" data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toBeInTheDocument()

      // Should contain refresh icon with spin animation
      const refreshIcon = loading.querySelector(".animate-spin")
      expect(refreshIcon).toBeInTheDocument()
    })

    it("warns for invalid variant", () => {
      const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})

      render(
        <UnifiedLoading
          // @ts-expect-error Testing invalid variant
          variant="invalid-variant"
          data-testid="loading"
        />
      )

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Invalid loading variant "invalid-variant"')
      )

      consoleSpy.mockRestore()
    })
  })

  // Size tests
  describe("Sizes", () => {
    it("applies correct size classes", () => {
      const { rerender } = render(
        <UnifiedLoading size="sm" data-testid="loading" />
      )
      let loading = screen.getByTestId("loading")
      expect(loading).toHaveClass("text-sm")

      rerender(<UnifiedLoading size="lg" data-testid="loading" />)
      loading = screen.getByTestId("loading")
      expect(loading).toHaveClass("text-lg")
    })
  })

  // Inline mode tests
  describe("Inline Mode", () => {
    it("applies inline styles when inline is true", () => {
      render(<UnifiedLoading inline={true} data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toHaveClass("inline-flex")
    })

    it("applies block styles when inline is false", () => {
      render(<UnifiedLoading inline={false} data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toHaveClass("flex")
      expect(loading).not.toHaveClass("inline-flex")
    })
  })

  // Custom content tests
  describe("Custom Content", () => {
    it("renders custom icon when provided", () => {
      const CustomIcon = ({ className }: { className?: string }) => (
        <div className={className} data-testid="custom-icon">
          ⚡
        </div>
      )

      render(<UnifiedLoading customIcon={CustomIcon} data-testid="loading" />)

      expect(screen.getByTestId("custom-icon")).toBeInTheDocument()
      expect(screen.getByText("⚡")).toBeInTheDocument()
    })
  })

  // Accessibility tests
  describe("Accessibility", () => {
    it("has proper ARIA attributes", () => {
      render(<UnifiedLoading text="Processing..." data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toHaveAttribute("role", "status")
      expect(loading).toHaveAttribute("aria-label", "Processing...")
    })

    it("is not focusable", () => {
      render(<UnifiedLoading data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).not.toHaveAttribute("tabIndex")
    })

    it("announces loading state to screen readers", () => {
      render(<UnifiedLoading text="Loading content..." data-testid="loading" />)
      const loading = screen.getByTestId("loading")
      expect(loading).toHaveAttribute("aria-live", "polite")
    })
  })
})

describe("UnifiedEmptyState", () => {
  // Basic rendering tests
  describe("Basic Rendering", () => {
    it("renders with title only", () => {
      render(
        <UnifiedEmptyState title="No items found" data-testid="empty-state" />
      )
      const emptyState = screen.getByTestId("empty-state")
      expect(emptyState).toBeInTheDocument()
      expect(screen.getByRole("heading", { level: 3 })).toHaveTextContent(
        "No items found"
      )
    })

    it("renders with title and description", () => {
      render(
        <UnifiedEmptyState
          title="No results"
          description="Try adjusting your search filters"
          data-testid="empty-state"
        />
      )

      expect(
        screen.getByRole("heading", { name: "No results" })
      ).toBeInTheDocument()
      expect(
        screen.getByText("Try adjusting your search filters")
      ).toBeInTheDocument()
    })

    it("renders with custom icon", () => {
      const CustomIcon = () => <div data-testid="custom-icon">📂</div>

      render(
        <UnifiedEmptyState
          title="Empty folder"
          icon={<CustomIcon />}
          data-testid="empty-state"
        />
      )

      expect(screen.getByTestId("custom-icon")).toBeInTheDocument()
    })

    it("renders with action button", () => {
      render(
        <UnifiedEmptyState
          title="No items"
          action={<button>Add Item</button>}
          data-testid="empty-state"
        />
      )

      expect(
        screen.getByRole("button", { name: "Add Item" })
      ).toBeInTheDocument()
    })
  })

  // Variant tests
  describe("Empty State Variants", () => {
    it("renders search variant with appropriate icon", () => {
      render(
        <UnifiedEmptyState
          variant="search"
          title="No search results"
          data-testid="empty-state"
        />
      )

      const emptyState = screen.getByTestId("empty-state")
      expect(emptyState).toBeInTheDocument()

      // Should contain search-related icon
      const icon = emptyState.querySelector("svg")
      expect(icon).toBeInTheDocument()
    })

    it("renders folder variant with appropriate icon", () => {
      render(
        <UnifiedEmptyState
          variant="folder"
          title="Empty folder"
          data-testid="empty-state"
        />
      )

      const emptyState = screen.getByTestId("empty-state")
      const icon = emptyState.querySelector("svg")
      expect(icon).toBeInTheDocument()
    })

    it("renders data variant with appropriate icon", () => {
      render(
        <UnifiedEmptyState
          variant="data"
          title="No data available"
          data-testid="empty-state"
        />
      )

      const emptyState = screen.getByTestId("empty-state")
      const icon = emptyState.querySelector("svg")
      expect(icon).toBeInTheDocument()
    })

    it("renders users variant with appropriate icon", () => {
      render(
        <UnifiedEmptyState
          variant="users"
          title="No users found"
          data-testid="empty-state"
        />
      )

      const emptyState = screen.getByTestId("empty-state")
      const icon = emptyState.querySelector("svg")
      expect(icon).toBeInTheDocument()
    })

    it("renders custom variant without default icon", () => {
      render(
        <UnifiedEmptyState
          variant="custom"
          title="Custom empty state"
          data-testid="empty-state"
        />
      )

      const emptyState = screen.getByTestId("empty-state")
      // Should not have default icon when custom variant
      const icon = emptyState.querySelector("svg")
      expect(icon).not.toBeInTheDocument()
    })

    it("warns for invalid variant", () => {
      const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})

      render(
        <UnifiedEmptyState
          // @ts-expect-error Testing invalid variant
          variant="invalid-variant"
          title="Test"
          data-testid="empty-state"
        />
      )

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Invalid empty state variant "invalid-variant"')
      )

      consoleSpy.mockRestore()
    })
  })

  // Size tests
  describe("Sizes", () => {
    it("applies correct size classes", () => {
      const { rerender } = render(
        <UnifiedEmptyState title="Test" size="sm" data-testid="empty-state" />
      )
      let emptyState = screen.getByTestId("empty-state")
      expect(emptyState).toHaveClass("max-w-sm")

      rerender(
        <UnifiedEmptyState title="Test" size="lg" data-testid="empty-state" />
      )
      emptyState = screen.getByTestId("empty-state")
      expect(emptyState).toHaveClass("max-w-2xl")
    })
  })

  // Accessibility tests
  describe("Accessibility", () => {
    it("uses proper heading hierarchy", () => {
      render(<UnifiedEmptyState title="No items" data-testid="empty-state" />)
      const heading = screen.getByRole("heading", { level: 3 })
      expect(heading).toHaveTextContent("No items")
    })

    it("is not focusable by default", () => {
      render(<UnifiedEmptyState title="No items" data-testid="empty-state" />)
      const emptyState = screen.getByTestId("empty-state")
      expect(emptyState).not.toHaveAttribute("tabIndex")
    })

    it("action buttons are properly accessible", () => {
      render(
        <UnifiedEmptyState
          title="No items"
          action={<button aria-label="Add new item">Add Item</button>}
          data-testid="empty-state"
        />
      )

      const button = screen.getByRole("button", { name: "Add new item" })
      expect(button).toBeInTheDocument()
    })
  })
})

// Backward compatibility component tests
describe("Backward Compatibility Components", () => {
  describe("LoadingSpinner", () => {
    it("renders identically to UnifiedLoading", () => {
      render(<LoadingSpinner text="Loading..." data-testid="loading-spinner" />)
      const spinner = screen.getByTestId("loading-spinner")
      expect(spinner).toBeInTheDocument()
      expect(spinner).toHaveAttribute("role", "status")
      expect(screen.getByText("Loading...")).toBeInTheDocument()
    })

    it("supports all legacy props", () => {
      render(
        <LoadingSpinner
          size="lg"
          text="Processing..."
          variant="dots"
          inline={true}
          data-testid="loading-spinner"
        />
      )

      const spinner = screen.getByTestId("loading-spinner")
      expect(spinner).toBeInTheDocument()
      expect(spinner).toHaveClass("inline-flex", "text-lg")
    })
  })

  describe("EmptyState", () => {
    it("renders identically to UnifiedEmptyState", () => {
      render(
        <EmptyState
          title="No items found"
          description="Try again later"
          data-testid="empty-state"
        />
      )

      const emptyState = screen.getByTestId("empty-state")
      expect(emptyState).toBeInTheDocument()
      expect(
        screen.getByRole("heading", { name: "No items found" })
      ).toBeInTheDocument()
      expect(screen.getByText("Try again later")).toBeInTheDocument()
    })
  })

  describe("LoadingOverlay", () => {
    it("renders as overlay with correct styling", () => {
      render(
        <LoadingOverlay
          text="Loading overlay..."
          data-testid="loading-overlay"
        />
      )
      const overlay = screen.getByTestId("loading-overlay")
      expect(overlay).toBeInTheDocument()
      expect(overlay).toHaveClass("fixed", "inset-0")
      expect(screen.getByText("Loading overlay...")).toBeInTheDocument()
    })
  })

  describe("InlineLoading", () => {
    it("renders as inline loading with correct styling", () => {
      render(
        <InlineLoading
          text="Inline loading..."
          size="sm"
          data-testid="inline-loading"
        />
      )
      const inlineLoading = screen.getByTestId("inline-loading")
      expect(inlineLoading).toBeInTheDocument()
      expect(inlineLoading).toHaveClass("inline-flex")
    })
  })
})

// Test functionality without utility function exports
describe("Component Functionality", () => {
  it("handles invalid loading variants gracefully", () => {
    const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})

    render(
      <UnifiedLoading
        // @ts-expect-error Testing invalid variant
        variant="invalid"
        data-testid="loading"
      />
    )

    const loading = screen.getByTestId("loading")
    expect(loading).toBeInTheDocument()

    consoleSpy.mockRestore()
  })

  it("handles invalid empty state variants gracefully", () => {
    const consoleSpy = vi.spyOn(console, "warn").mockImplementation(() => {})

    render(
      <UnifiedEmptyState
        // @ts-expect-error Testing invalid variant
        variant="invalid"
        title="Test"
        data-testid="empty-state"
      />
    )

    const emptyState = screen.getByTestId("empty-state")
    expect(emptyState).toBeInTheDocument()

    consoleSpy.mockRestore()
  })
})

// Performance tests
describe("Performance", () => {
  it("renders multiple loading states quickly", () => {
    const startTime = performance.now()

    const loadingStates = Array.from({ length: 50 }, (_, i) => (
      <UnifiedLoading
        key={i}
        text={`Loading ${i}`}
        variant={i % 2 === 0 ? "spinner" : "dots"}
        data-testid={`loading-${i}`}
      />
    ))

    render(<div>{loadingStates}</div>)

    const endTime = performance.now()
    const renderTime = endTime - startTime

    // Should render 50 loading states in less than 100ms (more realistic)
    expect(renderTime).toBeLessThan(100)

    // Verify all loading states are rendered
    expect(screen.getAllByRole("status")).toHaveLength(50)
  })

  it("renders multiple empty states quickly", () => {
    const startTime = performance.now()

    const emptyStates = Array.from({ length: 30 }, (_, i) => (
      <UnifiedEmptyState
        key={i}
        title={`Empty state ${i}`}
        variant={i % 2 === 0 ? "search" : "folder"}
        data-testid={`empty-${i}`}
      />
    ))

    render(<div>{emptyStates}</div>)

    const endTime = performance.now()
    const renderTime = endTime - startTime

    // Should render 30 empty states in less than 100ms (more realistic)
    expect(renderTime).toBeLessThan(100)

    // Verify all empty states are rendered
    expect(screen.getAllByRole("heading")).toHaveLength(30)
  })
})

// Edge cases
describe("Edge Cases", () => {
  it("handles undefined text gracefully", () => {
    render(
      <UnifiedLoading
        // @ts-expect-error Testing undefined text
        text={undefined}
        data-testid="loading"
      />
    )

    const loading = screen.getByTestId("loading")
    expect(loading).toBeInTheDocument()
    expect(loading).toHaveAttribute("aria-label", "Loading")
  })

  it("handles empty strings gracefully", () => {
    render(
      <UnifiedEmptyState title="" description="" data-testid="empty-state" />
    )

    const emptyState = screen.getByTestId("empty-state")
    expect(emptyState).toBeInTheDocument()
  })

  it("handles very long text appropriately", () => {
    const longText = "A".repeat(200)
    render(<UnifiedLoading text={longText} data-testid="loading" />)

    const loading = screen.getByTestId("loading")
    expect(loading).toHaveAttribute("aria-label", longText)
  })

  it("handles rapid variant changes", () => {
    const { rerender } = render(
      <UnifiedLoading variant="spinner" data-testid="loading" />
    )

    rerender(<UnifiedLoading variant="dots" data-testid="loading" />)
    rerender(<UnifiedLoading variant="bars" data-testid="loading" />)
    rerender(<UnifiedLoading variant="pulse" data-testid="loading" />)

    const loading = screen.getByTestId("loading")
    expect(loading).toBeInTheDocument()
  })

  it("handles missing required props gracefully", () => {
    render(
      <UnifiedEmptyState
        // @ts-expect-error Testing missing title
        data-testid="empty-state"
      />
    )

    const emptyState = screen.getByTestId("empty-state")
    expect(emptyState).toBeInTheDocument()
  })
})
