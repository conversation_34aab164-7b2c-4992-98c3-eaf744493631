/**
 * Unified Atomic Components - TypeScript Types & Interfaces
 *
 * Comprehensive type definitions for all unified atomic components
 * Provides type safety, IntelliSense support, and component contracts
 *
 * Features:
 * - Complete type coverage for all unified components
 * - Backward compatibility types
 * - Strict TypeScript compliance
 * - Exported utility types for external use
 * - Documentation with JSDoc comments
 */

import type { VariantProps } from "class-variance-authority"
import type { unifiedBadgeVariants } from "./unified-badge"
import type {
  actionConfig,
  sizeConfig as buttonSizeConfig,
} from "./unified-button"
import type {
  formFieldVariants,
  sizeConfig as formSizeConfig,
} from "./unified-form"
import type { iconVariants } from "./unified-icon"
import type { emptyStateVariants, loadingVariants } from "./unified-state"

// =============================================================================
// BASE TYPES
// =============================================================================

/** Base HTML element props without conflicting properties */
export type BaseElementProps<T = HTMLElement> = Omit<
  React.HTMLAttributes<T>,
  "size" | "color"
>

/** Standard component size options */
export type ComponentSize = "xs" | "sm" | "md" | "lg" | "xl"

/** Extended component size options including 2xl and 3xl */
export type ExtendedSize = ComponentSize | "2xl" | "3xl"

/** Standard color intentions for components */
export type ColorIntention =
  | "default"
  | "primary"
  | "secondary"
  | "destructive"
  | "success"
  | "warning"
  | "info"
  | "muted"

/** Component state for validation and feedback */
export type ComponentState =
  | "default"
  | "error"
  | "warning"
  | "success"
  | "info"

// =============================================================================
// UNIFIED BADGE TYPES
// =============================================================================

/** Badge variant options */
export type BadgeVariant = "default" | "solid" | "outline" | "ghost"

/** Badge type categories */
export type BadgeType = "component" | "status" | "priority" | "generic"

/** Component status options */
export type ComponentStatus =
  | "active"
  | "inactive"
  | "preferred"
  | "available"
  | "limited"
  | "out_of_stock"
  | "discontinued"
  | "on_order"

/** Project status options */
export type ProjectStatus =
  | "draft"
  | "active"
  | "paused"
  | "completed"
  | "cancelled"

/** Project priority options */
export type ProjectPriority = "low" | "medium" | "high" | "critical"

/** Generic semantic status options */
export type GenericStatus = "info" | "success" | "warning" | "error"

/** All possible badge intents */
export type BadgeIntent =
  | ComponentStatus
  | ProjectStatus
  | ProjectPriority
  | GenericStatus

/** Unified badge component props */
export interface UnifiedBadgeProps
  extends BaseElementProps<HTMLSpanElement>,
    VariantProps<typeof unifiedBadgeVariants> {
  /** Badge type category */
  type?: BadgeType
  /** Specific intent within the type */
  intent: BadgeIntent
  /** Show icon in badge */
  showIcon?: boolean
  /** Show label text in badge */
  showLabel?: boolean
  /** Custom label text override */
  customLabel?: string
  /** Custom icon component */
  customIcon?: React.ComponentType<{ className?: string }>
  /** Animate with pulse effect */
  pulse?: boolean
  /** Make badge interactive (clickable) */
  interactive?: boolean
  /** Test ID for testing */
  "data-testid"?: string
}

// =============================================================================
// UNIFIED BUTTON TYPES
// =============================================================================

/** Extended action types for comprehensive button coverage */
export type ActionType =
  // Core CRUD
  | "create"
  | "read"
  | "update"
  | "delete"
  | "add"
  | "remove"
  | "edit"
  | "view"
  // File operations
  | "save"
  | "download"
  | "upload"
  | "copy"
  | "share"
  | "export"
  | "import"
  // State changes
  | "activate"
  | "deactivate"
  | "pause"
  | "resume"
  | "play"
  | "stop"
  | "complete"
  | "cancel"
  | "archive"
  | "restore"
  | "refresh"
  // User management
  | "add-user"
  | "remove-user"
  | "invite-user"
  | "assign"
  | "unassign"
  | "approve"
  | "reject"
  // Navigation
  | "back"
  | "forward"
  | "next"
  | "previous"
  | "home"
  | "goto"
  | "link"
  // Interaction
  | "more"
  | "more-vertical"
  | "expand"
  | "collapse"
  | "filter"
  | "search"
  | "sort"
  // Communication
  | "notify"
  | "unnotify"
  | "bell"
  | "mail"
  | "subscribe"
  | "unsubscribe"
  // Favorites
  | "favorite"
  | "unfavorite"
  | "star"
  | "unstar"
  | "bookmark"
  | "unbookmark"
  | "like"
  | "unlike"
  // Security
  | "lock"
  | "unlock"
  | "secure"
  | "login"
  | "logout"
  // Settings
  | "settings"
  | "configure"
  | "customize"
  // Documentation
  | "help"
  | "info"
  | "docs"
  | "guide"
  // Scheduling
  | "schedule"
  | "calendar"
  | "event"
  // Visibility
  | "show"
  | "hide"
  | "preview"
  | "toggle"
  // Performance
  | "boost"
  | "optimize"
  | "enhance"

/** Button display options */
export type ButtonDisplay = "icon" | "text" | "both" | "icon-text" | "text-icon"

/** Button variant options */
export type ButtonVariant =
  | "default"
  | "destructive"
  | "outline"
  | "secondary"
  | "ghost"
  | "link"

/** Button size options */
export type ButtonSize = keyof typeof buttonSizeConfig

/** Icon position in button */
export type IconPosition = "left" | "right"

/** Unified button component props */
export interface UnifiedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** Action type determining icon and default styling */
  action: ActionType
  /** Button size */
  size?: ButtonSize
  /** Button variant override */
  variant?: ButtonVariant
  /** Display mode for icon and text */
  display?: ButtonDisplay
  /** Loading state */
  loading?: boolean
  /** Loading state text override */
  loadingText?: string
  /** Custom label text override */
  customLabel?: string
  /** Custom icon component */
  customIcon?: React.ComponentType<{ className?: string }>
  /** Icon position when showing both icon and text */
  iconPosition?: IconPosition
  /** Make button full width */
  fullWidth?: boolean
  /** Test ID for testing */
  "data-testid"?: string
}

// =============================================================================
// UNIFIED FORM TYPES
// =============================================================================

/** Form field input types */
export type FieldType =
  | "text"
  | "email"
  | "password"
  | "number"
  | "tel"
  | "url"
  | "search"
  | "textarea"
  | "hidden"

/** Form field validation state */
export type ValidationState = ComponentState

/** Form field size options */
export type FormFieldSize = keyof typeof formSizeConfig

/** Form field variant options */
export type FormFieldVariant = "default" | "ghost" | "outline"

/** Unified form field component props */
export interface UnifiedFormFieldProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement>,
    "size"
  > {
  // Core props
  /** Field label text */
  label: string
  /** Input field type */
  type?: FieldType

  // Validation props
  /** Error message(s) */
  error?: string | string[]
  /** Warning message(s) */
  warning?: string | string[]
  /** Success message(s) */
  success?: string | string[]
  /** Info message(s) */
  info?: string | string[]
  /** Help text */
  helpText?: string

  // State props
  /** Required field indicator */
  required?: boolean
  /** Optional field indicator */
  optional?: boolean
  /** Field has been interacted with */
  touched?: boolean
  /** Loading state */
  loading?: boolean

  // Appearance props
  /** Field size */
  size?: FormFieldSize
  /** Field variant */
  variant?: FormFieldVariant

  // Interactive props
  /** Show clear button for text fields */
  clearable?: boolean
  /** Show password visibility toggle */
  showPasswordToggle?: boolean
  /** Clear field callback */
  onClear?: () => void

  // Additional props
  /** Field description */
  description?: string
  /** Tooltip text */
  tooltip?: string
  /** Prefix content */
  prefix?: React.ReactNode
  /** Suffix content */
  suffix?: React.ReactNode

  // Accessibility props
  /** ARIA described by */
  "aria-describedby"?: string
  /** Test ID for testing */
  "data-testid"?: string
}

// =============================================================================
// UNIFIED STATE TYPES
// =============================================================================

/** Loading animation variants */
export type LoadingVariant = "spinner" | "pulse" | "dots" | "bars" | "refresh"

/** Loading layout options */
export type LoadingLayout = "inline" | "center" | "left" | "right"

/** Loading component size */
export type LoadingSize = ComponentSize

/** Empty state variant categories */
export type EmptyStateVariant =
  | "search"
  | "folder"
  | "users"
  | "documents"
  | "data"
  | "settings"
  | "calendar"
  | "archive"
  | "package"
  | "cart"
  | "favorites"
  | "network"
  | "server"
  | "custom"

/** Empty state component size */
export type EmptyStateSize = ComponentSize

/** Unified loading component props */
export interface UnifiedLoadingProps
  extends BaseElementProps<HTMLDivElement>,
    VariantProps<typeof loadingVariants> {
  /** Loading animation variant */
  variant?: LoadingVariant
  /** Loading text */
  text?: string
  /** Show text with loading indicator */
  showText?: boolean
  /** Test ID for testing */
  "data-testid"?: string
}

/** Unified empty state component props */
export interface UnifiedEmptyStateProps
  extends BaseElementProps<HTMLDivElement>,
    VariantProps<typeof emptyStateVariants> {
  /** Empty state variant */
  variant?: EmptyStateVariant
  /** Custom icon element */
  icon?: React.ReactNode
  /** Title text */
  title?: string
  /** Description text */
  description?: string
  /** Action element (usually a button) */
  action?: React.ReactNode
  /** Custom icon component */
  customIcon?: React.ComponentType<{ className?: string }>
  /** Test ID for testing */
  "data-testid"?: string
}

/** Error state component props */
export interface UnifiedErrorStateProps extends UnifiedEmptyStateProps {
  /** Error object or message */
  error?: Error | string
  /** Retry function */
  retry?: () => void
  /** Retry button text */
  retryText?: string
}

// =============================================================================
// UNIFIED ICON TYPES
// =============================================================================

/** Electrical component icon types */
export type ElectricalComponentType =
  // Basic components
  | "resistor"
  | "capacitor"
  | "inductor"
  | "diode"
  | "led"
  | "transistor"
  // Power components
  | "battery"
  | "power_supply"
  | "transformer"
  | "regulator"
  | "converter"
  | "inverter"
  // Connectors
  | "connector"
  | "cable"
  | "wire"
  | "terminal"
  | "socket"
  // Switches & Controls
  | "switch"
  | "button"
  | "relay"
  | "contactor"
  | "breaker"
  | "fuse"
  // Sensors
  | "sensor"
  | "temperature_sensor"
  | "pressure_sensor"
  | "proximity_sensor"
  // Communication
  | "antenna"
  | "transceiver"
  | "modem"
  | "router"
  | "bluetooth"
  | "wifi"
  // Display & Interface
  | "display"
  | "lcd"
  | "oled"
  | "touchscreen"
  | "keypad"
  // Audio
  | "speaker"
  | "microphone"
  | "buzzer"
  | "amplifier"
  // Mechanical
  | "motor"
  | "actuator"
  | "valve"
  | "pump"
  | "fan"
  // ICs & Processing
  | "ic"
  | "microcontroller"
  | "microprocessor"
  | "memory"
  // Default
  | "default"

/** General UI icon types */
export type GeneralIconType =
  // Actions
  | "add"
  | "edit"
  | "delete"
  | "save"
  | "copy"
  | "refresh"
  // Navigation
  | "home"
  | "back"
  | "forward"
  | "menu"
  | "more"
  // Files
  | "file"
  | "folder"
  | "image"
  | "video"
  | "download"
  | "upload"
  // Communication
  | "mail"
  | "message"
  | "send"
  | "share"
  // Users
  | "user"
  | "users"
  | "add_user"
  | "profile"
  // Status
  | "success"
  | "error"
  | "warning"
  | "info"
  | "notification"
  // Interface
  | "search"
  | "filter"
  | "settings"
  | "grid"
  | "list"
  // Default
  | "default"

/** All available icon types */
export type IconType = ElectricalComponentType | GeneralIconType

/** Icon size options */
export type IconSize = ExtendedSize

/** Icon color options including electrical-specific colors */
export type IconColor =
  | ColorIntention
  | "electrical"
  | "power"
  | "signal"
  | "mechanical"
  | "thermal"

/** Unified icon component props */
export interface UnifiedIconProps
  extends BaseElementProps<HTMLDivElement>,
    VariantProps<typeof iconVariants> {
  /** Icon type */
  type: IconType | string
  /** Custom icon component */
  customIcon?: React.ComponentType<{ className?: string }>
  /** Test ID for testing */
  "data-testid"?: string
}

// =============================================================================
// BACKWARD COMPATIBILITY TYPES
// =============================================================================

/** Legacy ComponentBadge props for backward compatibility */
export interface ComponentBadgeProps extends BaseElementProps<HTMLSpanElement> {
  /** Component status */
  status: ComponentStatus
  /** Badge size */
  size?: ComponentSize
  /** Badge variant */
  variant?: BadgeVariant
  /** Show icon */
  showIcon?: boolean
  /** Show label */
  showLabel?: boolean
  /** Custom label */
  customLabel?: string
  /** Pulse animation */
  pulse?: boolean
  /** Test ID */
  "data-testid"?: string
}

/** Legacy StatusBadge props for backward compatibility */
export interface StatusBadgeProps {
  /** Project status */
  status: ProjectStatus
  /** Badge size */
  size?: ComponentSize
  /** Badge variant */
  variant?: BadgeVariant
  /** Show icon */
  showIcon?: boolean
  /** CSS class name */
  className?: string
}

/** Legacy PriorityBadge props for backward compatibility */
export interface PriorityBadgeProps {
  /** Project priority */
  priority: ProjectPriority
  /** Badge size */
  size?: ComponentSize
  /** Badge variant */
  variant?: BadgeVariant
  /** Show icon */
  showIcon?: boolean
  /** CSS class name */
  className?: string
}

/** Legacy ActionButton props for backward compatibility */
export interface ActionButtonProps {
  /** Action type */
  action: ActionType
  /** Button size */
  size?: ComponentSize
  /** Button variant */
  variant?: ButtonVariant
  /** Display mode */
  display?: ButtonDisplay
  /** Disabled state */
  disabled?: boolean
  /** Loading state */
  loading?: boolean
  /** CSS class name */
  className?: string
  /** Click handler */
  onClick?: () => void
  /** Children content */
  children?: React.ReactNode
}

/** Legacy FormInput props for backward compatibility */
export interface FormInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size"> {
  /** Field label */
  label: string
  /** Error message */
  error?: string
  /** Warning message */
  warning?: string
  /** Success message */
  success?: string
  /** Help text */
  helpText?: string
  /** Required field */
  required?: boolean
  /** Field touched */
  touched?: boolean
  /** Field size */
  size?: ComponentSize
  /** Field variant */
  variant?: FormFieldVariant
}

/** Legacy LoadingSpinner props for backward compatibility */
export interface LoadingSpinnerProps {
  /** Loading size */
  size?: ComponentSize
  /** Loading text */
  text?: string
  /** Animation variant */
  variant?: LoadingVariant
  /** CSS class name */
  className?: string
  /** Inline display */
  inline?: boolean
}

/** Legacy EmptyState props for backward compatibility */
export interface EmptyStateProps {
  /** Custom icon */
  icon?: React.ReactNode
  /** Title text */
  title: string
  /** Description text */
  description?: string
  /** Action element */
  action?: React.ReactNode
  /** Empty state variant */
  variant?: EmptyStateVariant
  /** Component size */
  size?: ComponentSize
  /** CSS class name */
  className?: string
}

/** Legacy ComponentIcon props for backward compatibility */
export interface ComponentIconProps extends BaseElementProps<HTMLDivElement> {
  /** Component type */
  type: ElectricalComponentType | string
  /** Icon size */
  size?: IconSize
  /** Icon color */
  color?: IconColor
  /** Custom icon component */
  customIcon?: React.ComponentType<{ className?: string }>
  /** Test ID */
  "data-testid"?: string
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

/** Extract size type from component props */
export type ExtractSize<T> = T extends { size?: infer S } ? S : never

/** Extract variant type from component props */
export type ExtractVariant<T> = T extends { variant?: infer V } ? V : never

/** Make all properties optional except specified ones */
export type PartialExcept<T, K extends keyof T> = Partial<T> & Pick<T, K>

/** Component ref types */
export type ComponentRef<T extends keyof JSX.IntrinsicElements> =
  React.ComponentRef<T>

/** Forward ref component type */
export type ForwardRefComponent<T, P> = React.ForwardRefExoticComponent<
  P & React.RefAttributes<T>
>

// =============================================================================
// CONFIGURATION TYPES
// =============================================================================

/** Badge type configuration structure */
export interface BadgeTypeConfig {
  [key: string]: {
    label: string
    icon: React.ComponentType<{ className?: string }>
  }
}

/** Action configuration structure */
export interface ActionConfigItem {
  icon: React.ComponentType<{ className?: string }>
  label: string
  variant: ButtonVariant
  color: string
}

/** Size configuration structure */
export interface SizeConfig {
  [key: string]: {
    [property: string]: string
  }
}

/** Icon mapping structure */
export interface IconMapping {
  [key: string]: React.ComponentType<{ className?: string }>
}

// =============================================================================
// HOOKS & UTILITIES TYPES
// =============================================================================

/** Component badge hook return type */
export interface ComponentBadgeHookResult {
  type: "component"
  intent: ComponentStatus
}

/** Status badge hook return type */
export interface StatusBadgeHookResult {
  type: "status"
  intent: ProjectStatus
}

/** Priority badge hook return type */
export interface PriorityBadgeHookResult {
  type: "priority"
  intent: ProjectPriority
}

/** Component icon hook return type */
export interface ComponentIconHookResult {
  type: string
}

/** Field validation result */
export interface FieldValidationResult {
  isValid: boolean
  error?: string
}

// =============================================================================
// EXPORTS
// =============================================================================

// Re-export all component prop types
export type {
  UnifiedBadgeProps,
  UnifiedButtonProps,
  UnifiedFormFieldProps,
  UnifiedLoadingProps,
  UnifiedEmptyStateProps,
  UnifiedErrorStateProps,
  UnifiedIconProps,
}

// Export legacy compatibility types
export type {
  ComponentBadgeProps,
  StatusBadgeProps,
  PriorityBadgeProps,
  ActionButtonProps,
  FormInputProps,
  LoadingSpinnerProps,
  EmptyStateProps,
  ComponentIconProps,
}

// Export utility types
export type {
  ComponentSize,
  ExtendedSize,
  ColorIntention,
  ComponentState,
  BadgeType,
  BadgeVariant,
  BadgeIntent,
  ActionType,
  ButtonDisplay,
  ButtonVariant,
  ButtonSize,
  FieldType,
  ValidationState,
  FormFieldSize,
  FormFieldVariant,
  LoadingVariant,
  LoadingLayout,
  LoadingSize,
  EmptyStateVariant,
  EmptyStateSize,
  ElectricalComponentType,
  GeneralIconType,
  IconType,
  IconSize,
  IconColor,
}
