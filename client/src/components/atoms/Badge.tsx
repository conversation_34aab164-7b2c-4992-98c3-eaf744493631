/**
 * Badge Atom - Universal Foundational Component
 * 
 * Enhanced atomic design badge component providing comprehensive badge functionality
 * with electrical engineering domain support, consistent styling, accessibility, 
 * and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Comprehensive electrical engineering domain support
 * - Professional project status and priority variants
 * - Performance optimized with minimal footprint
 * - Full backward compatibility support
 * - Icon integration for enhanced visual communication
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import {
  AlertCircle,
  AlertTriangle,
  Archive,
  ArrowDown,
  ArrowUp,
  CheckCircle,
  FileText,
  Minus,
  Pause,
  Play,
  XCircle,
} from "lucide-react"

import { cn } from "@/lib/utils"

// Enhanced badge variants using CVA for comprehensive styling
const badgeVariants = cva(
  "inline-flex items-center gap-1 rounded-md border px-2 py-1 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
        destructive: "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
        success: "border-transparent bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        warning: "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-100",
        info: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-100",
        // Electrical engineering specific variants
        electrical: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-100",
        power: "border-transparent bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-100",
        signal: "border-transparent bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        mechanical: "border-transparent bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-100",
        thermal: "border-transparent bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-900 dark:text-orange-100",
      },
      size: {
        sm: "px-1.5 py-0.5 text-xs",
        default: "px-2 py-1 text-xs",
        lg: "px-3 py-1.5 text-sm",
      },
      intent: {
        // Component status intents
        active: "border-transparent bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        inactive: "border-transparent bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400",
        preferred: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-100",
        available: "border-transparent bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        limited: "border-transparent bg-yellow-100 text-yellow-700 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-100",
        out_of_stock: "border-transparent bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-100",
        discontinued: "border-transparent bg-gray-100 text-gray-500 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400",
        on_order: "border-transparent bg-blue-100 text-blue-700 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-100",
        
        // Project status intents
        draft: "border-transparent bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400",
        paused: "border-transparent bg-yellow-100 text-yellow-700 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-100",
        completed: "border-transparent bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        cancelled: "border-transparent bg-red-100 text-red-700 hover:bg-red-200 dark:bg-red-900 dark:text-red-100",
        
        // Priority intents
        low: "border-transparent bg-blue-50 text-blue-700 hover:bg-blue-100 dark:bg-blue-950 dark:text-blue-300",
        medium: "border-transparent bg-gray-50 text-gray-700 hover:bg-gray-100 dark:bg-gray-900 dark:text-gray-300",
        high: "border-transparent bg-orange-50 text-orange-700 hover:bg-orange-100 dark:bg-orange-950 dark:text-orange-300",
        critical: "border-transparent bg-red-50 text-red-700 hover:bg-red-100 dark:bg-red-950 dark:text-red-300",
        
        // Electrical status intents
        energized: "border-transparent bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        deenergized: "border-transparent bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400",
        fault: "border-transparent bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-100",
        overload: "border-transparent bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-900 dark:text-orange-100",
        undervoltage: "border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-900 dark:text-yellow-100",
        normal: "border-transparent bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        alarm: "border-transparent bg-red-100 text-red-800 hover:bg-red-200 dark:bg-red-900 dark:text-red-100",
        maintenance: "border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-100",
        calibration: "border-transparent bg-purple-100 text-purple-800 hover:bg-purple-200 dark:bg-purple-900 dark:text-purple-100",
        testing: "border-transparent bg-cyan-100 text-cyan-800 hover:bg-cyan-200 dark:bg-cyan-900 dark:text-cyan-100",
        commissioned: "border-transparent bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-900 dark:text-green-100",
        decommissioned: "border-transparent bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

// Configuration for different badge types with icons
const badgeIntentConfig = {
  // Component status configuration
  active: { label: "Active", icon: CheckCircle },
  inactive: { label: "Inactive", icon: XCircle },
  preferred: { label: "Preferred", icon: AlertTriangle },
  available: { label: "Available", icon: CheckCircle },
  limited: { label: "Limited Stock", icon: AlertTriangle },
  out_of_stock: { label: "Out of Stock", icon: XCircle },
  discontinued: { label: "Discontinued", icon: Archive },
  on_order: { label: "On Order", icon: FileText },
  
  // Project status configuration
  draft: { label: "Draft", icon: FileText },
  paused: { label: "Paused", icon: Pause },
  completed: { label: "Completed", icon: CheckCircle },
  cancelled: { label: "Cancelled", icon: XCircle },
  
  // Priority configuration
  low: { label: "Low", icon: ArrowDown },
  medium: { label: "Medium", icon: Minus },
  high: { label: "High", icon: ArrowUp },
  critical: { label: "Critical", icon: AlertTriangle },
  
  // Electrical status configuration
  energized: { label: "Energized", icon: CheckCircle },
  deenergized: { label: "De-energized", icon: XCircle },
  fault: { label: "Fault", icon: XCircle },
  overload: { label: "Overload", icon: AlertTriangle },
  undervoltage: { label: "Undervoltage", icon: AlertTriangle },
  normal: { label: "Normal", icon: CheckCircle },
  alarm: { label: "Alarm", icon: AlertCircle },
  maintenance: { label: "Maintenance", icon: AlertCircle },
  calibration: { label: "Calibration", icon: AlertCircle },
  testing: { label: "Testing", icon: AlertCircle },
  commissioned: { label: "Commissioned", icon: CheckCircle },
  decommissioned: { label: "Decommissioned", icon: Archive },
} as const

// Badge intent type
export type BadgeIntent = keyof typeof badgeIntentConfig

export interface BadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof badgeVariants> {
  /** Interactive badge (clickable) */
  interactive?: boolean
  /** Pulse animation */
  pulse?: boolean
  /** Show icon with badge */
  showIcon?: boolean
  /** Show label text */
  showLabel?: boolean
  /** Custom label text override */
  customLabel?: string
  /** Custom icon component */
  customIcon?: React.ComponentType<{ className?: string }>
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  (
    {
      className,
      variant,
      size,
      intent,
      interactive = false,
      pulse = false,
      showIcon = false,
      showLabel = true,
      customLabel,
      customIcon: CustomIcon,
      role = "status",
      tabIndex,
      children,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    // Get configuration for intent-based badge
    const config = intent ? badgeIntentConfig[intent] : null
    const Icon = CustomIcon || (config?.icon)
    const label = customLabel || (config?.label) || children

    return (
      <span
        ref={ref}
        className={cn(
          badgeVariants({ variant, size, intent }),
          interactive && "cursor-pointer hover:opacity-80 focus:ring-2 focus:ring-ring focus:ring-offset-2",
          pulse && "animate-pulse",
          className
        )}
        role={role}
        tabIndex={interactive ? (tabIndex ?? 0) : undefined}
        aria-label={intent ? `${intent}: ${label}` : undefined}
        data-testid={testId || (intent && `badge-${intent}`)}
        {...props}
      >
        {showIcon && Icon && <Icon className="h-3 w-3" aria-hidden="true" />}
        {showLabel && label && <span>{label}</span>}
        {!intent && children}
      </span>
    )
  }
)

Badge.displayName = "Badge"

// Convenience components for common use cases

// Component status badges
export const ComponentStatusBadge = React.forwardRef<
  HTMLSpanElement,
  Omit<BadgeProps, "intent"> & { status: Extract<BadgeIntent, "active" | "inactive" | "preferred" | "available" | "limited" | "out_of_stock" | "discontinued" | "on_order"> }
>(({ status, ...props }, ref) => (
  <Badge ref={ref} intent={status} showIcon {...props} />
))
ComponentStatusBadge.displayName = "ComponentStatusBadge"

// Project status badges
export const ProjectStatusBadge = React.forwardRef<
  HTMLSpanElement,
  Omit<BadgeProps, "intent"> & { status: Extract<BadgeIntent, "draft" | "active" | "paused" | "completed" | "cancelled"> }
>(({ status, ...props }, ref) => (
  <Badge ref={ref} intent={status} showIcon {...props} />
))
ProjectStatusBadge.displayName = "ProjectStatusBadge"

// Priority badges
export const PriorityBadge = React.forwardRef<
  HTMLSpanElement,
  Omit<BadgeProps, "intent"> & { priority: Extract<BadgeIntent, "low" | "medium" | "high" | "critical"> }
>(({ priority, ...props }, ref) => (
  <Badge ref={ref} intent={priority} showIcon {...props} />
))
PriorityBadge.displayName = "PriorityBadge"

// Electrical status badges
export const ElectricalStatusBadge = React.forwardRef<
  HTMLSpanElement,
  Omit<BadgeProps, "intent"> & { 
    status: Extract<BadgeIntent, "energized" | "deenergized" | "fault" | "overload" | "undervoltage" | "normal" | "alarm" | "maintenance" | "calibration" | "testing" | "commissioned" | "decommissioned"> 
  }
>(({ status, ...props }, ref) => (
  <Badge ref={ref} intent={status} variant="electrical" showIcon {...props} />
))
ElectricalStatusBadge.displayName = "ElectricalStatusBadge"

// Individual convenience components for common statuses
export const ActiveBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="active" showIcon {...props} />
)

export const InactiveBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="inactive" showIcon {...props} />
)

export const PreferredBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="preferred" showIcon {...props} />
)

export const CompletedBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="completed" showIcon {...props} />
)

export const DraftBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="draft" showIcon {...props} />
)

export const HighPriorityBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="high" showIcon {...props} />
)

export const CriticalPriorityBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="critical" showIcon {...props} />
)

export const EnergizedBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="energized" variant="electrical" showIcon {...props} />
)

export const FaultBadge = (props: Omit<BadgeProps, "intent">) => (
  <Badge intent="fault" variant="power" showIcon {...props} />
)

// Utility functions

export const getBadgeConfig = (intent: BadgeIntent) => {
  return badgeIntentConfig[intent]
}

export const isValidBadgeIntent = (intent: string): intent is BadgeIntent => {
  return intent in badgeIntentConfig
}

export const getBadgeIntentsByCategory = () => {
  return {
    component: ["active", "inactive", "preferred", "available", "limited", "out_of_stock", "discontinued", "on_order"],
    project: ["draft", "active", "paused", "completed", "cancelled"],
    priority: ["low", "medium", "high", "critical"],
    electrical: ["energized", "deenergized", "fault", "overload", "undervoltage", "normal", "alarm", "maintenance", "calibration", "testing", "commissioned", "decommissioned"],
  } as const
}

// Hook for getting badge props from component data (backward compatibility)
export const useComponentBadgeProps = (component: {
  is_active?: boolean
  is_preferred?: boolean
  stock_status?: string
}): { intent: BadgeIntent } => {
  if (!component.is_active) {
    return { intent: "inactive" }
  }
  
  if (component.is_preferred) {
    return { intent: "preferred" }
  }
  
  // Map stock status to badge intent
  const stockStatusMap: Record<string, BadgeIntent> = {
    available: "available",
    limited: "limited",
    out_of_stock: "out_of_stock",
    discontinued: "discontinued",
    on_order: "on_order",
  }
  
  const intent = component.stock_status
    ? stockStatusMap[component.stock_status] || "active"
    : "active"
  
  return { intent }
}

export const useStatusBadgeProps = (status: string): { intent: BadgeIntent } => {
  const normalizedStatus = status.toLowerCase()
  const validStatuses = getBadgeIntentsByCategory().project
  
  if (validStatuses.includes(normalizedStatus as any)) {
    return { intent: normalizedStatus as BadgeIntent }
  }
  
  return { intent: "draft" }
}

export const usePriorityBadgeProps = (priority: string): { intent: BadgeIntent } => {
  const normalizedPriority = priority.toLowerCase()
  const validPriorities = getBadgeIntentsByCategory().priority
  
  if (validPriorities.includes(normalizedPriority as any)) {
    return { intent: normalizedPriority as BadgeIntent }
  }
  
  return { intent: "medium" }
}

export const useElectricalBadgeProps = (status: string): { intent: BadgeIntent } => {
  const normalizedStatus = status.toLowerCase()
  const validStatuses = getBadgeIntentsByCategory().electrical
  
  if (validStatuses.includes(normalizedStatus as any)) {
    return { intent: normalizedStatus as BadgeIntent }
  }
  
  return { intent: "normal" }
}

// Export types for external use
export type BadgeVariant = NonNullable<BadgeProps["variant"]>
export type BadgeSize = NonNullable<BadgeProps["size"]>

// Export variants and config for external use
export { badgeVariants, badgeIntentConfig }