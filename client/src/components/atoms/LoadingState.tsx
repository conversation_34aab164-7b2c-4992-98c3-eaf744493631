/**
 * LoadingState Atom - Universal Foundational Component
 * 
 * Enhanced atomic design loading state component providing comprehensive loading functionality
 * with multiple variants, accessibility, and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Multiple loading animation variants (spinner, pulse, dots, bars)
 * - Flexible layout options and size configurations
 * - Performance optimized with conditional rendering
 * - Full backward compatibility support
 * - Professional loading patterns for electrical applications
 */

import React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import {
  Loader2,
  RefreshCw,
} from "lucide-react"

import { cn } from "@/lib/utils"

// Loading variants using CVA
const loadingVariants = cva(
  "flex items-center transition-opacity",
  {
    variants: {
      size: {
        xs: "gap-1",
        sm: "gap-1.5", 
        default: "gap-2",
        lg: "gap-3",
        xl: "gap-4",
      },
      layout: {
        inline: "inline-flex",
        center: "justify-center",
        left: "justify-start",
        right: "justify-end",
      },
    },
    defaultVariants: {
      size: "default",
      layout: "center",
    },
  }
)

// Size configurations
const loadingSizeConfig = {
  xs: {
    icon: "h-3 w-3",
    text: "text-xs",
  },
  sm: {
    icon: "h-4 w-4", 
    text: "text-sm",
  },
  default: {
    icon: "h-5 w-5",
    text: "text-sm",
  },
  lg: {
    icon: "h-6 w-6",
    text: "text-base",
  },
  xl: {
    icon: "h-8 w-8", 
    text: "text-lg",
  },
} as const

// Loading spinner types
export type LoadingVariant = "spinner" | "pulse" | "dots" | "bars" | "refresh"
export type LoadingSize = keyof typeof loadingSizeConfig
export type LoadingLayout = "inline" | "center" | "left" | "right"

export interface LoadingStateProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingVariants> {
  /** Loading animation variant */
  variant?: LoadingVariant
  /** Loading text */
  text?: string
  /** Show text with loading indicator */
  showText?: boolean
  /** Test ID for testing */
  "data-testid"?: string
}

export const LoadingState = React.forwardRef<
  HTMLDivElement,
  LoadingStateProps
>(
  (
    {
      variant = "spinner",
      size = "default", 
      layout = "center",
      text,
      showText = true,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const sizeStyles = loadingSizeConfig[size]
    
    const renderLoadingIcon = () => {
      const iconClasses = cn(
        sizeStyles.icon,
        "text-muted-foreground",
        variant !== "pulse" && "animate-spin"
      )
      
      switch (variant) {
        case "refresh":
          return <RefreshCw className={iconClasses} />
        case "pulse":
          return <div className={cn(sizeStyles.icon, "bg-current rounded-full animate-pulse opacity-60")} />
        case "dots":
          return (
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className={cn("bg-current rounded-full animate-bounce", 
                    size === "xs" ? "h-1 w-1" : 
                    size === "sm" ? "h-1.5 w-1.5" :
                    size === "default" ? "h-2 w-2" :
                    size === "lg" ? "h-2.5 w-2.5" : "h-3 w-3"
                  )}
                  style={{ animationDelay: `${i * 0.1}s` }}
                />
              ))}
            </div>
          )
        case "bars":
          return (
            <div className="flex space-x-1 items-end">
              {[0, 1, 2, 3].map((i) => (
                <div
                  key={i}
                  className={cn("bg-current animate-pulse",
                    size === "xs" ? "w-0.5" : 
                    size === "sm" ? "w-0.5" :
                    size === "default" ? "w-1" :
                    size === "lg" ? "w-1" : "w-1.5",
                    `h-${2 + i}`
                  )}
                  style={{ animationDelay: `${i * 0.15}s` }}
                />
              ))}
            </div>
          )
        default:
          return <Loader2 className={iconClasses} />
      }
    }

    return (
      <div
        ref={ref}
        className={cn(loadingVariants({ size, layout }), className)}
        role="status"
        aria-label={text || "Loading"}
        data-testid={testId || `loading-state-${variant}`}
        {...props}
      >
        {renderLoadingIcon()}
        {showText && text && (
          <span className={cn(sizeStyles.text, "text-muted-foreground")}>
            {text}
          </span>
        )}
      </div>
    )
  }
)

LoadingState.displayName = "LoadingState"

// Convenience components

export const LoadingSpinner = React.forwardRef<
  HTMLDivElement,
  Omit<LoadingStateProps, "layout"> & { inline?: boolean }
>(({ inline, ...props }, ref) => (
  <LoadingState ref={ref} layout={inline ? "inline" : "center"} {...props} />
))
LoadingSpinner.displayName = "LoadingSpinner"

export const LoadingOverlay: React.FC<{
  text?: string
  variant?: LoadingVariant
  size?: LoadingSize
  className?: string
}> = ({ text = "Loading...", variant, size = "lg", className }) => (
  <div className={cn("flex items-center justify-center py-8", className)}>
    <LoadingState variant={variant} size={size} text={text} />
  </div>
)

export const InlineLoading: React.FC<{
  text?: string
  variant?: LoadingVariant
  size?: LoadingSize
}> = ({ text, variant, size = "sm" }) => (
  <LoadingState variant={variant} size={size} text={text} layout="inline" />
)

// Electrical engineering specific loading states
export const SystemCalculationLoading = (props: Omit<LoadingStateProps, "text" | "variant">) => (
  <LoadingState text="Calculating electrical parameters..." variant="bars" {...props} />
)

export const ValidationProcessLoading = (props: Omit<LoadingStateProps, "text" | "variant">) => (
  <LoadingState text="Validating electrical specifications..." variant="refresh" {...props} />
)

export const ComponentAnalysisLoading = (props: Omit<LoadingStateProps, "text" | "variant">) => (
  <LoadingState text="Analyzing component compatibility..." variant="spinner" {...props} />
)

// Export types for external use
export type LoadingStateSize = LoadingSize
export type LoadingStateVariant = LoadingVariant
export type LoadingStateLayout = LoadingLayout

// Export configuration for external use
export { loadingSizeConfig, loadingVariants }