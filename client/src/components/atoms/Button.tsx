/**
 * Button Atom - Universal Foundational Component
 * 
 * Enhanced atomic design button component providing comprehensive button functionality
 * with electrical engineering action support, consistent styling, accessibility, 
 * and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Comprehensive electrical engineering action support
 * - Professional action-based button system
 * - Performance optimized with minimal footprint
 * - Full backward compatibility support
 * - Icon integration and flexible display modes
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import { Slot } from "@radix-ui/react-slot"
import {
  AlertCircle,
  AlertTriangle,
  Archive,
  ArrowLeft,
  ArrowRight,
  Bell,
  BellOff,
  BookOpen,
  Calendar,
  CheckCircle,
  Copy,
  Download,
  Edit,
  Edit3,
  Eye,
  EyeOff,
  FileText,
  Filter,
  Heart,
  HeartOff,
  Home,
  Link,
  Loader2,
  Lock,
  LogOut,
  Mail,
  MoreHorizontal,
  MoreVertical,
  Pause,
  Play,
  Plus,
  PlusCircle,
  Refresh,
  RefreshCw,
  Save,
  Search,
  Settings,
  Share,
  Shield,
  Star,
  <PERSON>Off,
  Trash2,
  <PERSON>lock,
  Upload,
  User,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>r<PERSON>inus,
  UserPlus,
  Users,
  XCircle,
  Zap,
} from "lucide-react"

import { cn } from "@/lib/utils"

// Button variants using CVA for consistent styling
const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",
        destructive:
          "bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",
        outline:
          "border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "size-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

// Enhanced action types with comprehensive coverage including electrical engineering
export type ActionType =
  // Core CRUD actions
  | "create" | "read" | "update" | "delete"
  | "add" | "remove" | "edit" | "view"
  
  // File operations
  | "save" | "download" | "upload" | "copy"
  | "share" | "export" | "import" 
  
  // State changes
  | "activate" | "deactivate" | "pause" | "resume"
  | "play" | "stop" | "complete" | "cancel"
  | "archive" | "restore" | "refresh"
  
  // User management
  | "add-user" | "remove-user" | "invite-user"
  | "assign" | "unassign" | "approve" | "reject"
  
  // Navigation
  | "back" | "forward" | "next" | "previous"
  | "home" | "goto" | "link"
  
  // Interaction
  | "more" | "more-vertical" | "expand" | "collapse"
  | "filter" | "search" | "sort"
  
  // Notifications & Communication
  | "notify" | "unnotify" | "bell" | "mail"
  | "subscribe" | "unsubscribe"
  
  // Favorites & Bookmarks
  | "favorite" | "unfavorite" | "star" | "unstar"
  | "bookmark" | "unbookmark" | "like" | "unlike"
  
  // Security
  | "lock" | "unlock" | "secure" | "login" | "logout"
  
  // Settings & Configuration
  | "settings" | "configure" | "customize"
  
  // Documentation
  | "help" | "info" | "docs" | "guide"
  
  // Calendar & Scheduling
  | "schedule" | "calendar" | "event"
  
  // Visibility
  | "show" | "hide" | "preview" | "toggle"
  
  // Power & Performance
  | "boost" | "optimize" | "enhance"
  
  // Electrical Engineering Actions
  | "energize" | "deenergize" | "isolate" | "test"
  | "calibrate" | "measure" | "monitor" | "analyze"
  | "simulate" | "calculate" | "validate" | "inspect"
  | "commission" | "decommission" | "maintain"
  | "report" | "certify" | "approve-electrical"

// Action configuration with icons, labels, and default styling
const actionConfig = {
  // Core CRUD actions
  create: { icon: Plus, label: "Create", variant: "default" as const },
  add: { icon: PlusCircle, label: "Add", variant: "default" as const },
  read: { icon: Eye, label: "Read", variant: "ghost" as const },
  view: { icon: Eye, label: "View", variant: "ghost" as const },
  update: { icon: Edit, label: "Update", variant: "outline" as const },
  edit: { icon: Edit3, label: "Edit", variant: "outline" as const },
  delete: { icon: Trash2, label: "Delete", variant: "destructive" as const },
  remove: { icon: XCircle, label: "Remove", variant: "destructive" as const },
  
  // File operations
  save: { icon: Save, label: "Save", variant: "default" as const },
  download: { icon: Download, label: "Download", variant: "outline" as const },
  upload: { icon: Upload, label: "Upload", variant: "outline" as const },
  copy: { icon: Copy, label: "Copy", variant: "ghost" as const },
  share: { icon: Share, label: "Share", variant: "outline" as const },
  export: { icon: Download, label: "Export", variant: "outline" as const },
  import: { icon: Upload, label: "Import", variant: "outline" as const },
  
  // State changes
  activate: { icon: Play, label: "Activate", variant: "outline" as const },
  deactivate: { icon: Pause, label: "Deactivate", variant: "outline" as const },
  pause: { icon: Pause, label: "Pause", variant: "outline" as const },
  resume: { icon: Play, label: "Resume", variant: "outline" as const },
  play: { icon: Play, label: "Play", variant: "outline" as const },
  stop: { icon: Pause, label: "Stop", variant: "outline" as const },
  complete: { icon: CheckCircle, label: "Complete", variant: "outline" as const },
  cancel: { icon: XCircle, label: "Cancel", variant: "destructive" as const },
  archive: { icon: Archive, label: "Archive", variant: "outline" as const },
  restore: { icon: RefreshCw, label: "Restore", variant: "outline" as const },
  refresh: { icon: Refresh, label: "Refresh", variant: "ghost" as const },
  
  // User management
  "add-user": { icon: UserPlus, label: "Add User", variant: "outline" as const },
  "remove-user": { icon: UserMinus, label: "Remove User", variant: "destructive" as const },
  "invite-user": { icon: UserPlus, label: "Invite User", variant: "default" as const },
  assign: { icon: UserCheck, label: "Assign", variant: "outline" as const },
  unassign: { icon: UserMinus, label: "Unassign", variant: "outline" as const },
  approve: { icon: CheckCircle, label: "Approve", variant: "default" as const },
  reject: { icon: XCircle, label: "Reject", variant: "destructive" as const },
  
  // Navigation
  back: { icon: ArrowLeft, label: "Back", variant: "ghost" as const },
  forward: { icon: ArrowRight, label: "Forward", variant: "ghost" as const },
  next: { icon: ArrowRight, label: "Next", variant: "outline" as const },
  previous: { icon: ArrowLeft, label: "Previous", variant: "outline" as const },
  home: { icon: Home, label: "Home", variant: "ghost" as const },
  goto: { icon: ArrowRight, label: "Go To", variant: "ghost" as const },
  link: { icon: Link, label: "Link", variant: "ghost" as const },
  
  // Interaction
  more: { icon: MoreHorizontal, label: "More", variant: "ghost" as const },
  "more-vertical": { icon: MoreVertical, label: "More", variant: "ghost" as const },
  expand: { icon: ArrowRight, label: "Expand", variant: "ghost" as const },
  collapse: { icon: ArrowLeft, label: "Collapse", variant: "ghost" as const },
  filter: { icon: Filter, label: "Filter", variant: "outline" as const },
  search: { icon: Search, label: "Search", variant: "outline" as const },
  sort: { icon: ArrowRight, label: "Sort", variant: "ghost" as const },
  
  // Notifications & Communication
  notify: { icon: Bell, label: "Notify", variant: "outline" as const },
  unnotify: { icon: BellOff, label: "Turn Off", variant: "ghost" as const },
  bell: { icon: Bell, label: "Notifications", variant: "ghost" as const },
  mail: { icon: Mail, label: "Email", variant: "outline" as const },
  subscribe: { icon: Bell, label: "Subscribe", variant: "outline" as const },
  unsubscribe: { icon: BellOff, label: "Unsubscribe", variant: "ghost" as const },
  
  // Favorites & Bookmarks
  favorite: { icon: Heart, label: "Favorite", variant: "ghost" as const },
  unfavorite: { icon: HeartOff, label: "Unfavorite", variant: "ghost" as const },
  star: { icon: Star, label: "Star", variant: "ghost" as const },
  unstar: { icon: StarOff, label: "Unstar", variant: "ghost" as const },
  bookmark: { icon: Star, label: "Bookmark", variant: "ghost" as const },
  unbookmark: { icon: StarOff, label: "Remove Bookmark", variant: "ghost" as const },
  like: { icon: Heart, label: "Like", variant: "ghost" as const },
  unlike: { icon: HeartOff, label: "Unlike", variant: "ghost" as const },
  
  // Security
  lock: { icon: Lock, label: "Lock", variant: "outline" as const },
  unlock: { icon: Unlock, label: "Unlock", variant: "outline" as const },
  secure: { icon: Shield, label: "Secure", variant: "outline" as const },
  login: { icon: User, label: "Login", variant: "default" as const },
  logout: { icon: LogOut, label: "Logout", variant: "ghost" as const },
  
  // Settings & Configuration
  settings: { icon: Settings, label: "Settings", variant: "ghost" as const },
  configure: { icon: Settings, label: "Configure", variant: "outline" as const },
  customize: { icon: Edit, label: "Customize", variant: "outline" as const },
  
  // Documentation
  help: { icon: BookOpen, label: "Help", variant: "ghost" as const },
  info: { icon: AlertCircle, label: "Info", variant: "ghost" as const },
  docs: { icon: FileText, label: "Documentation", variant: "ghost" as const },
  guide: { icon: BookOpen, label: "Guide", variant: "ghost" as const },
  
  // Calendar & Scheduling
  schedule: { icon: Calendar, label: "Schedule", variant: "outline" as const },
  calendar: { icon: Calendar, label: "Calendar", variant: "ghost" as const },
  event: { icon: Calendar, label: "Event", variant: "outline" as const },
  
  // Visibility
  show: { icon: Eye, label: "Show", variant: "ghost" as const },
  hide: { icon: EyeOff, label: "Hide", variant: "ghost" as const },
  preview: { icon: Eye, label: "Preview", variant: "outline" as const },
  toggle: { icon: RefreshCw, label: "Toggle", variant: "ghost" as const },
  
  // Power & Performance
  boost: { icon: Zap, label: "Boost", variant: "default" as const },
  optimize: { icon: Zap, label: "Optimize", variant: "outline" as const },
  enhance: { icon: Star, label: "Enhance", variant: "outline" as const },
  
  // Electrical Engineering Actions
  energize: { icon: Zap, label: "Energize", variant: "default" as const },
  deenergize: { icon: Zap, label: "De-energize", variant: "destructive" as const },
  isolate: { icon: Shield, label: "Isolate", variant: "outline" as const },
  test: { icon: CheckCircle, label: "Test", variant: "outline" as const },
  calibrate: { icon: Settings, label: "Calibrate", variant: "outline" as const },
  measure: { icon: Search, label: "Measure", variant: "ghost" as const },
  monitor: { icon: Eye, label: "Monitor", variant: "ghost" as const },
  analyze: { icon: Search, label: "Analyze", variant: "outline" as const },
  simulate: { icon: RefreshCw, label: "Simulate", variant: "outline" as const },
  calculate: { icon: Settings, label: "Calculate", variant: "outline" as const },
  validate: { icon: CheckCircle, label: "Validate", variant: "outline" as const },
  inspect: { icon: Eye, label: "Inspect", variant: "outline" as const },
  commission: { icon: CheckCircle, label: "Commission", variant: "default" as const },
  decommission: { icon: XCircle, label: "Decommission", variant: "destructive" as const },
  maintain: { icon: Settings, label: "Maintain", variant: "outline" as const },
  report: { icon: FileText, label: "Report", variant: "outline" as const },
  certify: { icon: CheckCircle, label: "Certify", variant: "default" as const },
  "approve-electrical": { icon: CheckCircle, label: "Approve", variant: "default" as const },
} as const

// Display mode options
export type ButtonDisplay = "icon" | "text" | "both" | "icon-text" | "text-icon"

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  /** Render as a different component while preserving button styling */
  asChild?: boolean
  /** Show loading state with spinner */
  loading?: boolean
  /** Custom loading text to display */
  loadingText?: string
  /** Make button full width */
  fullWidth?: boolean
  /** Action type for automatic icon and label */
  action?: ActionType
  /** Display mode for icon and text */
  display?: ButtonDisplay
  /** Custom label text override */
  customLabel?: string
  /** Custom icon component */
  customIcon?: React.ComponentType<{ className?: string }>
  /** Icon position when showing both icon and text */
  iconPosition?: "left" | "right"
  /** Test identifier for automated testing */
  "data-testid"?: string
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant: variantOverride,
      size,
      asChild = false,
      loading = false,
      loadingText,
      fullWidth = false,
      disabled = false,
      action,
      display = "text",
      customLabel,
      customIcon: CustomIcon,
      iconPosition = "left",
      children,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : "button"
    const isDisabled = disabled || loading

    // Get configuration for action-based button
    const config = action ? actionConfig[action] : null
    const variant = variantOverride || (config?.variant) || "default"
    const Icon = CustomIcon || (config?.icon)
    const label = customLabel || (config?.label) || children

    // Display mode logic
    const isIconOnly = display === "icon"
    const showText = display === "text" || display === "both" || display === "icon-text" || display === "text-icon"
    const showIcon = display === "icon" || display === "both" || display === "icon-text" || display === "text-icon"
    const iconFirst = display !== "text-icon" && iconPosition === "left"

    return (
      <Comp
        ref={ref}
        className={cn(
          buttonVariants({ variant, size }),
          fullWidth && "w-full",
          loading && "cursor-not-allowed",
          isIconOnly && size !== "icon" && "aspect-square p-0",
          className
        )}
        disabled={isDisabled}
        aria-disabled={isDisabled}
        aria-label={isIconOnly ? (typeof label === "string" ? label : action) : undefined}
        data-testid={testId || (action && `button-${action}`)}
        {...props}
      >
        {loading && (
          <>
            <Loader2 className="size-4 animate-spin" aria-hidden="true" />
            {loadingText ? (
              <span className="truncate">{loadingText}</span>
            ) : (
              showText && label && <span className="truncate">{label}</span>
            )}
          </>
        )}
        
        {!loading && showIcon && Icon && iconFirst && (
          <Icon className={cn("size-4", showText && "mr-2")} aria-hidden="true" />
        )}

        {!loading && showText && (
          <span className="truncate">{label}</span>
        )}

        {!loading && showIcon && Icon && !iconFirst && (
          <Icon className={cn("size-4", showText && "ml-2")} aria-hidden="true" />
        )}

        {!loading && !action && children}
      </Comp>
    )
  }
)

Button.displayName = "Button"

// Convenience components for common actions

export const ActionButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (props, ref) => <Button ref={ref} {...props} />
)
ActionButton.displayName = "ActionButton"

export const CreateButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="create" display="both" {...props} />
)

export const EditButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="edit" display="icon" variant="outline" {...props} />
)

export const DeleteButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="delete" display="icon" variant="destructive" {...props} />
)

export const SaveButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="save" display="both" {...props} />
)

export const CancelButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="cancel" display="text" variant="ghost" {...props} />
)

export const MoreButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="more" display="icon" variant="ghost" {...props} />
)

export const BackButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="back" display="both" variant="ghost" {...props} />
)

export const NextButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="next" display="both" {...props} />
)

// Electrical engineering convenience components
export const EnergizeButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="energize" display="both" {...props} />
)

export const TestButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="test" display="both" variant="outline" {...props} />
)

export const CalibrateButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="calibrate" display="both" variant="outline" {...props} />
)

export const CommissionButton = (props: Omit<ButtonProps, "action">) => (
  <Button action="commission" display="both" {...props} />
)

// Utility functions

export const getActionConfig = (action: ActionType) => {
  return actionConfig[action]
}

export const isValidActionType = (action: string): action is ActionType => {
  return Object.keys(actionConfig).includes(action)
}

export const getActionsByCategory = () => {
  return {
    crud: ["create", "read", "update", "delete", "add", "remove", "edit", "view"],
    file: ["save", "download", "upload", "copy", "share", "export", "import"],
    state: ["activate", "deactivate", "pause", "resume", "play", "stop", "complete", "cancel", "archive", "restore", "refresh"],
    user: ["add-user", "remove-user", "invite-user", "assign", "unassign", "approve", "reject"],
    navigation: ["back", "forward", "next", "previous", "home", "goto", "link"],
    interaction: ["more", "more-vertical", "expand", "collapse", "filter", "search", "sort"],
    notification: ["notify", "unnotify", "bell", "mail", "subscribe", "unsubscribe"],
    favorites: ["favorite", "unfavorite", "star", "unstar", "bookmark", "unbookmark", "like", "unlike"],
    security: ["lock", "unlock", "secure", "login", "logout"],
    settings: ["settings", "configure", "customize"],
    documentation: ["help", "info", "docs", "guide"],
    scheduling: ["schedule", "calendar", "event"],
    visibility: ["show", "hide", "preview", "toggle"],
    performance: ["boost", "optimize", "enhance"],
    electrical: ["energize", "deenergize", "isolate", "test", "calibrate", "measure", "monitor", "analyze", "simulate", "calculate", "validate", "inspect", "commission", "decommission", "maintain", "report", "certify", "approve-electrical"],
  }
}

// Export types for external use
export type ButtonVariant = NonNullable<ButtonProps["variant"]>
export type ButtonSize = NonNullable<ButtonProps["size"]>

// Export variants and config for external use
export { buttonVariants, actionConfig }