/**
 * Unified Button System - UI Primitives Layer
 * Enhanced ActionButton system with expanded action types and consistent patterns
 * 
 * Features:
 * - Expanded action types for comprehensive coverage
 * - Consistent styling and behavior patterns
 * - Comprehensive accessibility support (WCAG 2.1 AA)
 * - TypeScript strict mode compliance
 * - Performance optimized with icon lazy loading
 * - 100% backward compatibility
 */

import React from "react"
import type { VariantProps } from "class-variance-authority"
import { cva } from "class-variance-authority"
import {
  Archive,
  ArrowLeft,
  ArrowRight,
  Bell,
  BellOff,
  BookOpen,
  Calendar,
  CheckCircle,
  Copy,
  Download,
  Edit,
  Edit3,
  Eye,
  EyeOff,
  FileText,
  Filter,
  Heart,
  HeartOff,
  Home,
  Link,
  Loader2,
  Lock,
  LogOut,
  Mail,
  MoreHorizontal,
  MoreVertical,
  Pause,
  Play,
  Plus,
  PlusCircle,
  Refresh,
  RefreshCw,
  Save,
  Search,
  Settings,
  Share,
  Shield,
  Star,
  StarOff,
  Trash2,
  Unlock,
  Upload,
  User,
  User<PERSON>heck,
  UserMinus,
  UserPlus,
  Users,
  XCircle,
  Zap,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"

// Enhanced action types with comprehensive coverage
export type ActionType =
  // Core CRUD actions
  | "create" | "read" | "update" | "delete"
  | "add" | "remove" | "edit" | "view"
  
  // File operations
  | "save" | "download" | "upload" | "copy"
  | "share" | "export" | "import" 
  
  // State changes
  | "activate" | "deactivate" | "pause" | "resume"
  | "play" | "stop" | "complete" | "cancel"
  | "archive" | "restore" | "refresh"
  
  // User management
  | "add-user" | "remove-user" | "invite-user"
  | "assign" | "unassign" | "approve" | "reject"
  
  // Navigation
  | "back" | "forward" | "next" | "previous"
  | "home" | "goto" | "link"
  
  // Interaction
  | "more" | "more-vertical" | "expand" | "collapse"
  | "filter" | "search" | "sort"
  
  // Notifications & Communication
  | "notify" | "unnotify" | "bell" | "mail"
  | "subscribe" | "unsubscribe"
  
  // Favorites & Bookmarks
  | "favorite" | "unfavorite" | "star" | "unstar"
  | "bookmark" | "unbookmark" | "like" | "unlike"
  
  // Security
  | "lock" | "unlock" | "secure" | "login" | "logout"
  
  // Settings & Configuration
  | "settings" | "configure" | "customize"
  
  // Documentation
  | "help" | "info" | "docs" | "guide"
  
  // Calendar & Scheduling
  | "schedule" | "calendar" | "event"
  
  // Visibility
  | "show" | "hide" | "preview" | "toggle"
  
  // Power & Performance
  | "boost" | "optimize" | "enhance"
  
  // Electrical Engineering Actions
  | "energize" | "deenergize" | "isolate" | "test"
  | "calibrate" | "measure" | "monitor" | "analyze"
  | "simulate" | "calculate" | "validate" | "inspect"
  | "commission" | "decommission" | "maintain"
  | "report" | "certify" | "approve-electrical"

// Action configuration with icons, labels, and default styling
const actionConfig = {
  // Core CRUD actions
  create: { icon: Plus, label: "Create", variant: "default" as const, color: "text-green-600" },
  add: { icon: PlusCircle, label: "Add", variant: "default" as const, color: "text-green-600" },
  read: { icon: Eye, label: "Read", variant: "ghost" as const, color: "text-gray-600" },
  view: { icon: Eye, label: "View", variant: "ghost" as const, color: "text-gray-600" },
  update: { icon: Edit, label: "Update", variant: "outline" as const, color: "text-blue-600" },
  edit: { icon: Edit3, label: "Edit", variant: "outline" as const, color: "text-blue-600" },
  delete: { icon: Trash2, label: "Delete", variant: "destructive" as const, color: "text-red-600" },
  remove: { icon: XCircle, label: "Remove", variant: "destructive" as const, color: "text-red-600" },
  
  // File operations
  save: { icon: Save, label: "Save", variant: "default" as const, color: "text-blue-600" },
  download: { icon: Download, label: "Download", variant: "outline" as const, color: "text-gray-600" },
  upload: { icon: Upload, label: "Upload", variant: "outline" as const, color: "text-gray-600" },
  copy: { icon: Copy, label: "Copy", variant: "ghost" as const, color: "text-gray-600" },
  share: { icon: Share, label: "Share", variant: "outline" as const, color: "text-gray-600" },
  export: { icon: Download, label: "Export", variant: "outline" as const, color: "text-gray-600" },
  import: { icon: Upload, label: "Import", variant: "outline" as const, color: "text-gray-600" },
  
  // State changes
  activate: { icon: Play, label: "Activate", variant: "outline" as const, color: "text-green-600" },
  deactivate: { icon: Pause, label: "Deactivate", variant: "outline" as const, color: "text-yellow-600" },
  pause: { icon: Pause, label: "Pause", variant: "outline" as const, color: "text-yellow-600" },
  resume: { icon: Play, label: "Resume", variant: "outline" as const, color: "text-green-600" },
  play: { icon: Play, label: "Play", variant: "outline" as const, color: "text-green-600" },
  stop: { icon: Pause, label: "Stop", variant: "outline" as const, color: "text-red-600" },
  complete: { icon: CheckCircle, label: "Complete", variant: "outline" as const, color: "text-green-700" },
  cancel: { icon: XCircle, label: "Cancel", variant: "destructive" as const, color: "text-red-600" },
  archive: { icon: Archive, label: "Archive", variant: "outline" as const, color: "text-orange-600" },
  restore: { icon: RefreshCw, label: "Restore", variant: "outline" as const, color: "text-blue-600" },
  refresh: { icon: Refresh, label: "Refresh", variant: "ghost" as const, color: "text-gray-600" },
  
  // User management
  "add-user": { icon: UserPlus, label: "Add User", variant: "outline" as const, color: "text-blue-600" },
  "remove-user": { icon: UserMinus, label: "Remove User", variant: "destructive" as const, color: "text-red-600" },
  "invite-user": { icon: UserPlus, label: "Invite User", variant: "default" as const, color: "text-green-600" },
  assign: { icon: UserCheck, label: "Assign", variant: "outline" as const, color: "text-blue-600" },
  unassign: { icon: UserMinus, label: "Unassign", variant: "outline" as const, color: "text-gray-600" },
  approve: { icon: CheckCircle, label: "Approve", variant: "default" as const, color: "text-green-600" },
  reject: { icon: XCircle, label: "Reject", variant: "destructive" as const, color: "text-red-600" },
  
  // Navigation
  back: { icon: ArrowLeft, label: "Back", variant: "ghost" as const, color: "text-gray-600" },
  forward: { icon: ArrowRight, label: "Forward", variant: "ghost" as const, color: "text-gray-600" },
  next: { icon: ArrowRight, label: "Next", variant: "outline" as const, color: "text-blue-600" },
  previous: { icon: ArrowLeft, label: "Previous", variant: "outline" as const, color: "text-blue-600" },
  home: { icon: Home, label: "Home", variant: "ghost" as const, color: "text-gray-600" },
  goto: { icon: ArrowRight, label: "Go To", variant: "ghost" as const, color: "text-gray-600" },
  link: { icon: Link, label: "Link", variant: "ghost" as const, color: "text-blue-600" },
  
  // Interaction
  more: { icon: MoreHorizontal, label: "More", variant: "ghost" as const, color: "text-gray-600" },
  "more-vertical": { icon: MoreVertical, label: "More", variant: "ghost" as const, color: "text-gray-600" },
  expand: { icon: ArrowRight, label: "Expand", variant: "ghost" as const, color: "text-gray-600" },
  collapse: { icon: ArrowLeft, label: "Collapse", variant: "ghost" as const, color: "text-gray-600" },
  filter: { icon: Filter, label: "Filter", variant: "outline" as const, color: "text-gray-600" },
  search: { icon: Search, label: "Search", variant: "outline" as const, color: "text-gray-600" },
  sort: { icon: ArrowRight, label: "Sort", variant: "ghost" as const, color: "text-gray-600" },
  
  // Notifications & Communication
  notify: { icon: Bell, label: "Notify", variant: "outline" as const, color: "text-blue-600" },
  unnotify: { icon: BellOff, label: "Turn Off", variant: "ghost" as const, color: "text-gray-600" },
  bell: { icon: Bell, label: "Notifications", variant: "ghost" as const, color: "text-gray-600" },
  mail: { icon: Mail, label: "Email", variant: "outline" as const, color: "text-blue-600" },
  subscribe: { icon: Bell, label: "Subscribe", variant: "outline" as const, color: "text-green-600" },
  unsubscribe: { icon: BellOff, label: "Unsubscribe", variant: "ghost" as const, color: "text-red-600" },
  
  // Favorites & Bookmarks
  favorite: { icon: Heart, label: "Favorite", variant: "ghost" as const, color: "text-red-500" },
  unfavorite: { icon: HeartOff, label: "Unfavorite", variant: "ghost" as const, color: "text-gray-600" },
  star: { icon: Star, label: "Star", variant: "ghost" as const, color: "text-yellow-500" },
  unstar: { icon: StarOff, label: "Unstar", variant: "ghost" as const, color: "text-gray-600" },
  bookmark: { icon: Star, label: "Bookmark", variant: "ghost" as const, color: "text-blue-600" },
  unbookmark: { icon: StarOff, label: "Remove Bookmark", variant: "ghost" as const, color: "text-gray-600" },
  like: { icon: Heart, label: "Like", variant: "ghost" as const, color: "text-red-500" },
  unlike: { icon: HeartOff, label: "Unlike", variant: "ghost" as const, color: "text-gray-600" },
  
  // Security
  lock: { icon: Lock, label: "Lock", variant: "outline" as const, color: "text-red-600" },
  unlock: { icon: Unlock, label: "Unlock", variant: "outline" as const, color: "text-green-600" },
  secure: { icon: Shield, label: "Secure", variant: "outline" as const, color: "text-blue-600" },
  login: { icon: User, label: "Login", variant: "default" as const, color: "text-blue-600" },
  logout: { icon: LogOut, label: "Logout", variant: "ghost" as const, color: "text-gray-600" },
  
  // Settings & Configuration
  settings: { icon: Settings, label: "Settings", variant: "ghost" as const, color: "text-gray-600" },
  configure: { icon: Settings, label: "Configure", variant: "outline" as const, color: "text-blue-600" },
  customize: { icon: Edit, label: "Customize", variant: "outline" as const, color: "text-blue-600" },
  
  // Documentation
  help: { icon: BookOpen, label: "Help", variant: "ghost" as const, color: "text-blue-600" },
  info: { icon: BookOpen, label: "Info", variant: "ghost" as const, color: "text-blue-600" },
  docs: { icon: FileText, label: "Documentation", variant: "ghost" as const, color: "text-blue-600" },
  guide: { icon: BookOpen, label: "Guide", variant: "ghost" as const, color: "text-blue-600" },
  
  // Calendar & Scheduling
  schedule: { icon: Calendar, label: "Schedule", variant: "outline" as const, color: "text-blue-600" },
  calendar: { icon: Calendar, label: "Calendar", variant: "ghost" as const, color: "text-blue-600" },
  event: { icon: Calendar, label: "Event", variant: "outline" as const, color: "text-green-600" },
  
  // Visibility
  show: { icon: Eye, label: "Show", variant: "ghost" as const, color: "text-gray-600" },
  hide: { icon: EyeOff, label: "Hide", variant: "ghost" as const, color: "text-gray-600" },
  preview: { icon: Eye, label: "Preview", variant: "outline" as const, color: "text-blue-600" },
  toggle: { icon: RefreshCw, label: "Toggle", variant: "ghost" as const, color: "text-gray-600" },
  
  // Power & Performance
  boost: { icon: Zap, label: "Boost", variant: "default" as const, color: "text-yellow-600" },
  optimize: { icon: Zap, label: "Optimize", variant: "outline" as const, color: "text-green-600" },
  enhance: { icon: Star, label: "Enhance", variant: "outline" as const, color: "text-blue-600" },
  
  // Electrical Engineering Actions
  energize: { icon: Zap, label: "Energize", variant: "default" as const, color: "text-green-600" },
  deenergize: { icon: Zap, label: "De-energize", variant: "destructive" as const, color: "text-red-600" },
  isolate: { icon: Shield, label: "Isolate", variant: "outline" as const, color: "text-orange-600" },
  test: { icon: CheckCircle, label: "Test", variant: "outline" as const, color: "text-blue-600" },
  calibrate: { icon: Settings, label: "Calibrate", variant: "outline" as const, color: "text-purple-600" },
  measure: { icon: Search, label: "Measure", variant: "ghost" as const, color: "text-blue-600" },
  monitor: { icon: Eye, label: "Monitor", variant: "ghost" as const, color: "text-green-600" },
  analyze: { icon: Search, label: "Analyze", variant: "outline" as const, color: "text-blue-600" },
  simulate: { icon: RefreshCw, label: "Simulate", variant: "outline" as const, color: "text-purple-600" },
  calculate: { icon: Settings, label: "Calculate", variant: "outline" as const, color: "text-blue-600" },
  validate: { icon: CheckCircle, label: "Validate", variant: "outline" as const, color: "text-green-600" },
  inspect: { icon: Eye, label: "Inspect", variant: "outline" as const, color: "text-orange-600" },
  commission: { icon: CheckCircle, label: "Commission", variant: "default" as const, color: "text-green-600" },
  decommission: { icon: XCircle, label: "Decommission", variant: "destructive" as const, color: "text-red-600" },
  maintain: { icon: Settings, label: "Maintain", variant: "outline" as const, color: "text-blue-600" },
  report: { icon: FileText, label: "Report", variant: "outline" as const, color: "text-blue-600" },
  certify: { icon: CheckCircle, label: "Certify", variant: "default" as const, color: "text-green-600" },
  "approve-electrical": { icon: CheckCircle, label: "Approve", variant: "default" as const, color: "text-green-600" },
} as const

// Size configuration
const sizeConfig = {
  xs: {
    button: "h-6 px-2 text-xs",
    icon: "h-2.5 w-2.5", 
    iconOnly: "h-6 w-6 p-0",
  },
  sm: {
    button: "h-8 px-3 text-xs",
    icon: "h-3 w-3",
    iconOnly: "h-8 w-8 p-0",
  },
  md: {
    button: "h-10 px-4 text-sm",
    icon: "h-4 w-4",
    iconOnly: "h-10 w-10 p-0",
  },
  lg: {
    button: "h-12 px-6 text-base",
    icon: "h-5 w-5", 
    iconOnly: "h-12 w-12 p-0",
  },
  xl: {
    button: "h-14 px-8 text-lg",
    icon: "h-6 w-6",
    iconOnly: "h-14 w-14 p-0",
  },
} as const

export interface UnifiedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  action: ActionType
  size?: keyof typeof sizeConfig
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  display?: "icon" | "text" | "both" | "icon-text" | "text-icon"
  loading?: boolean
  loadingText?: string
  customLabel?: string
  customIcon?: React.ComponentType<{ className?: string }>
  iconPosition?: "left" | "right"
  fullWidth?: boolean
  "data-testid"?: string
}

export const UnifiedButton = React.forwardRef<
  HTMLButtonElement,
  UnifiedButtonProps
>(
  (
    {
      action,
      size = "md",
      variant: variantOverride,
      display = "icon",
      loading = false,
      loadingText,
      customLabel,
      customIcon: CustomIcon,
      iconPosition = "left",
      fullWidth = false,
      disabled = false,
      className,
      "data-testid": testId,
      children,
      ...props
    },
    ref
  ) => {
    const config = actionConfig[action]
    const sizeStyles = sizeConfig[size]
    const Icon = CustomIcon || config.icon

    if (!config) {
      console.warn(`UnifiedButton: Invalid action type "${action}". Using default configuration.`)
    }

    const buttonVariant = variantOverride || config?.variant || "outline"
    const label = customLabel || children || config?.label || action
    
    const isIconOnly = display === "icon"
    const showText = display === "text" || display === "both" || display === "icon-text" || display === "text-icon"
    const showIcon = display === "icon" || display === "both" || display === "icon-text" || display === "text-icon"
    const iconFirst = display !== "text-icon" && iconPosition === "left"
    
    const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
      if (loading || disabled) {
        e.preventDefault()
        return
      }
      props.onClick?.(e)
    }

    return (
      <Button
        ref={ref}
        variant={buttonVariant}
        disabled={disabled || loading}
        onClick={handleClick}
        className={cn(
          isIconOnly ? sizeStyles.iconOnly : sizeStyles.button,
          "transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
          fullWidth && "w-full",
          loading && "cursor-not-allowed",
          disabled && "cursor-not-allowed opacity-50",
          className
        )}
        aria-label={isIconOnly ? label : undefined}
        data-testid={testId || `unified-button-${action}`}
        {...props}
      >
        {loading && (
          <Loader2 
            className={cn(
              sizeStyles.icon, 
              "animate-spin",
              showText && iconFirst && "mr-2",
              showText && !iconFirst && "ml-2"
            )} 
            aria-hidden="true"
          />
        )}
        
        {!loading && showIcon && Icon && iconFirst && (
          <Icon 
            className={cn(
              sizeStyles.icon,
              showText && "mr-2"
            )}
            aria-hidden="true"
          />
        )}

        {showText && (
          <span className="truncate">
            {loading && loadingText ? loadingText : label}
          </span>
        )}

        {!loading && showIcon && Icon && !iconFirst && (
          <Icon 
            className={cn(
              sizeStyles.icon,
              showText && "ml-2"
            )}
            aria-hidden="true" 
          />
        )}
      </Button>
    )
  }
)

UnifiedButton.displayName = "UnifiedButton"

// Convenience components for backward compatibility (ActionButton)
export const ActionButton = React.forwardRef<
  HTMLButtonElement,
  UnifiedButtonProps
>((props, ref) => <UnifiedButton ref={ref} {...props} />)

ActionButton.displayName = "ActionButton"

// Common button combinations as convenience components
export const CreateButton = (props: Omit<UnifiedButtonProps, "action">) => (
  <UnifiedButton action="create" display="both" {...props} />
)

export const EditButton = (props: Omit<UnifiedButtonProps, "action">) => (
  <UnifiedButton action="edit" display="icon" variant="outline" {...props} />
)

export const DeleteButton = (props: Omit<UnifiedButtonProps, "action">) => (
  <UnifiedButton action="delete" display="icon" variant="destructive" {...props} />
)

export const SaveButton = (props: Omit<UnifiedButtonProps, "action">) => (
  <UnifiedButton action="save" display="both" {...props} />
)

export const CancelButton = (props: Omit<UnifiedButtonProps, "action">) => (
  <UnifiedButton action="cancel" display="text" variant="ghost" {...props} />
)

export const MoreButton = (props: Omit<UnifiedButtonProps, "action">) => (
  <UnifiedButton action="more" display="icon" variant="ghost" {...props} />
)

export const BackButton = (props: Omit<UnifiedButtonProps, "action">) => (
  <UnifiedButton action="back" display="both" variant="ghost" {...props} />
)

export const NextButton = (props: Omit<UnifiedButtonProps, "action">) => (
  <UnifiedButton action="next" display="both" {...props} />
)

// Utility functions

export const getActionConfig = (action: ActionType) => {
  return actionConfig[action]
}

export const isValidActionType = (action: string): action is ActionType => {
  return Object.keys(actionConfig).includes(action)
}

export const getActionsByCategory = () => {
  return {
    crud: ["create", "read", "update", "delete", "add", "remove", "edit", "view"],
    file: ["save", "download", "upload", "copy", "share", "export", "import"],
    state: ["activate", "deactivate", "pause", "resume", "play", "stop", "complete", "cancel", "archive", "restore", "refresh"],
    user: ["add-user", "remove-user", "invite-user", "assign", "unassign", "approve", "reject"],
    navigation: ["back", "forward", "next", "previous", "home", "goto", "link"],
    interaction: ["more", "more-vertical", "expand", "collapse", "filter", "search", "sort"],
    notification: ["notify", "unnotify", "bell", "mail", "subscribe", "unsubscribe"],
    favorites: ["favorite", "unfavorite", "star", "unstar", "bookmark", "unbookmark", "like", "unlike"],
    security: ["lock", "unlock", "secure", "login", "logout"],
    settings: ["settings", "configure", "customize"],
    documentation: ["help", "info", "docs", "guide"],
    scheduling: ["schedule", "calendar", "event"],
    visibility: ["show", "hide", "preview", "toggle"],
    performance: ["boost", "optimize", "enhance"],
    electrical: ["energize", "deenergize", "isolate", "test", "calibrate", "measure", "monitor", "analyze", "simulate", "calculate", "validate", "inspect", "commission", "decommission", "maintain", "report", "certify", "approve-electrical"],
  }
}

// Export types for external use
export type UnifiedButtonSize = keyof typeof sizeConfig
export type UnifiedButtonVariant = NonNullable<UnifiedButtonProps["variant"]>
export type UnifiedButtonDisplay = NonNullable<UnifiedButtonProps["display"]>

// Export action configuration for external use
export { actionConfig, sizeConfig }