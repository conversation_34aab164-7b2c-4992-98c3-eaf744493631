/**
 * Atomic Components - Foundational Building Blocks
 * 
 * Universal atomic design components providing core functionality
 * for the Ultimate Electrical Designer application with comprehensive
 * electrical engineering domain support.
 * 
 * Architecture:
 * - Single responsibility components
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Consistent design system integration
 * - Performance optimized
 * - Full backward compatibility
 * - Professional electrical engineering support
 * 
 * ENHANCED CONSOLIDATION FEATURES:
 * - Badge: Comprehensive electrical status, component status, project status, and priority support
 * - Button: Action-based system with electrical engineering actions (energize, calibrate, commission, etc.)
 * - FormField: Complete form field component with validation states and electrical-specific field types
 * - LoadingState: Multiple variants (spinner, pulse, dots, bars) with electrical process loading states
 * - EmptyState: Contextual variants including electrical-specific empty states (components, circuits, calculations)
 * - Icon: Enhanced with comprehensive electrical component icon mapping
 * - Full unified patterns consolidated from legacy components
 */

// =============================================================================
// CORE ATOMS - Enhanced with Unified Features
// =============================================================================

// Enhanced Badge with comprehensive electrical engineering support
export { 
  Badge,
  // Convenience badge components
  ComponentStatusBadge,
  ProjectStatusBadge,
  PriorityBadge,
  ElectricalStatusBadge,
  // Individual convenience badges
  ActiveBadge,
  InactiveBadge,
  PreferredBadge,
  CompletedBadge,
  DraftBadge,
  HighPriorityBadge,
  CriticalPriorityBadge,
  EnergizedBadge,
  FaultBadge,
  // Utility functions and hooks
  getBadgeConfig,
  isValidBadgeIntent,
  getBadgeIntentsByCategory,
  useComponentBadgeProps,
  useStatusBadgeProps,
  usePriorityBadgeProps,
  useElectricalBadgeProps,
  // Types
  type BadgeProps,
  type BadgeVariant,
  type BadgeSize,
  type BadgeIntent,
  // Variants and config
  badgeVariants,
  badgeIntentConfig
} from "./Badge"

// Enhanced Button with comprehensive action system and electrical engineering actions
export { 
  Button,
  // Convenience button components
  ActionButton,
  CreateButton,
  EditButton,
  DeleteButton,
  SaveButton,
  CancelButton,
  MoreButton,
  BackButton,
  NextButton,
  // Electrical engineering convenience components
  EnergizeButton,
  TestButton,
  CalibrateButton,
  CommissionButton,
  // Utility functions
  getActionConfig,
  isValidActionType,
  getActionsByCategory,
  // Types
  type ButtonProps,
  type ButtonVariant,
  type ButtonSize,
  type ActionType,
  type ButtonDisplay,
  // Variants and config
  buttonVariants,
  actionConfig
} from "./Button"

// Core atomic components
export { 
  Input,
  type InputProps,
  type InputSize,
  type InputState,
  type InputVariant,
  inputVariants
} from "./Input"

export { 
  Label,
  type LabelProps,
  type LabelSize,
  type LabelWeight,
  type LabelState,
  labelVariants
} from "./Label"

export { 
  Icon,
  type IconProps,
  type IconName,
  type IconSize,
  type IconColor,
  iconVariants,
  iconMap,
  hasIcon,
  getAvailableIcons
} from "./Icon"

// =============================================================================
// NEW CONSOLIDATED ATOMS - From Unified Components
// =============================================================================

// FormField - Consolidated from unified-form
export {
  FormField,
  // Specialized field components
  EmailField,
  PasswordField,
  NumberField,
  SearchField,
  TextAreaField,
  TelephoneField,
  UrlField,
  // Utility functions
  validateField,
  getFieldTypeFromString,
  // Types
  type FormFieldProps,
  type FormFieldSize,
  type FormFieldVariant,
  type FormFieldType,
  type FieldType,
  type ValidationState,
  // Configuration
  sizeConfig as formFieldSizeConfig,
  formFieldVariants
} from "./FormField"

// LoadingState - Consolidated from unified-state
export {
  LoadingState,
  // Convenience components
  LoadingSpinner,
  LoadingOverlay,
  InlineLoading,
  // Electrical engineering specific loading states
  SystemCalculationLoading,
  ValidationProcessLoading,
  ComponentAnalysisLoading,
  // Types
  type LoadingStateProps,
  type LoadingStateSize,
  type LoadingStateVariant,
  type LoadingStateLayout,
  type LoadingVariant,
  type LoadingSize,
  type LoadingLayout,
  // Configuration
  loadingSizeConfig,
  loadingVariants
} from "./LoadingState"

// EmptyState - Consolidated from unified-state
export {
  EmptyState,
  ErrorState,
  // Specialized empty state components
  EmptyProjectList,
  EmptySearchResults,
  EmptyTeamMembers,
  EmptyComponents,
  EmptyCalculations,
  EmptyCircuits,
  NetworkError,
  // Skeleton components
  SkeletonLine,
  SkeletonCard,
  // Utility functions
  getEmptyStateConfig,
  isValidEmptyStateVariant,
  // Types
  type EmptyStateProps,
  type ErrorStateProps,
  type EmptyStateSize,
  type EmptyStateVariant,
  // Configuration
  emptyStateSizeConfig,
  emptyStateConfig,
  emptyStateVariants
} from "./EmptyState"

// =============================================================================
// ENHANCED ATOMS - Phase 1 Implementation (Existing)
// =============================================================================

export { 
  StatusIndicator, 
  type StatusIndicatorProps, 
  type StatusIndicatorVariant, 
  type StatusIndicatorSize, 
  type StatusIndicatorStyle,
  statusIndicatorVariants,
  statusIconMap,
  iconSizeConfig as statusIconSizeConfig,
  getElectricalStatusColor,
  isElectricalStatus,
  isSystemStatus,
  isProcessStatus
} from "./StatusIndicator"

export { 
  Avatar, 
  AvatarGroup,
  type AvatarProps, 
  type AvatarGroupProps,
  type AvatarVariant, 
  type AvatarSize,
  type UserRole,
  type UserStatus,
  avatarVariants,
  roleIndicatorVariants,
  roleIconMap,
  statusColors,
  getElectricalRoleIcon,
  getElectricalRoleColor,
  isValidElectricalRole
} from "./Avatar"

export { 
  ProgressBar, 
  CircularProgress,
  type ProgressBarProps, 
  type CircularProgressProps,
  type ProgressBarVariant, 
  type ProgressBarSize,
  progressBarVariants,
  progressFillVariants,
  shimmerVariants,
  getElectricalProgressVariant,
  getElectricalProgressColor,
  progressAnimationKeyframes
} from "./ProgressBar"

export { 
  Chip, 
  ChipGroup,
  type ChipProps, 
  type ChipGroupProps,
  type ChipVariant, 
  type ChipSize,
  chipVariants,
  closeButtonVariants,
  variantIconMap as chipVariantIconMap,
  getElectricalChipVariant,
  getElectricalChipIcon,
  createElectricalStatusChips
} from "./Chip"

// =============================================================================
// MIGRATION NOTES & COMPATIBILITY
// =============================================================================

/*
MIGRATION GUIDE: Legacy Unified Components → Enhanced Atomic Components

WHAT'S NEW:
✅ Enhanced Badge: Complete electrical engineering status support
✅ Enhanced Button: Action-based system with electrical engineering actions
✅ New FormField: Consolidated unified-form functionality with validation
✅ New LoadingState: Multiple variants with electrical process states
✅ New EmptyState: Contextual variants including electrical-specific states
✅ Full backward compatibility maintained

MIGRATION PATH (for future development):

Old unified approach:
  import { UnifiedBadge } from "@/components/ui"
  <UnifiedBadge type="component" intent="active" showIcon />

New atomic approach (recommended):
  import { Badge } from "@/components/atoms"
  <Badge intent="active" showIcon />

Old form approach:
  import { UnifiedFormField } from "@/components/ui"
  <UnifiedFormField label="Email" type="email" error="Invalid email" />

New atomic approach (recommended):
  import { FormField } from "@/components/atoms"
  <FormField label="Email" type="email" error="Invalid email" />

Old loading approach:
  import { UnifiedLoading } from "@/components/ui"
  <UnifiedLoading variant="spinner" text="Loading..." />

New atomic approach (recommended):
  import { LoadingState } from "@/components/atoms"
  <LoadingState variant="spinner" text="Loading..." />

Old empty state approach:
  import { UnifiedEmptyState } from "@/components/ui"
  <UnifiedEmptyState variant="search" title="No results" />

New atomic approach (recommended):
  import { EmptyState } from "@/components/atoms"
  <EmptyState variant="search" title="No results" />

ELECTRICAL ENGINEERING BENEFITS:
🔌 Native electrical component status support
⚡ Electrical engineering actions (energize, calibrate, commission, etc.)
🔧 Professional electrical design workflow integration
📊 Contextual electrical empty states and loading states
🔍 Enhanced electrical component icon mapping
✅ Engineering-grade quality standards compliance

BACKWARD COMPATIBILITY:
All existing unified components remain functional through the UI index exports.
This allows gradual migration and no breaking changes to existing code.
*/