/**
 * EmptyState Atom - Universal Foundational Component
 * 
 * Enhanced atomic design empty state component providing comprehensive empty state functionality
 * with contextual variants, actions, and engineering-grade quality.
 * 
 * Features:
 * - Atomic design principles (single responsibility)
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Comprehensive contextual variants (search, folder, users, electrical, etc.)
 * - Professional empty state patterns for electrical applications
 * - Performance optimized with conditional rendering
 * - Full backward compatibility support
 * - Action integration for user guidance
 */

import React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import {
  AlertCircle,
  Archive,
  BookOpen,
  Calendar,
  Database,
  FileText,
  Folder,
  Heart,
  Package,
  PlusCircle,
  RefreshCw,
  Search,
  Server,
  Settings,
  ShoppingCart,
  Star,
  Users,
  WifiOff,
  Zap,
  Component,
  CircuitBoard,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "./Button"

// Empty state variants using CVA
const emptyStateVariants = cva(
  "flex flex-col items-center text-center transition-opacity",
  {
    variants: {
      size: {
        sm: "py-6 space-y-2",
        default: "py-8 space-y-3", 
        lg: "py-12 space-y-4",
        xl: "py-16 space-y-6",
      },
    },
    defaultVariants: {
      size: "default",
    },
  }
)

const emptyStateSizeConfig = {
  sm: {
    icon: "h-8 w-8",
    title: "text-base font-medium",
    description: "text-sm", 
    button: "h-8 px-3 text-xs",
  },
  default: {
    icon: "h-12 w-12",
    title: "text-lg font-semibold", 
    description: "text-sm",
    button: "h-10 px-4 text-sm",
  },
  lg: {
    icon: "h-16 w-16",
    title: "text-xl font-semibold",
    description: "text-base",
    button: "h-12 px-6 text-base", 
  },
  xl: {
    icon: "h-20 w-20",
    title: "text-2xl font-bold", 
    description: "text-lg",
    button: "h-14 px-8 text-lg",
  },
} as const

// Empty state types  
export type EmptyStateVariant =
  | "search" | "folder" | "users" | "documents" | "data"
  | "settings" | "calendar" | "archive" | "package" 
  | "cart" | "favorites" | "network" | "server" | "custom"
  // Electrical engineering specific variants
  | "components" | "calculations" | "circuits" | "projects"
  | "specifications" | "reports" | "calibration" | "testing"

export type EmptyStateSize = keyof typeof emptyStateSizeConfig

// Empty state configuration
const emptyStateConfig = {
  search: {
    icon: Search,
    color: "text-blue-400",
    title: "No results found",
    description: "Try adjusting your search terms or filters",
  },
  folder: {
    icon: Folder,
    color: "text-yellow-400", 
    title: "No items found",
    description: "This folder is empty or no items match your criteria",
  },
  users: {
    icon: Users,
    color: "text-green-400",
    title: "No users found", 
    description: "No users have been added or match your search",
  },
  documents: {
    icon: FileText,
    color: "text-purple-400",
    title: "No documents found",
    description: "No documents are available or match your criteria",
  },
  data: {
    icon: Database,
    color: "text-gray-400",
    title: "No data available",
    description: "There is no data to display at this time",
  },
  settings: {
    icon: Settings,
    color: "text-blue-400",
    title: "No configuration found",
    description: "Settings have not been configured yet",
  },
  calendar: {
    icon: Calendar,
    color: "text-indigo-400", 
    title: "No events scheduled",
    description: "You have no upcoming events or appointments",
  },
  archive: {
    icon: Archive,
    color: "text-amber-400",
    title: "Archive is empty",
    description: "No archived items are currently available",
  },
  package: {
    icon: Package,
    color: "text-orange-400",
    title: "No packages found", 
    description: "No packages or components are available",
  },
  cart: {
    icon: ShoppingCart,
    color: "text-emerald-400",
    title: "Cart is empty",
    description: "You haven't added any items to your cart yet",
  },
  favorites: {
    icon: Star,
    color: "text-yellow-400", 
    title: "No favorites yet",
    description: "Items you mark as favorites will appear here",
  },
  network: {
    icon: WifiOff,
    color: "text-red-400",
    title: "Connection lost",
    description: "Please check your internet connection and try again",
  },
  server: {
    icon: Server,
    color: "text-slate-400",
    title: "Server unavailable", 
    description: "The server is currently unavailable. Please try again later",
  },
  custom: {
    icon: null,
    color: "text-muted-foreground",
    title: "No content",
    description: "No content is currently available", 
  },
  // Electrical engineering specific configurations
  components: {
    icon: Component,
    color: "text-blue-500",
    title: "No electrical components",
    description: "No electrical components are available or match your criteria",
  },
  calculations: {
    icon: Zap,
    color: "text-yellow-500",
    title: "No calculations available",
    description: "No electrical calculations have been performed yet",
  },
  circuits: {
    icon: CircuitBoard,
    color: "text-green-500",
    title: "No circuits found",
    description: "No circuit designs are available or match your search",
  },
  projects: {
    icon: Folder,
    color: "text-purple-500",
    title: "No electrical projects",
    description: "No electrical design projects have been created yet",
  },
  specifications: {
    icon: FileText,
    color: "text-indigo-500",
    title: "No specifications",
    description: "No technical specifications are available",
  },
  reports: {
    icon: BookOpen,
    color: "text-orange-500",
    title: "No reports generated",
    description: "No electrical analysis reports have been generated",
  },
  calibration: {
    icon: Settings,
    color: "text-cyan-500",
    title: "No calibration data",
    description: "No instrument calibration records are available",
  },
  testing: {
    icon: AlertCircle,
    color: "text-red-500",
    title: "No test results",
    description: "No electrical testing results are available",
  },
} as const

// Empty State Component
export interface EmptyStateProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof emptyStateVariants> {
  /** Empty state variant */
  variant?: EmptyStateVariant
  /** Custom icon element */
  icon?: React.ReactNode
  /** Title text */
  title?: string
  /** Description text */
  description?: string
  /** Action element (usually a button) */
  action?: React.ReactNode
  /** Custom icon component */
  customIcon?: React.ComponentType<{ className?: string }>
  /** Test ID for testing */
  "data-testid"?: string
}

export const EmptyState = React.forwardRef<
  HTMLDivElement,
  EmptyStateProps
>(
  (
    {
      variant = "data",
      size = "default",
      icon: customIcon,
      title: customTitle,
      description: customDescription,
      action,
      customIcon: CustomIconComponent,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const config = emptyStateConfig[variant]
    const sizeStyles = emptyStateSizeConfig[size]
    
    const IconComponent = CustomIconComponent || config.icon
    const title = customTitle || config.title
    const description = customDescription || config.description

    return (
      <div
        ref={ref}
        className={cn(emptyStateVariants({ size }), className)}
        data-testid={testId || `empty-state-${variant}`}
        {...props}
      >
        {/* Icon */}
        <div className={cn(sizeStyles.icon, config.color)}>
          {customIcon ? (
            customIcon
          ) : IconComponent ? (
            <IconComponent className="h-full w-full" />
          ) : (
            <Database className="h-full w-full" />
          )}
        </div>

        {/* Content */}
        <div className="space-y-2 max-w-md">
          <h3 className={cn(sizeStyles.title, "text-foreground")}>
            {title}
          </h3>

          {description && (
            <p className={cn(sizeStyles.description, "text-muted-foreground")}>
              {description}
            </p>
          )}
        </div>

        {/* Action */}
        {action && (
          <div className="pt-2">
            {action}
          </div>
        )}
      </div>
    )
  }
)

EmptyState.displayName = "EmptyState"

// Error State Component
export interface ErrorStateProps extends EmptyStateProps {
  /** Error object or message */
  error?: Error | string
  /** Retry function */
  retry?: () => void
  /** Retry button text */
  retryText?: string
}

export const ErrorState = React.forwardRef<
  HTMLDivElement,
  ErrorStateProps
>(
  (
    {
      error,
      retry,
      retryText = "Try Again",
      title: customTitle,
      description: customDescription,
      action: customAction,
      size = "default",
      className,
      ...props
    },
    ref
  ) => {
    const errorMessage = typeof error === "string" ? error : error?.message
    const title = customTitle || "Something went wrong"
    const description = customDescription || errorMessage || "An unexpected error occurred. Please try again."
    
    const action = customAction || (retry && (
      <Button 
        onClick={retry}
        variant="outline"
        className={emptyStateSizeConfig[size].button}
      >
        <RefreshCw className="mr-2 h-4 w-4" />
        {retryText}
      </Button>
    ))

    return (
      <EmptyState
        ref={ref}
        variant="custom"
        size={size}
        icon={<AlertCircle className="h-full w-full text-destructive" />}
        title={title}
        description={description}
        action={action}
        className={className}
        {...props}
      />
    )
  }
)

ErrorState.displayName = "ErrorState"

// Specialized empty state components

export const EmptyProjectList: React.FC<{
  onCreateProject?: () => void
  createText?: string
}> = ({ onCreateProject, createText = "Create Project" }) => (
  <EmptyState
    variant="projects"
    title="No electrical projects"
    description="Get started by creating your first electrical design project or adjust your search filters."
    action={
      onCreateProject && (
        <Button onClick={onCreateProject}>
          <PlusCircle className="mr-2 h-4 w-4" />
          {createText}
        </Button>
      )
    }
  />
)

export const EmptySearchResults: React.FC<{
  searchQuery?: string
  onClearSearch?: () => void
  clearText?: string
}> = ({ searchQuery, onClearSearch, clearText = "Clear Search" }) => (
  <EmptyState
    variant="search"
    title="No results found"
    description={
      searchQuery
        ? `No items match "${searchQuery}". Try adjusting your search terms.`
        : "No items match your current filters."
    }
    action={
      onClearSearch && (
        <Button variant="outline" onClick={onClearSearch}>
          {clearText}
        </Button>
      )
    }
  />
)

export const EmptyTeamMembers: React.FC<{
  onAddMember?: () => void
  addText?: string
}> = ({ onAddMember, addText = "Add Team Member" }) => (
  <EmptyState
    variant="users"
    title="No team members"
    description="This project doesn't have any team members yet. Add members to start collaborating."
    action={
      onAddMember && (
        <Button onClick={onAddMember}>
          <Users className="mr-2 h-4 w-4" />
          {addText}
        </Button>
      )
    }
    size="sm"
  />
)

export const EmptyComponents: React.FC<{
  onAddComponent?: () => void
  addText?: string
}> = ({ onAddComponent, addText = "Add Component" }) => (
  <EmptyState
    variant="components"
    title="No electrical components found" 
    description="No electrical components are available or match your search criteria."
    action={
      onAddComponent && (
        <Button onClick={onAddComponent}>
          <PlusCircle className="mr-2 h-4 w-4" />
          {addText}
        </Button>
      )
    }
  />
)

export const EmptyCalculations: React.FC<{
  onCreateCalculation?: () => void
  createText?: string
}> = ({ onCreateCalculation, createText = "Create Calculation" }) => (
  <EmptyState
    variant="calculations"
    title="No calculations available"
    description="No electrical calculations have been performed. Start with a new calculation."
    action={
      onCreateCalculation && (
        <Button onClick={onCreateCalculation}>
          <Zap className="mr-2 h-4 w-4" />
          {createText}
        </Button>
      )
    }
  />
)

export const EmptyCircuits: React.FC<{
  onCreateCircuit?: () => void
  createText?: string
}> = ({ onCreateCircuit, createText = "Create Circuit" }) => (
  <EmptyState
    variant="circuits"
    title="No circuit designs"
    description="No circuit designs are available. Start by creating a new circuit design."
    action={
      onCreateCircuit && (
        <Button onClick={onCreateCircuit}>
          <CircuitBoard className="mr-2 h-4 w-4" />
          {createText}
        </Button>
      )
    }
  />
)

export const NetworkError: React.FC<{
  onRetry?: () => void
  retryText?: string
}> = ({ onRetry, retryText = "Retry" }) => (
  <ErrorState
    title="Connection Error"
    description="Unable to connect to the server. Please check your internet connection and try again."
    retry={onRetry}
    retryText={retryText}
    icon={<WifiOff className="h-full w-full text-destructive" />}
  />
)

// Skeleton loading patterns
export const SkeletonLine: React.FC<{ 
  width?: string | number
  height?: string | number
  className?: string 
}> = ({ width = "100%", height = "1rem", className }) => (
  <div 
    className={cn("bg-muted animate-pulse rounded", className)}
    style={{ width, height }}
  />
)

export const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => (
  <div className={cn("p-4 border rounded-lg space-y-3", className)}>
    <SkeletonLine width="60%" height="1.25rem" />
    <SkeletonLine width="100%" height="1rem" />
    <SkeletonLine width="80%" height="1rem" />
    <div className="flex space-x-2 pt-2">
      <SkeletonLine width="4rem" height="2rem" />
      <SkeletonLine width="4rem" height="2rem" />
    </div>
  </div>
)

// Utility functions
export const getEmptyStateConfig = (variant: EmptyStateVariant) => {
  return emptyStateConfig[variant]
}

export const isValidEmptyStateVariant = (variant: string): variant is EmptyStateVariant => {
  return Object.keys(emptyStateConfig).includes(variant)
}

// Export types for external use
export type EmptyStateSize = keyof typeof emptyStateSizeConfig
export type EmptyStateVariant = keyof typeof emptyStateConfig

// Export configurations for external use
export { 
  emptyStateSizeConfig, 
  emptyStateConfig,
  emptyStateVariants
}