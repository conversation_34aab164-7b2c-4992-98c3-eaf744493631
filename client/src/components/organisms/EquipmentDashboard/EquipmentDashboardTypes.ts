/**
 * Equipment Dashboard Organism Types
 * 
 * TypeScript type definitions for the Equipment Dashboard organism,
 * providing electrical equipment monitoring interfaces with engineering-grade quality.
 * 
 * Features:
 * - Professional electrical engineering types
 * - IEEE/IEC standards compliance
 * - Real-time monitoring data structures
 * - WCAG 2.1 AA accessibility support
 * - TypeScript strict mode compliance
 */

import type { LucideIcon } from "lucide-react"
import type { HealthStatus, HealthMetric } from "@/components/molecules/HealthIndicator"
import type { StatusCardMetric } from "@/components/molecules/StatusCard"

// Equipment Status Types
export type EquipmentStatus = 
  | "operational"
  | "warning"
  | "critical"
  | "maintenance"
  | "fault"
  | "offline"
  | "energized"
  | "deenergized"
  | "testing"

// Electrical System Types
export type ElectricalSystemType = 
  | "power_distribution"
  | "motor_control"
  | "lighting"
  | "ups"
  | "switchgear"
  | "transformer"
  | "panel_board"
  | "motor"
  | "generator"
  | "capacitor_bank"

// Voltage Classes per IEC Standards
export type VoltageClass = 
  | "LV" // Low Voltage: ≤1kV
  | "MV" // Medium Voltage: 1kV-35kV
  | "HV" // High Voltage: 35kV-100kV
  | "EHV" // Extra High Voltage: >100kV

// Equipment Priority Levels
export type EquipmentPriority = "critical" | "high" | "medium" | "low"

// Electrical Measurements
export interface ElectricalMeasurement {
  readonly timestamp: Date
  readonly voltage: number // Volts
  readonly current: number // Amperes
  readonly power: number // Watts
  readonly powerFactor: number // 0-1
  readonly frequency: number // Hz
  readonly thd?: number // Total Harmonic Distortion %
  readonly temperature?: number // Celsius
  readonly vibration?: number // mm/s RMS
  readonly efficiency?: number // %
}

// Equipment Location
export interface EquipmentLocation {
  readonly building: string
  readonly floor?: string
  readonly room: string
  readonly coordinates?: {
    readonly x: number
    readonly y: number
    readonly z?: number
  }
}

// Maintenance Information
export interface MaintenanceInfo {
  readonly lastMaintenance?: Date
  readonly nextMaintenance: Date
  readonly maintenanceType: "preventive" | "predictive" | "corrective"
  readonly hoursToMaintenance?: number
  readonly maintenanceHistory: ReadonlyArray<{
    readonly date: Date
    readonly type: string
    readonly description: string
    readonly technician: string
  }>
}

// Equipment Definition
export interface Equipment {
  readonly id: string
  readonly name: string
  readonly type: ElectricalSystemType
  readonly status: EquipmentStatus
  readonly priority: EquipmentPriority
  readonly voltageClass: VoltageClass
  readonly ratedVoltage: number // Volts
  readonly ratedCurrent: number // Amperes
  readonly ratedPower?: number // Watts
  readonly location: EquipmentLocation
  readonly manufacturer?: string
  readonly model?: string
  readonly serialNumber?: string
  readonly commissionDate?: Date
  readonly measurements: ElectricalMeasurement
  readonly health: HealthStatus
  readonly healthMetrics: ReadonlyArray<HealthMetric>
  readonly maintenance: MaintenanceInfo
  readonly description?: string
  readonly tags?: ReadonlyArray<string>
  readonly isOnline: boolean
  readonly lastUpdate: Date
}

// Dashboard Layout Configuration
export interface DashboardLayout {
  readonly columns: 1 | 2 | 3 | 4
  readonly cardSize: "sm" | "md" | "lg"
  readonly showMetrics: boolean
  readonly showTrends: boolean
  readonly groupBy: "status" | "type" | "location" | "priority" | "none"
  readonly sortBy: "name" | "status" | "priority" | "lastUpdate" | "health"
  readonly sortOrder: "asc" | "desc"
}

// Filter Configuration
export interface EquipmentFilters {
  readonly status?: ReadonlyArray<EquipmentStatus>
  readonly type?: ReadonlyArray<ElectricalSystemType>
  readonly priority?: ReadonlyArray<EquipmentPriority>
  readonly voltageClass?: ReadonlyArray<VoltageClass>
  readonly location?: string
  readonly healthStatus?: ReadonlyArray<HealthStatus>
  readonly isOnline?: boolean
  readonly maintenanceDue?: boolean
  readonly searchQuery?: string
}

// Alert Configuration
export interface EquipmentAlert {
  readonly id: string
  readonly equipmentId: string
  readonly equipmentName: string
  readonly type: "threshold" | "fault" | "maintenance" | "communication"
  readonly severity: "critical" | "warning" | "info"
  readonly title: string
  readonly message: string
  readonly timestamp: Date
  readonly acknowledged: boolean
  readonly acknowledgedBy?: string
  readonly acknowledgedAt?: Date
}

// Dashboard Configuration
export interface DashboardConfig {
  readonly autoRefresh: boolean
  readonly refreshInterval: number // seconds
  readonly alertThresholds: {
    readonly voltage: { min: number; max: number }
    readonly current: { max: number }
    readonly temperature: { max: number }
    readonly powerFactor: { min: number }
  }
  readonly showOfflineEquipment: boolean
  readonly groupAlerts: boolean
  readonly soundAlerts: boolean
}

// Equipment Dashboard Props
export interface EquipmentDashboardProps {
  /** Equipment data to display */
  equipment: ReadonlyArray<Equipment>
  /** Dashboard layout configuration */
  layout?: DashboardLayout
  /** Active filters */
  filters?: EquipmentFilters
  /** Active alerts */
  alerts?: ReadonlyArray<EquipmentAlert>
  /** Dashboard configuration */
  config?: DashboardConfig
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Equipment selection callback */
  onEquipmentSelect?: (equipment: Equipment) => void
  /** Equipment action callback */
  onEquipmentAction?: (equipmentId: string, action: string) => void
  /** Filter change callback */
  onFiltersChange?: (filters: EquipmentFilters) => void
  /** Layout change callback */
  onLayoutChange?: (layout: DashboardLayout) => void
  /** Alert acknowledgment callback */
  onAlertAcknowledge?: (alertId: string) => void
  /** Refresh callback */
  onRefresh?: () => void
  /** Configuration change callback */
  onConfigChange?: (config: DashboardConfig) => void
  /** Custom class name */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

// Equipment Card Props
export interface EquipmentCardProps {
  readonly equipment: Equipment
  readonly size?: "sm" | "md" | "lg"
  readonly showMetrics?: boolean
  readonly showTrends?: boolean
  readonly interactive?: boolean
  readonly selected?: boolean
  readonly loading?: boolean
  readonly onClick?: (equipment: Equipment) => void
  readonly onAction?: (equipmentId: string, action: string) => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Alert Panel Props
export interface AlertPanelProps {
  readonly alerts: ReadonlyArray<EquipmentAlert>
  readonly maxHeight?: number
  readonly groupByEquipment?: boolean
  readonly showAll?: boolean
  readonly onAcknowledge?: (alertId: string) => void
  readonly onViewEquipment?: (equipmentId: string) => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Filter Panel Props
export interface FilterPanelProps {
  readonly filters: EquipmentFilters
  readonly availableTypes: ReadonlyArray<ElectricalSystemType>
  readonly availableLocations: ReadonlyArray<string>
  readonly onChange: (filters: EquipmentFilters) => void
  readonly onReset: () => void
  readonly collapsed?: boolean
  readonly onToggleCollapsed?: () => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Dashboard Controls Props
export interface DashboardControlsProps {
  readonly layout: DashboardLayout
  readonly config: DashboardConfig
  readonly isRefreshing?: boolean
  readonly onLayoutChange: (layout: DashboardLayout) => void
  readonly onConfigChange: (config: DashboardConfig) => void
  readonly onRefresh: () => void
  readonly onExport?: () => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Utility Types
export type EquipmentMetrics = Pick<Equipment, "measurements" | "health" | "healthMetrics">
export type EquipmentSummary = Pick<Equipment, "id" | "name" | "type" | "status" | "priority" | "health">
export type EquipmentActions = "start" | "stop" | "reset" | "test" | "maintenance" | "isolate" | "energize"

// Status Mapping Utilities
export const EQUIPMENT_STATUS_COLORS: Record<EquipmentStatus, string> = {
  operational: "text-green-600",
  warning: "text-amber-600", 
  critical: "text-red-600",
  maintenance: "text-blue-600",
  fault: "text-red-700",
  offline: "text-gray-500",
  energized: "text-emerald-600",
  deenergized: "text-slate-600",
  testing: "text-purple-600",
} as const

export const SYSTEM_TYPE_LABELS: Record<ElectricalSystemType, string> = {
  power_distribution: "Power Distribution",
  motor_control: "Motor Control",
  lighting: "Lighting",
  ups: "UPS",
  switchgear: "Switchgear",
  transformer: "Transformer",
  panel_board: "Panel Board",
  motor: "Motor",
  generator: "Generator",
  capacitor_bank: "Capacitor Bank",
} as const

export const VOLTAGE_CLASS_LABELS: Record<VoltageClass, string> = {
  LV: "Low Voltage (≤1kV)",
  MV: "Medium Voltage (1-35kV)",
  HV: "High Voltage (35-100kV)",
  EHV: "Extra High Voltage (>100kV)",
} as const

// Default Values
export const DEFAULT_DASHBOARD_LAYOUT: DashboardLayout = {
  columns: 3,
  cardSize: "md",
  showMetrics: true,
  showTrends: true,
  groupBy: "none",
  sortBy: "priority",
  sortOrder: "desc",
} as const

export const DEFAULT_DASHBOARD_CONFIG: DashboardConfig = {
  autoRefresh: true,
  refreshInterval: 30,
  alertThresholds: {
    voltage: { min: 440, max: 520 },
    current: { max: 100 },
    temperature: { max: 75 },
    powerFactor: { min: 0.85 },
  },
  showOfflineEquipment: true,
  groupAlerts: true,
  soundAlerts: false,
} as const