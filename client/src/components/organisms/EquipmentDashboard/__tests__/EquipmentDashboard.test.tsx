/**
 * Equipment Dashboard Organism Tests
 * 
 * Comprehensive test suite for Equipment Dashboard organism covering
 * functionality, accessibility, electrical engineering features, and edge cases.
 * 
 * Features tested:
 * - Component rendering and interaction
 * - Real-time data updates
 * - Filter and search functionality
 * - Alert management
 * - Accessibility compliance
 * - Electrical engineering contexts
 * - Error handling and edge cases
 * - Performance optimization
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { render, screen, fireEvent, waitFor, within } from "@testing-library/react"
import { userEvent } from "@testing-library/user-event"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { toast } from "sonner"

import { EquipmentDashboard } from "../EquipmentDashboard"
import { useEquipmentDashboard } from "../useEquipmentDashboard"
import type { Equipment, EquipmentAlert, DashboardLayout } from "../EquipmentDashboardTypes"

// Mock the hooks
vi.mock("../useEquipmentDashboard")
vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
  },
}))

// Mock Lucide React icons
vi.mock("lucide-react", () => ({
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  Filter: () => <div data-testid="filter-icon" />,
  RefreshCw: () => <div data-testid="refresh-icon" />,
  Bell: () => <div data-testid="bell-icon" />,
  Zap: () => <div data-testid="zap-icon" />,
  Activity: () => <div data-testid="activity-icon" />,
  TrendingUp: () => <div data-testid="trending-up-icon" />,
  TrendingDown: () => <div data-testid="trending-down-icon" />,
  CheckCircle2: () => <div data-testid="check-circle-icon" />,
  XCircle: () => <div data-testid="x-circle-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
}))

// Test data
const mockEquipment: Equipment = {
  id: "eq-001",
  name: "Main Distribution Panel",
  type: "panel_board",
  status: "operational",
  priority: "critical",
  voltageClass: "LV",
  ratedVoltage: 480,
  ratedCurrent: 400,
  ratedPower: 150000,
  location: {
    building: "Main Building",
    floor: "Ground Floor",
    room: "Electrical Room A",
    coordinates: { x: 10, y: 20 },
  },
  manufacturer: "Schneider Electric",
  model: "PowerPact P250",
  serialNumber: "SN-12345",
  commissionDate: new Date("2020-01-15"),
  measurements: {
    timestamp: new Date(),
    voltage: 480,
    current: 250,
    power: 120000,
    powerFactor: 0.92,
    frequency: 60,
    thd: 3.2,
    temperature: 45,
  },
  health: "healthy",
  healthMetrics: [
    {
      name: "Voltage",
      value: 480,
      max: 520,
      unit: "V",
      status: "healthy",
      threshold: { warning: 440, critical: 420 },
    },
    {
      name: "Current",
      value: 250,
      max: 400,
      unit: "A", 
      status: "healthy",
      threshold: { warning: 320, critical: 380 },
    },
  ],
  maintenance: {
    nextMaintenance: new Date("2025-01-15"),
    maintenanceType: "preventive",
    maintenanceHistory: [
      {
        date: new Date("2024-01-15"),
        type: "Preventive",
        description: "Annual maintenance",
        technician: "John Smith",
      },
    ],
  },
  tags: ["critical", "main-feed"],
  isOnline: true,
  lastUpdate: new Date(),
}

const mockAlert: EquipmentAlert = {
  id: "alert-001", 
  equipmentId: "eq-001",
  equipmentName: "Main Distribution Panel",
  type: "threshold",
  severity: "warning",
  title: "High Temperature",
  message: "Equipment temperature exceeds normal range",
  timestamp: new Date(),
  acknowledged: false,
}

const mockLayout: DashboardLayout = {
  columns: 3,
  cardSize: "md",
  showMetrics: true,
  showTrends: true,
  groupBy: "none",
  sortBy: "priority",
  sortOrder: "desc",
}

// Mock hook return value
const mockHookReturn = {
  // Data
  equipment: [mockEquipment],
  filteredEquipment: [mockEquipment],
  selectedEquipment: null,
  alerts: [mockAlert],
  healthSummary: {
    total: 1,
    healthy: 1,
    warning: 0,
    critical: 0,
    offline: 0,
    averageHealth: 95,
  },
  
  // State
  loading: false,
  error: null,
  isRefreshing: false,
  connectionStatus: "connected" as const,
  hasUnacknowledgedAlerts: true,
  
  // UI State
  layout: mockLayout,
  config: {
    autoRefresh: true,
    refreshInterval: 30,
    alertThresholds: {
      voltage: { min: 440, max: 520 },
      current: { max: 400 },
      temperature: { max: 75 },
      powerFactor: { min: 0.85 },
    },
    showOfflineEquipment: true,
    groupAlerts: true,
    soundAlerts: false,
  },
  filters: {},
  selectedEquipmentIds: [],
  isFilterPanelOpen: false,
  bulkActionMode: false,
  searchQuery: "",
  
  // Computed State
  filteredCount: 1,
  totalCount: 1,
  activeFilterCount: 0,
  
  // Actions (mocked)
  selectEquipment: vi.fn(),
  executeEquipmentAction: vi.fn(),
  executeBulkAction: vi.fn(),
  refreshEquipment: vi.fn(),
  updateLayout: vi.fn(),
  updateConfig: vi.fn(),
  updateFilters: vi.fn(),
  resetFilters: vi.fn(),
  toggleFilterPanel: vi.fn(),
  toggleEquipmentSelection: vi.fn(),
  selectAllEquipment: vi.fn(),
  clearSelection: vi.fn(),
  toggleBulkActionMode: vi.fn(),
  setSearchQuery: vi.fn(),
  acknowledgeAlert: vi.fn(),
  markAllAlertsRead: vi.fn(),
}

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe("EquipmentDashboard", () => {
  const user = userEvent.setup()
  
  beforeEach(() => {
    vi.mocked(useEquipmentDashboard).mockReturnValue(mockHookReturn)
  })
  
  afterEach(() => {
    vi.clearAllMocks()
  })

  describe("Rendering", () => {
    it("renders dashboard with header and equipment grid", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("Equipment Dashboard")).toBeInTheDocument()
      expect(screen.getByText("Showing 1 of 1 equipment")).toBeInTheDocument()
      expect(screen.getByText("Main Distribution Panel")).toBeInTheDocument()
    })

    it("renders equipment cards with electrical engineering information", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const equipmentCard = screen.getByText("Main Distribution Panel").closest("[role='button']")
      expect(equipmentCard).toBeInTheDocument()
      
      // Check for electrical engineering details
      expect(screen.getByText("LV")).toBeInTheDocument() // Voltage class
      expect(screen.getByText("critical")).toBeInTheDocument() // Priority
      expect(screen.getByText("panel_board • Electrical Room A")).toBeInTheDocument()
    })

    it("displays connection status indicator", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("Connected")).toBeInTheDocument()
    })

    it("shows alert indicator when there are unacknowledged alerts", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const alertButton = screen.getByRole("button", { name: /alerts/i })
      expect(alertButton).toBeInTheDocument()
      
      // Should have alert indicator dot
      const alertIndicator = alertButton.querySelector(".bg-red-500")
      expect(alertIndicator).toBeInTheDocument()
    })
  })

  describe("Search and Filtering", () => {
    it("renders search box and handles search input", async () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const searchBox = screen.getByPlaceholderText("Search equipment...")
      expect(searchBox).toBeInTheDocument()
      
      await user.type(searchBox, "panel")
      
      expect(mockHookReturn.setSearchQuery).toHaveBeenCalledWith("panel")
    })

    it("shows filter button with active count", () => {
      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        activeFilterCount: 2,
      })

      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const filterButton = screen.getByRole("button", { name: /filters/i })
      expect(filterButton).toBeInTheDocument()
      expect(screen.getByText("2")).toBeInTheDocument() // Filter count badge
    })

    it("toggles filter panel when filter button clicked", async () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const filterButton = screen.getByRole("button", { name: /filters/i })
      await user.click(filterButton)
      
      expect(mockHookReturn.toggleFilterPanel).toHaveBeenCalled()
    })
  })

  describe("Equipment Interaction", () => {
    it("selects equipment when card is clicked", async () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const equipmentCard = screen.getByText("Main Distribution Panel").closest("[role='button']")
      expect(equipmentCard).toBeInTheDocument()
      
      await user.click(equipmentCard!)
      
      expect(mockHookReturn.selectEquipment).toHaveBeenCalledWith(mockEquipment)
    })

    it("shows equipment metrics when enabled in layout", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      // Check for metric values
      expect(screen.getByText("480")).toBeInTheDocument() // Voltage
      expect(screen.getByText("250")).toBeInTheDocument() // Current
      expect(screen.getByText("0.92")).toBeInTheDocument() // Power Factor
    })

    it("handles equipment action execution", async () => {
      const onEquipmentAction = vi.fn()
      
      render(
        <TestWrapper>
          <EquipmentDashboard onEquipmentAction={onEquipmentAction} />
        </TestWrapper>
      )

      // This would typically be triggered by action buttons in a larger card
      // For now, we'll test the prop passing
      expect(onEquipmentAction).toBeDefined()
    })
  })

  describe("Alert Management", () => {
    it("shows alerts panel when alerts button clicked", async () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const alertsButton = screen.getByRole("button", { name: /alerts/i })
      await user.click(alertsButton)
      
      await waitFor(() => {
        expect(screen.getByText("Active Alerts")).toBeInTheDocument()
        expect(screen.getByText("High Temperature")).toBeInTheDocument()
      })
    })

    it("handles alert acknowledgment", async () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      // Open alerts panel
      const alertsButton = screen.getByRole("button", { name: /alerts/i })
      await user.click(alertsButton)
      
      await waitFor(() => {
        const markAllButton = screen.getByRole("button", { name: /mark all read/i })
        expect(markAllButton).toBeInTheDocument()
      })
    })

    it("closes alerts panel", async () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      // Open alerts panel
      const alertsButton = screen.getByRole("button", { name: /alerts/i })
      await user.click(alertsButton)
      
      await waitFor(() => {
        const closeButton = screen.getByRole("button", { name: /close/i })
        fireEvent.click(closeButton)
      })

      await waitFor(() => {
        expect(screen.queryByText("Active Alerts")).not.toBeInTheDocument()
      })
    })
  })

  describe("Layout and Configuration", () => {
    it("renders equipment in grid layout based on columns setting", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const grid = screen.getByTestId("equipment-dashboard").querySelector(".grid")
      expect(grid).toHaveClass("grid-cols-1", "md:grid-cols-2", "lg:grid-cols-3")
    })

    it("handles refresh action", async () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const refreshButton = screen.getByRole("button", { name: /refresh/i })
      await user.click(refreshButton)
      
      expect(mockHookReturn.refreshEquipment).toHaveBeenCalled()
    })

    it("shows refreshing state with spinner", () => {
      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        isRefreshing: true,
      })

      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const refreshButton = screen.getByRole("button", { name: /refresh/i })
      expect(refreshButton).toBeDisabled()
    })
  })

  describe("Loading and Error States", () => {
    it("shows loading skeleton when loading", () => {
      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        loading: true,
        equipment: [],
        filteredEquipment: [],
      })

      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const skeletons = screen.getAllByRole("generic").filter(el => 
        el.className.includes("animate-pulse")
      )
      expect(skeletons.length).toBeGreaterThan(0)
    })

    it("shows empty state when no equipment found", () => {
      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        equipment: [],
        filteredEquipment: [],
        loading: false,
      })

      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("No Equipment Found")).toBeInTheDocument()
      expect(screen.getByText("Try adjusting your filters or search criteria.")).toBeInTheDocument()
    })

    it("shows error state when error occurs", () => {
      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        error: "Failed to load equipment data",
      })

      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("Equipment Dashboard Error")).toBeInTheDocument()
      expect(screen.getByText("Failed to load equipment data")).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /try again/i })).toBeInTheDocument()
    })

    it("handles error recovery", async () => {
      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        error: "Network error",
      })

      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const tryAgainButton = screen.getByRole("button", { name: /try again/i })
      await user.click(tryAgainButton)
      
      expect(mockHookReturn.refreshEquipment).toHaveBeenCalled()
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA labels and roles", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      // Check for main dashboard container
      expect(screen.getByTestId("equipment-dashboard")).toBeInTheDocument()
      
      // Check for accessible button labels
      expect(screen.getByRole("button", { name: /filters/i })).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /alerts/i })).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /refresh/i })).toBeInTheDocument()
      
      // Equipment cards should be clickable
      const equipmentCard = screen.getByRole("button")
      expect(equipmentCard).toHaveAttribute("tabindex", "0")
    })

    it("supports keyboard navigation", async () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      // Tab navigation should work
      await user.tab()
      expect(document.activeElement).toHaveAttribute("placeholder", "Search equipment...")
      
      await user.tab()
      expect(document.activeElement).toHaveTextContent("Filters")
    })

    it("provides screen reader friendly content", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      // Status indicators should have proper labels
      const statusIndicator = screen.getByText("Connected").closest("[role='status']")
      expect(statusIndicator).toBeInTheDocument()
    })
  })

  describe("Electrical Engineering Features", () => {
    it("displays electrical measurements with proper units", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("480")).toBeInTheDocument() // Voltage
      expect(screen.getByText("V")).toBeInTheDocument() // Voltage unit
      expect(screen.getByText("250")).toBeInTheDocument() // Current  
      expect(screen.getByText("A")).toBeInTheDocument() // Current unit
    })

    it("shows voltage class badge for electrical classification", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("LV")).toBeInTheDocument() // Low Voltage
    })

    it("displays equipment priority for electrical safety", () => {
      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("critical")).toBeInTheDocument()
    })

    it("shows offline status for electrical equipment monitoring", () => {
      const offlineEquipment = {
        ...mockEquipment,
        isOnline: false,
      }

      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        equipment: [offlineEquipment],
        filteredEquipment: [offlineEquipment],
      })

      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("Offline")).toBeInTheDocument()
    })
  })

  describe("Performance", () => {
    it("renders large equipment lists efficiently", () => {
      const manyEquipment = Array.from({ length: 100 }, (_, i) => ({
        ...mockEquipment,
        id: `eq-${i.toString().padStart(3, "0")}`,
        name: `Equipment ${i + 1}`,
      }))

      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        equipment: manyEquipment,
        filteredEquipment: manyEquipment.slice(0, 50), // Simulated filtering
      })

      render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("Showing 50 of 100 equipment")).toBeInTheDocument()
    })

    it("handles real-time updates without full re-renders", () => {
      const { rerender } = render(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      const updatedEquipment = {
        ...mockEquipment,
        measurements: {
          ...mockEquipment.measurements,
          current: 275, // Updated current
        },
      }

      vi.mocked(useEquipmentDashboard).mockReturnValue({
        ...mockHookReturn,
        equipment: [updatedEquipment],
        filteredEquipment: [updatedEquipment],
      })

      rerender(
        <TestWrapper>
          <EquipmentDashboard />
        </TestWrapper>
      )

      expect(screen.getByText("275")).toBeInTheDocument()
    })
  })
})