/**
 * Equipment Dashboard Custom Hook
 * 
 * Centralized hook for Equipment Dashboard organism state management,
 * combining server state (React Query) with client state (Zustand).
 * 
 * Features:
 * - Unified state management interface
 * - Real-time data synchronization
 * - Performance optimized operations
 * - Error handling and loading states
 * - TypeScript strict mode compliance
 * - Professional electrical engineering integration
 */

import { useCallback, useEffect, useMemo } from "react"
import { toast } from "sonner"

import { 
  useEquipmentDashboardStore, 
  useEquipmentDashboardSelectors 
} from "@/stores/equipmentDashboardStore"
import {
  useEquipmentList,
  useEquipmentDetail,
  useEquipmentAlerts,
  useEquipmentHealthSummary,
  useEquipmentAction,
  useAcknowledgeAlert,
  useBulkEquipmentAction,
  useRealTimeEquipmentData,
} from "@/hooks/api/useEquipment"
import type { 
  Equipment, 
  EquipmentFilters, 
  DashboardLayout, 
  DashboardConfig,
  EquipmentAlert,
  EquipmentActions
} from "./EquipmentDashboardTypes"

export interface UseEquipmentDashboardOptions {
  /** Enable real-time data updates */
  enableRealTime?: boolean
  /** Custom refetch interval in milliseconds */
  refetchInterval?: number
  /** Enable automatic alert notifications */
  enableNotifications?: boolean
  /** Maximum number of equipment items to display */
  maxItems?: number
}

export interface UseEquipmentDashboardReturn {
  // Data
  equipment: ReadonlyArray<Equipment>
  filteredEquipment: ReadonlyArray<Equipment>
  selectedEquipment: Equipment | null
  alerts: ReadonlyArray<EquipmentAlert>
  healthSummary: any // Equipment health summary
  
  // State
  loading: boolean
  error: string | null
  isRefreshing: boolean
  connectionStatus: "connected" | "disconnected" | "reconnecting"
  hasUnacknowledgedAlerts: boolean
  
  // UI State
  layout: DashboardLayout
  config: DashboardConfig
  filters: EquipmentFilters
  selectedEquipmentIds: ReadonlyArray<string>
  isFilterPanelOpen: boolean
  bulkActionMode: boolean
  searchQuery: string
  
  // Computed State
  filteredCount: number
  totalCount: number
  activeFilterCount: number
  
  // Actions - Equipment Management
  selectEquipment: (equipmentId: string | null) => void
  executeEquipmentAction: (equipmentId: string, action: EquipmentActions) => void
  executeBulkAction: (action: EquipmentActions) => void
  refreshEquipment: () => void
  
  // Actions - UI Management
  updateLayout: (layout: Partial<DashboardLayout>) => void
  updateConfig: (config: Partial<DashboardConfig>) => void
  updateFilters: (filters: Partial<EquipmentFilters>) => void
  resetFilters: () => void
  toggleFilterPanel: () => void
  
  // Actions - Selection Management
  toggleEquipmentSelection: (equipmentId: string) => void
  selectAllEquipment: () => void
  clearSelection: () => void
  toggleBulkActionMode: () => void
  
  // Actions - Search
  setSearchQuery: (query: string) => void
  
  // Actions - Alert Management
  acknowledgeAlert: (alertId: string) => void
  markAllAlertsRead: () => void
}

export function useEquipmentDashboard(
  options: UseEquipmentDashboardOptions = {}
): UseEquipmentDashboardReturn {
  const {
    enableRealTime = true,
    refetchInterval = 30000,
    enableNotifications = true,
    maxItems = 100,
  } = options

  // Zustand store state and actions
  const store = useEquipmentDashboardStore()
  const selectors = useEquipmentDashboardSelectors()
  
  // Server state queries
  const equipmentListQuery = useEquipmentList(store.filters, {
    refetchInterval,
    enabled: true,
  })
  
  const selectedEquipmentQuery = useEquipmentDetail(store.selectedEquipmentId, {
    enabled: !!store.selectedEquipmentId,
    refetchInterval: enableRealTime ? 15000 : 60000,
  })
  
  const alertsQuery = useEquipmentAlerts({}, {
    refetchInterval: 30000,
    enabled: true,
  })
  
  const healthSummaryQuery = useEquipmentHealthSummary({
    refetchInterval: 60000,
    enabled: true,
  })
  
  // Real-time data for selected equipment
  const realTimeQuery = useRealTimeEquipmentData(
    store.selectedEquipmentId,
    enableRealTime && !!store.selectedEquipmentId
  )
  
  // Mutations
  const equipmentActionMutation = useEquipmentAction()
  const bulkActionMutation = useBulkEquipmentAction()
  const acknowledgeAlertMutation = useAcknowledgeAlert()
  
  // Compute filtered equipment
  const filteredEquipment = useMemo(() => {
    if (!equipmentListQuery.data?.equipment) return []
    
    return equipmentListQuery.data.equipment
      .filter(equipment => selectors.equipmentMatchesFilters(equipment))
      .slice(0, maxItems)
  }, [equipmentListQuery.data?.equipment, selectors, maxItems])
  
  // Update connection status based on query states
  useEffect(() => {
    if (equipmentListQuery.isError || alertsQuery.isError) {
      store.setConnectionStatus("disconnected")
    } else if (equipmentListQuery.isFetching || alertsQuery.isFetching) {
      if (store.connectionStatus === "disconnected") {
        store.setConnectionStatus("reconnecting")
      }
    } else if (equipmentListQuery.isSuccess && alertsQuery.isSuccess) {
      store.setConnectionStatus("connected")
    }
  }, [
    equipmentListQuery.isError,
    equipmentListQuery.isFetching,
    equipmentListQuery.isSuccess,
    alertsQuery.isError,
    alertsQuery.isFetching,
    alertsQuery.isSuccess,
    store,
  ])
  
  // Handle new alerts notifications
  useEffect(() => {
    if (!enableNotifications || !alertsQuery.data?.alerts) return
    
    const newAlerts = alertsQuery.data.alerts.filter(
      alert => 
        !alert.acknowledged &&
        !store.unacknowledgedAlerts.includes(alert.id) &&
        alert.severity === "critical"
    )
    
    newAlerts.forEach(alert => {
      toast.error(alert.title, {
        description: alert.message,
        duration: 10000,
        action: {
          label: "Acknowledge",
          onClick: () => acknowledgeAlert(alert.id),
        },
      })
    })
    
    // Update store with new unacknowledged alerts
    const allUnacknowledged = alertsQuery.data.alerts
      .filter(alert => !alert.acknowledged)
      .map(alert => alert.id)
    
    if (allUnacknowledged.length !== store.unacknowledgedAlerts.length) {
      store.unacknowledgedAlerts.forEach(id => {
        if (!allUnacknowledged.includes(id)) {
          store.acknowledgeAlert(id)
        }
      })
    }
  }, [alertsQuery.data?.alerts, enableNotifications, store, acknowledgeAlertMutation])
  
  // Actions
  const selectEquipment = useCallback((equipmentId: string | null) => {
    store.setSelectedEquipment(equipmentId)
  }, [store])
  
  const executeEquipmentAction = useCallback(
    async (equipmentId: string, action: EquipmentActions) => {
      try {
        await equipmentActionMutation.mutateAsync({
          equipmentId,
          action,
        })
      } catch (error) {
        console.error("Equipment action failed:", error)
      }
    },
    [equipmentActionMutation]
  )
  
  const executeBulkAction = useCallback(
    async (action: EquipmentActions) => {
      if (store.selectedEquipmentIds.length === 0) {
        toast.warning("No equipment selected for bulk action")
        return
      }
      
      try {
        await bulkActionMutation.mutateAsync({
          equipmentIds: store.selectedEquipmentIds,
          action,
        })
        store.clearSelection()
      } catch (error) {
        console.error("Bulk action failed:", error)
      }
    },
    [bulkActionMutation, store]
  )
  
  const refreshEquipment = useCallback(() => {
    store.setRefreshing(true)
    Promise.all([
      equipmentListQuery.refetch(),
      alertsQuery.refetch(),
      healthSummaryQuery.refetch(),
    ]).finally(() => {
      store.setRefreshing(false)
      store.updateDataTimestamp()
    })
  }, [equipmentListQuery, alertsQuery, healthSummaryQuery, store])
  
  const updateLayout = useCallback(
    (layoutUpdate: Partial<DashboardLayout>) => {
      store.setLayout(layoutUpdate)
    },
    [store]
  )
  
  const updateConfig = useCallback(
    (configUpdate: Partial<DashboardConfig>) => {
      store.setConfig(configUpdate)
    },
    [store]
  )
  
  const updateFilters = useCallback(
    (filterUpdate: Partial<EquipmentFilters>) => {
      store.setFilters(filterUpdate)
    },
    [store]
  )
  
  const resetFilters = useCallback(() => {
    store.resetFilters()
  }, [store])
  
  const toggleFilterPanel = useCallback(() => {
    store.toggleFilterPanel()
  }, [store])
  
  const toggleEquipmentSelection = useCallback(
    (equipmentId: string) => {
      store.toggleEquipmentSelection(equipmentId)
    },
    [store]
  )
  
  const selectAllEquipment = useCallback(() => {
    const allIds = filteredEquipment.map(equipment => equipment.id)
    store.selectAllEquipment(allIds)
  }, [filteredEquipment, store])
  
  const clearSelection = useCallback(() => {
    store.clearSelection()
  }, [store])
  
  const toggleBulkActionMode = useCallback(() => {
    store.setBulkActionMode(!store.bulkActionMode)
  }, [store])
  
  const setSearchQuery = useCallback(
    (query: string) => {
      store.setSearchQuery(query)
    },
    [store]
  )
  
  const acknowledgeAlert = useCallback(
    async (alertId: string) => {
      try {
        await acknowledgeAlertMutation.mutateAsync(alertId)
        store.acknowledgeAlert(alertId)
      } catch (error) {
        console.error("Failed to acknowledge alert:", error)
      }
    },
    [acknowledgeAlertMutation, store]
  )
  
  const markAllAlertsRead = useCallback(() => {
    store.markAllAlertsRead()
  }, [store])
  
  // Computed values
  const loading = equipmentListQuery.isLoading || healthSummaryQuery.isLoading
  const error = equipmentListQuery.error?.message || 
                healthSummaryQuery.error?.message || 
                null
  
  return {
    // Data
    equipment: equipmentListQuery.data?.equipment ?? [],
    filteredEquipment,
    selectedEquipment: selectedEquipmentQuery.data || null,
    alerts: alertsQuery.data?.alerts ?? [],
    healthSummary: healthSummaryQuery.data,
    
    // State
    loading,
    error,
    isRefreshing: store.isRefreshing || equipmentListQuery.isFetching,
    connectionStatus: store.connectionStatus,
    hasUnacknowledgedAlerts: selectors.hasUnacknowledgedAlerts(),
    
    // UI State
    layout: store.layout,
    config: store.config,
    filters: store.filters,
    selectedEquipmentIds: store.selectedEquipmentIds,
    isFilterPanelOpen: store.isFilterPanelOpen,
    bulkActionMode: store.bulkActionMode,
    searchQuery: store.searchQuery,
    
    // Computed State
    filteredCount: filteredEquipment.length,
    totalCount: equipmentListQuery.data?.total ?? 0,
    activeFilterCount: selectors.getActiveFilterCount(),
    
    // Actions
    selectEquipment,
    executeEquipmentAction,
    executeBulkAction,
    refreshEquipment,
    updateLayout,
    updateConfig,
    updateFilters,
    resetFilters,
    toggleFilterPanel,
    toggleEquipmentSelection,
    selectAllEquipment,
    clearSelection,
    toggleBulkActionMode,
    setSearchQuery,
    acknowledgeAlert,
    markAllAlertsRead,
  }
}