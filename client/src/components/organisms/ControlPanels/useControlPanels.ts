/**
 * Control Panels Custom Hook
 * 
 * Custom hook combining server and client state management for Control Panels organism.
 * Provides unified interface for electrical system control functionality.
 * 
 * Features:
 * - Unified state management (server + client)
 * - Real-time control system updates
 * - Safety interlock management
 * - Professional electrical engineering context
 * - Error boundary integration
 * - Performance optimized with selective subscriptions
 * - TypeScript strict mode compliance
 */

import { useCallback, useMemo, useEffect } from "react"
import { toast } from "sonner"

// Server state hooks
import {
  useControlGroups,
  useControlEquipment,
  useControlInterlocks,
  useControlAlerts,
  useControlSession,
  useControlLogs,
  useControlAction,
  useBulkControlAction,
  useGroupAction,
  useSequenceExecution,
  useEmergencyStop,
  useInterlockBypass,
  useAcknowledgeControlAlert,
  useMarkAllControlAlertsRead,
  useCreateControlSession,
  useEndControlSession,
} from "@/hooks/api/useControlPanels"

// Client state hooks
import {
  useControlPanelsStore,
  useControlPanelsLayout,
  useControlPanelsConfig,
  useControlPanelsFilters,
  useControlPanelsSelection,
  useControlPanelsSession,
  useControlPanelsSafety,
  useControlPanelsConnection,
  useControlPanelsPanels,
  useControlPanelsComputed,
  useControlPanelsLayoutActions,
  useControlPanelsFilterActions,
  useControlPanelsSelectionActions,
  useControlPanelsSafetyActions,
  useControlPanelsSessionActions,
} from "@/stores/controlPanelsStore"

import type {
  ControlGroup,
  ControlEquipment,
  ControlSession,
  ControlAlert,
  ControlLogEntry,
  ControlAction,
  SafetyInterlock,
  ControlFilters,
  ControlPanelsLayout,
  ControlPanelsConfig,
  ControlSequenceStep,
} from "./ControlPanelsTypes"

// Hook options interface
interface UseControlPanelsOptions {
  readonly enabled?: boolean
  readonly autoRefresh?: boolean
  readonly refreshInterval?: number
  readonly enableRealTime?: boolean
}

// Unified hook interface
export interface UseControlPanelsReturn {
  // Server Data
  readonly controlGroups: ReadonlyArray<ControlGroup>
  readonly filteredEquipment: ReadonlyArray<ControlEquipment>
  readonly selectedEquipment: ReadonlyArray<ControlEquipment>
  readonly interlocks: ReadonlyArray<SafetyInterlock>
  readonly alerts: ReadonlyArray<ControlAlert>
  readonly logs: ReadonlyArray<ControlLogEntry>
  readonly activeSession: ControlSession | null
  
  // Loading States
  readonly loading: boolean
  readonly refreshing: boolean
  readonly isControlling: boolean
  readonly error: string | null
  
  // Computed Data
  readonly healthSummary: {
    readonly total: number
    readonly operational: number
    readonly faulted: number
    readonly maintenance: number
    readonly offline: number
    readonly interlocked: number
  }
  readonly alertSummary: {
    readonly total: number
    readonly critical: number
    readonly warning: number
    readonly unacknowledged: number
  }
  readonly sessionSummary: {
    readonly isActive: boolean
    readonly timeRemaining: number | null
    readonly permissionLevel: string | null
    readonly warnings: ReadonlyArray<string>
  }
  
  // Client State
  readonly layout: ControlPanelsLayout
  readonly config: ControlPanelsConfig
  readonly filters: ControlFilters
  readonly selection: {
    readonly selectedEquipmentIds: ReadonlyArray<string>
    readonly selectedGroupIds: ReadonlyArray<string>
    readonly bulkActionMode: boolean
  }
  readonly safety: {
    readonly pendingActions: ReadonlyArray<any>
    readonly emergencyStopActive: boolean
    readonly interlockOverrides: ReadonlyArray<any>
  }
  readonly panels: {
    readonly isFilterPanelOpen: boolean
    readonly isConfigPanelOpen: boolean
    readonly isLogPanelOpen: boolean
  }
  
  // Computed State  
  readonly filteredCount: number
  readonly totalCount: number
  readonly activeFilterCount: number
  readonly hasUnacknowledgedAlerts: boolean
  readonly hasActiveInterlocks: boolean
  readonly connectionStatus: "connected" | "disconnected" | "reconnecting"
  
  // Actions
  readonly executeControlAction: (equipmentId: string, action: ControlAction, confirmationCode?: string) => Promise<void>
  readonly executeBulkAction: (equipmentIds: ReadonlyArray<string>, action: ControlAction, confirmationCode?: string) => Promise<void>
  readonly executeGroupAction: (groupId: string, action: ControlAction, confirmationCode?: string) => Promise<void>
  readonly executeSequence: (groupId: string, sequence: ReadonlyArray<ControlSequenceStep>, confirmationCode?: string) => Promise<void>
  readonly activateEmergencyStop: (reason: string, scope?: "all" | "group" | "equipment", targetId?: string) => Promise<void>
  readonly bypassInterlock: (interlockId: string, reason: string) => Promise<void>
  readonly acknowledgeAlert: (alertId: string) => Promise<void>
  readonly acknowledgeAllAlerts: () => Promise<void>
  readonly startSession: () => Promise<void>
  readonly endSession: () => Promise<void>
  readonly refreshData: () => Promise<void>
  
  // UI Actions
  readonly updateLayout: (layout: Partial<ControlPanelsLayout>) => void
  readonly updateConfig: (config: Partial<ControlPanelsConfig>) => void
  readonly updateFilters: (filters: Partial<ControlFilters>) => void
  readonly resetFilters: () => void
  readonly setSearchQuery: (query: string) => void
  readonly toggleFilterPanel: () => void
  readonly toggleConfigPanel: () => void
  readonly toggleLogPanel: () => void
  readonly selectEquipment: (equipmentId: string) => void
  readonly selectAllEquipment: () => void
  readonly clearSelection: () => void
  readonly toggleBulkActionMode: () => void
}

export const useControlPanels = (options: UseControlPanelsOptions = {}): UseControlPanelsReturn => {
  const {
    enabled = true,
    autoRefresh = true,
    refreshInterval = 10000,
    enableRealTime = true,
  } = options

  // Client state
  const layout = useControlPanelsLayout()
  const config = useControlPanelsConfig()
  const filters = useControlPanelsFilters()
  const selection = useControlPanelsSelection()
  const sessionState = useControlPanelsSession()
  const safety = useControlPanelsSafety()
  const connection = useControlPanelsConnection()
  const panels = useControlPanelsPanels()
  const computed = useControlPanelsComputed()
  
  // Client actions
  const layoutActions = useControlPanelsLayoutActions()
  const filterActions = useControlPanelsFilterActions()
  const selectionActions = useControlPanelsSelectionActions()
  const safetyActions = useControlPanelsSafetyActions()
  const sessionActions = useControlPanelsSessionActions()
  
  // Store access for connection updates
  const updateConnectionStatus = useControlPanelsStore(state => state.setConnectionStatus)
  const updateLastUpdate = useControlPanelsStore(state => state.updateLastUpdate)
  
  // Server queries
  const {
    data: controlGroupsData,
    isLoading: groupsLoading,
    error: groupsError,
    refetch: refetchGroups,
  } = useControlGroups(filters, {
    enabled,
    refetchInterval: autoRefresh ? refreshInterval : false,
  })
  
  const {
    data: equipmentData,
    isLoading: equipmentLoading,
    error: equipmentError,
    refetch: refetchEquipment,
  } = useControlEquipment(filters, {
    enabled,
    refetchInterval: autoRefresh ? refreshInterval : false,
  })
  
  const {
    data: interlocksData,
    isLoading: interlocksLoading,
    refetch: refetchInterlocks,
  } = useControlInterlocks(undefined, {
    enabled,
  })
  
  const {
    data: alertsData,
    isLoading: alertsLoading,
    refetch: refetchAlerts,
  } = useControlAlerts(true, {
    enabled,
  })
  
  const {
    data: sessionData,
    isLoading: sessionLoading,
    refetch: refetchSession,
  } = useControlSession({
    enabled,
  })
  
  const {
    data: logsData,
    isLoading: logsLoading,
  } = useControlLogs({}, {
    enabled,
  })
  
  // Server mutations
  const controlActionMutation = useControlAction()
  const bulkActionMutation = useBulkControlAction()
  const groupActionMutation = useGroupAction()
  const sequenceExecutionMutation = useSequenceExecution()
  const emergencyStopMutation = useEmergencyStop()
  const interlockBypassMutation = useInterlockBypass()
  const acknowledgeAlertMutation = useAcknowledgeControlAlert()
  const acknowledgeAllAlertsMutation = useMarkAllControlAlertsRead()
  const createSessionMutation = useCreateControlSession()
  const endSessionMutation = useEndControlSession()
  
  // Extract data with fallbacks
  const controlGroups = controlGroupsData?.controlGroups || []
  const equipment = equipmentData?.equipment || []
  const interlocks = interlocksData || []
  const alerts = alertsData?.alerts || []
  const logs = logsData?.pages.flatMap(page => page.logs) || []
  const activeSession = sessionData || sessionState.activeSession
  
  // Loading states
  const loading = groupsLoading || equipmentLoading || interlocksLoading || alertsLoading
  const refreshing = controlActionMutation.isPending || 
                    bulkActionMutation.isPending || 
                    groupActionMutation.isPending ||
                    sequenceExecutionMutation.isPending
  const isControlling = refreshing || emergencyStopMutation.isPending
  const error = groupsError?.message || equipmentError?.message || null
  
  // Selected equipment data
  const selectedEquipment = useMemo(() => {
    return equipment.filter(eq => selection.selectedEquipmentIds.includes(eq.id))
  }, [equipment, selection.selectedEquipmentIds])
  
  // Filtered equipment (applying client-side search)
  const filteredEquipment = useMemo(() => {
    let filtered = equipment
    
    // Apply search query
    if (filters.searchQuery) {
      const query = filters.searchQuery.toLowerCase()
      filtered = filtered.filter(eq => 
        eq.name.toLowerCase().includes(query) ||
        eq.type.toLowerCase().includes(query) ||
        eq.location.toLowerCase().includes(query)
      )
    }
    
    return filtered
  }, [equipment, filters.searchQuery])
  
  // Health Summary
  const healthSummary = useMemo(() => {
    const total = equipment.length
    const operational = equipment.filter(eq => eq.state === "running" || eq.state === "automatic").length
    const faulted = equipment.filter(eq => eq.state === "fault" || eq.state === "tripped").length
    const maintenance = equipment.filter(eq => eq.state === "maintenance" || eq.state === "test_mode").length
    const offline = equipment.filter(eq => !eq.isOnline).length
    const interlocked = equipment.filter(eq => eq.state === "interlocked").length
    
    return {
      total,
      operational,
      faulted,
      maintenance,
      offline,
      interlocked,
    }
  }, [equipment])
  
  // Alert Summary
  const alertSummary = useMemo(() => {
    const total = alerts.length
    const critical = alerts.filter(alert => alert.severity === "critical").length
    const warning = alerts.filter(alert => alert.severity === "warning").length
    const unacknowledged = alerts.filter(alert => !alert.acknowledged).length
    
    return {
      total,
      critical,
      warning,
      unacknowledged,
    }
  }, [alerts])
  
  // Session Summary
  const sessionSummary = useMemo(() => {
    return {
      isActive: Boolean(activeSession),
      timeRemaining: sessionState.sessionTimeout,
      permissionLevel: activeSession?.permissionLevel || null,
      warnings: sessionState.sessionWarnings,
    }
  }, [activeSession, sessionState.sessionTimeout, sessionState.sessionWarnings])
  
  // Computed state
  const filteredCount = filteredEquipment.length
  const totalCount = equipment.length
  const activeFilterCount = Object.keys(filters).filter(key => {
    const value = filters[key as keyof ControlFilters]
    return Array.isArray(value) ? value.length > 0 : Boolean(value)
  }).length
  const hasUnacknowledgedAlerts = alertSummary.unacknowledged > 0
  const hasActiveInterlocks = interlocks.some(interlock => interlock.active)
  
  // Update connection status based on query states
  useEffect(() => {
    if (groupsError || equipmentError) {
      updateConnectionStatus("disconnected")
    } else if (loading) {
      updateConnectionStatus("reconnecting")
    } else {
      updateConnectionStatus("connected")
      updateLastUpdate()
    }
  }, [loading, groupsError, equipmentError, updateConnectionStatus, updateLastUpdate])
  
  // Update active session in store
  useEffect(() => {
    if (sessionData !== undefined) {
      sessionActions.setActiveSession(sessionData)
    }
  }, [sessionData, sessionActions])
  
  // Action implementations
  const executeControlAction = useCallback(async (
    equipmentId: string,
    action: ControlAction,
    confirmationCode?: string
  ): Promise<void> => {
    try {
      // Add pending action if confirmation required
      if (config.requireConfirmation && !confirmationCode) {
        safetyActions.addPendingAction(equipmentId, action)
        return
      }
      
      await controlActionMutation.mutateAsync({
        equipmentId,
        action,
        confirmationCode,
      })
    } catch (error) {
      // Error handling is done in the mutation
      throw error
    }
  }, [config.requireConfirmation, controlActionMutation, safetyActions])
  
  const executeBulkAction = useCallback(async (
    equipmentIds: ReadonlyArray<string>,
    action: ControlAction,
    confirmationCode?: string
  ): Promise<void> => {
    try {
      await bulkActionMutation.mutateAsync({
        equipmentIds,
        action,
        confirmationCode,
      })
      
      // Clear selections after successful bulk action
      selectionActions.clearAllSelections()
    } catch (error) {
      throw error
    }
  }, [bulkActionMutation, selectionActions])
  
  const executeGroupAction = useCallback(async (
    groupId: string,
    action: ControlAction,
    confirmationCode?: string
  ): Promise<void> => {
    try {
      await groupActionMutation.mutateAsync({
        groupId,
        action,
        confirmationCode,
      })
    } catch (error) {
      throw error
    }
  }, [groupActionMutation])
  
  const executeSequence = useCallback(async (
    groupId: string,
    sequence: ReadonlyArray<ControlSequenceStep>,
    confirmationCode?: string
  ): Promise<void> => {
    try {
      await sequenceExecutionMutation.mutateAsync({
        groupId,
        sequence,
        confirmationCode,
      })
    } catch (error) {
      throw error
    }
  }, [sequenceExecutionMutation])
  
  const activateEmergencyStop = useCallback(async (
    reason: string,
    scope: "all" | "group" | "equipment" = "all",
    targetId?: string
  ): Promise<void> => {
    try {
      safetyActions.activateEmergencyStop()
      await emergencyStopMutation.mutateAsync({
        reason,
        scope,
        targetId,
      })
    } catch (error) {
      safetyActions.deactivateEmergencyStop() // Rollback on failure
      throw error
    }
  }, [emergencyStopMutation, safetyActions])
  
  const bypassInterlock = useCallback(async (
    interlockId: string,
    reason: string
  ): Promise<void> => {
    try {
      await interlockBypassMutation.mutateAsync({
        interlockId,
        reason,
      })
    } catch (error) {
      throw error
    }
  }, [interlockBypassMutation])
  
  const acknowledgeAlert = useCallback(async (alertId: string): Promise<void> => {
    try {
      await acknowledgeAlertMutation.mutateAsync(alertId)
    } catch (error) {
      throw error
    }
  }, [acknowledgeAlertMutation])
  
  const acknowledgeAllAlerts = useCallback(async (): Promise<void> => {
    try {
      await acknowledgeAllAlertsMutation.mutateAsync()
    } catch (error) {
      throw error
    }
  }, [acknowledgeAllAlertsMutation])
  
  const startSession = useCallback(async (): Promise<void> => {
    try {
      await createSessionMutation.mutateAsync()
    } catch (error) {
      throw error
    }
  }, [createSessionMutation])
  
  const endSession = useCallback(async (): Promise<void> => {
    try {
      await endSessionMutation.mutateAsync()
    } catch (error) {
      throw error
    }
  }, [endSessionMutation])
  
  const refreshData = useCallback(async (): Promise<void> => {
    try {
      await Promise.all([
        refetchGroups(),
        refetchEquipment(),
        refetchInterlocks(),
        refetchAlerts(),
        refetchSession(),
      ])
      
      toast.success("Control data refreshed")
    } catch (error: any) {
      toast.error("Failed to refresh data")
      throw error
    }
  }, [refetchGroups, refetchEquipment, refetchInterlocks, refetchAlerts, refetchSession])
  
  const selectEquipment = useCallback((equipmentId: string) => {
    selectionActions.toggleEquipmentSelection(equipmentId)
  }, [selectionActions])
  
  const selectAllEquipment = useCallback(() => {
    const allIds = filteredEquipment.map(eq => eq.id)
    selectionActions.selectAllEquipment(allIds)
  }, [filteredEquipment, selectionActions])
  
  const clearSelection = useCallback(() => {
    selectionActions.clearAllSelections()
  }, [selectionActions])
  
  return {
    // Server Data
    controlGroups,
    filteredEquipment,
    selectedEquipment,
    interlocks,
    alerts,
    logs,
    activeSession,
    
    // Loading States
    loading,
    refreshing,
    isControlling,
    error,
    
    // Computed Data
    healthSummary,
    alertSummary,
    sessionSummary,
    
    // Client State
    layout,
    config,
    filters,
    selection,
    safety,
    panels,
    
    // Computed State
    filteredCount,
    totalCount,
    activeFilterCount,
    hasUnacknowledgedAlerts,
    hasActiveInterlocks,
    connectionStatus: connection.connectionStatus,
    
    // Actions
    executeControlAction,
    executeBulkAction,
    executeGroupAction,
    executeSequence,
    activateEmergencyStop,
    bypassInterlock,
    acknowledgeAlert,
    acknowledgeAllAlerts,
    startSession,
    endSession,
    refreshData,
    
    // UI Actions
    updateLayout: layoutActions.updateLayout,
    updateConfig: layoutActions.updateConfig,
    updateFilters: filterActions.updateFilters,
    resetFilters: filterActions.resetFilters,
    setSearchQuery: filterActions.setSearchQuery,
    toggleFilterPanel: filterActions.toggleFilterPanel,
    toggleConfigPanel: () => {}, // Will be implemented with the panel actions
    toggleLogPanel: () => {}, // Will be implemented with the panel actions
    selectEquipment,
    selectAllEquipment,
    clearSelection,
    toggleBulkActionMode: selectionActions.toggleBulkActionMode,
  }
}