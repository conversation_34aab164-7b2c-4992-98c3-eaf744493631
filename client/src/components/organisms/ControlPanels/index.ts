/**
 * Control Panels Organism - Index
 * 
 * Barrel export for Control Panels organism components and utilities.
 * Provides a clean public API for the electrical control system management.
 */

// Main component
export { ControlPanels } from "./ControlPanels"

// Hook
export { useControlPanels } from "./useControlPanels"

// Types
export type {
  // Core Types
  ControlGroup,
  ControlEquipment,
  ControlSession,
  ControlAlert,
  ControlLogEntry,
  SafetyInterlock,
  ControlSequenceStep,
  
  // Enums
  ControlSystemType,
  ControlAction,
  ControlState,
  SafetyIntegrityLevel,
  ControlPermissionLevel,
  
  // Configuration
  ControlPanelsLayout,
  ControlPanelsConfig,
  ControlFilters,
  
  // Component Props
  ControlPanelsProps,
  ControlGroupCardProps,
  EquipmentControlProps,
  SafetyInterlockPanelProps,
  
  // Utility Types
  ControlEquipmentSummary,
  ControlGroupSummary,
} from "./ControlPanelsTypes"

// Constants and utilities
export {
  CONTROL_STATE_COLORS,
  CONTROL_SYSTEM_LABELS,
  SAFETY_LEVEL_LABELS,
  PERMISSION_LEVEL_LABELS,
  DEFAULT_CONTROL_LAYOUT,
  DEFAULT_CONTROL_CONFIG,
  isActionPermitted,
  hasActiveInterlocks,
  canBypassInterlock,
} from "./ControlPanelsTypes"