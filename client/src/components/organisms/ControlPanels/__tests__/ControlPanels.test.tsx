/**
 * Control Panels Organism Tests
 * 
 * Comprehensive test suite for Control Panels organism covering
 * functionality, accessibility, safety features, and electrical engineering contexts.
 * 
 * Features tested:
 * - Component rendering and interaction
 * - Real-time control system updates
 * - Safety interlock functionality
 * - Emergency stop procedures
 * - Control action execution
 * - Session management
 * - Accessibility compliance
 * - Electrical engineering contexts
 * - Error handling and edge cases
 * - Performance optimization
 */

import { describe, it, expect, vi, beforeEach, afterEach } from "vitest"
import { render, screen, fireEvent, waitFor, within } from "@testing-library/react"
import { userEvent } from "@testing-library/user-event"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { toast } from "sonner"

import { ControlPanels } from "../ControlPanels"
import { useControlPanels } from "../useControlPanels"
import type { 
  ControlGroup, 
  ControlEquipment, 
  ControlAlert, 
  SafetyInterlock, 
  ControlSession,
  ControlPanelsLayout,
} from "../ControlPanelsTypes"

// Mock the hooks
vi.mock("../useControlPanels")
vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
}))

// Mock Lucide React icons
vi.mock("lucide-react", () => ({
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
  Settings: () => <div data-testid="settings-icon" />,
  Filter: () => <div data-testid="filter-icon" />,
  RefreshCw: () => <div data-testid="refresh-icon" />,
  Bell: () => <div data-testid="bell-icon" />,
  Zap: () => <div data-testid="zap-icon" />,
  Play: () => <div data-testid="play-icon" />,
  Square: () => <div data-testid="square-icon" />,
  AlertOctagon: () => <div data-testid="alert-octagon-icon" />,
  Shield: () => <div data-testid="shield-icon" />,
  Activity: () => <div data-testid="activity-icon" />,
  Users: () => <div data-testid="users-icon" />,
  Clock: () => <div data-testid="clock-icon" />,
  Search: () => <div data-testid="search-icon" />,
  Grid: () => <div data-testid="grid-icon" />,
  List: () => <div data-testid="list-icon" />,
  ChevronDown: () => <div data-testid="chevron-down-icon" />,
  ChevronUp: () => <div data-testid="chevron-up-icon" />,
}))

// Test data
const mockInterlock: SafetyInterlock = {
  id: "interlock-001",
  name: "Emergency Stop Circuit",
  type: "hardware",
  safetyLevel: "SIL3",
  description: "Main emergency stop interlock",
  active: true,
  bypassable: false,
  bypassRequiresPermission: "administrator",
  conditions: [
    {
      id: "cond-001",
      description: "E-stop button not pressed",
      satisfied: false,
      required: true,
    },
  ],
  lastChecked: new Date(),
}

const mockEquipment: ControlEquipment = {
  id: "eq-001",
  name: "Main Feeder Breaker",
  type: "switchgear_control",
  state: "running",
  location: "Main Electrical Room",
  description: "Primary incoming feeder control",
  currentValue: 350,
  setpoint: 400,
  unit: "A",
  minValue: 0,
  maxValue: 500,
  isOnline: true,
  isControllable: true,
  lastUpdate: new Date(),
  interlocks: [mockInterlock],
  availableActions: ["start", "stop", "reset", "isolate"],
  requiredPermission: "operator",
  tags: ["critical", "main-feed"],
}

const mockGroup: ControlGroup = {
  id: "group-001",
  name: "Main Distribution",
  description: "Primary electrical distribution controls",
  equipment: [mockEquipment],
  groupType: "sequential",
  sequence: [],
  globalInterlocks: [],
  emergencyStop: true,
  manualOverride: false,
  expanded: false,
}

const mockAlert: ControlAlert = {
  id: "alert-001",
  equipmentId: "eq-001", 
  equipmentName: "Main Feeder Breaker",
  type: "interlock",
  severity: "critical",
  title: "Safety Interlock Active",
  message: "Emergency stop circuit is active",
  timestamp: new Date(),
  acknowledged: false,
  autoAcknowledge: false,
  requiresAction: true,
}

const mockSession: ControlSession = {
  id: "session-001",
  userId: "user-001",
  userName: "John Doe",
  permissionLevel: "engineer",
  startTime: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
  lastActivity: new Date(),
  ipAddress: "*************",
  userAgent: "test-agent",
  active: true,
}

const mockLayout: ControlPanelsLayout = {
  columns: 2,
  groupSize: "md",
  showDetails: true,
  showInterlocks: true,
  compactMode: false,
  groupBy: "type",
  sortBy: "name",
  sortOrder: "asc",
}

// Mock hook return value
const mockHookReturn = {
  // Data
  controlGroups: [mockGroup],
  filteredEquipment: [mockEquipment],
  selectedEquipment: [],
  interlocks: [mockInterlock],
  alerts: [mockAlert],
  logs: [],
  activeSession: mockSession,
  
  // Loading States
  loading: false,
  refreshing: false,
  isControlling: false,
  error: null,
  
  // Computed Data
  healthSummary: {
    total: 1,
    operational: 1,
    faulted: 0,
    maintenance: 0,
    offline: 0,
    interlocked: 0,
  },
  alertSummary: {
    total: 1,
    critical: 1,
    warning: 0,
    unacknowledged: 1,
  },
  sessionSummary: {
    isActive: true,
    timeRemaining: 1800000, // 30 minutes
    permissionLevel: "engineer",
    warnings: [],
  },
  
  // Client State
  layout: mockLayout,
  config: {
    requireConfirmation: true,
    confirmationTimeout: 10,
    showWarnings: true,
    enableSounds: true,
    logAllActions: true,
    autoRefresh: true,
    refreshInterval: 15,
    emergencyStopBehavior: "confirm_each",
    interlockDisplayMode: "active_only",
    sessionTimeout: 30,
  },
  filters: {},
  selection: {
    selectedEquipmentIds: [],
    selectedGroupIds: [],
    bulkActionMode: false,
  },
  safety: {
    pendingActions: [],
    emergencyStopActive: false,
    interlockOverrides: [],
  },
  panels: {
    isFilterPanelOpen: false,
    isConfigPanelOpen: false,
    isLogPanelOpen: false,
  },
  
  // Computed State
  filteredCount: 1,
  totalCount: 1,
  activeFilterCount: 0,
  hasUnacknowledgedAlerts: true,
  hasActiveInterlocks: true,
  connectionStatus: "connected" as const,
  
  // Actions (mocked)
  executeControlAction: vi.fn(),
  executeBulkAction: vi.fn(),
  executeGroupAction: vi.fn(),
  executeSequence: vi.fn(),
  activateEmergencyStop: vi.fn(),
  bypassInterlock: vi.fn(),
  acknowledgeAlert: vi.fn(),
  acknowledgeAllAlerts: vi.fn(),
  startSession: vi.fn(),
  endSession: vi.fn(),
  refreshData: vi.fn(),
  
  // UI Actions (mocked)
  updateLayout: vi.fn(),
  updateConfig: vi.fn(),
  updateFilters: vi.fn(),
  resetFilters: vi.fn(),
  setSearchQuery: vi.fn(),
  toggleFilterPanel: vi.fn(),
  toggleConfigPanel: vi.fn(),
  toggleLogPanel: vi.fn(),
  selectEquipment: vi.fn(),
  selectAllEquipment: vi.fn(),
  clearSelection: vi.fn(),
  toggleBulkActionMode: vi.fn(),
}

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe("ControlPanels", () => {
  const user = userEvent.setup()
  
  beforeEach(() => {
    vi.mocked(useControlPanels).mockReturnValue(mockHookReturn)
  })
  
  afterEach(() => {
    vi.clearAllMocks()
  })

  describe("Rendering", () => {
    it("renders control panels with header and control groups", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("Control Panels")).toBeInTheDocument()
      expect(screen.getByText("1 equipment units")).toBeInTheDocument()
      expect(screen.getByText("1 operational")).toBeInTheDocument()
      expect(screen.getByText("Main Distribution")).toBeInTheDocument()
    })

    it("shows safety interlock warnings", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("Active interlocks")).toBeInTheDocument()
      expect(screen.getByTestId("shield-icon")).toBeInTheDocument()
    })

    it("displays connection status indicator", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("connected")).toBeInTheDocument()
    })

    it("shows control session information when active", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("Control Session Active")).toBeInTheDocument()
      expect(screen.getByText("User: John Doe • Permission: Engineer")).toBeInTheDocument()
      expect(screen.getByText("30m remaining")).toBeInTheDocument()
    })

    it("renders control equipment with electrical engineering details", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Expand the group first
      const expandButton = screen.getByTestId("chevron-down-icon").closest("button")
      expect(expandButton).toBeInTheDocument()
    })
  })

  describe("Alert Management", () => {
    it("shows alert indicator with count", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const alertButton = screen.getByRole("button", { name: /alerts/i })
      expect(alertButton).toBeInTheDocument()
      
      // Should have unacknowledged alert indicator
      const alertIndicator = alertButton.querySelector(".bg-red-500")
      expect(alertIndicator).toBeInTheDocument()
      
      // Should show alert count
      expect(screen.getByText("1")).toBeInTheDocument() // Alert count badge
    })

    it("opens alerts panel when clicked", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const alertButton = screen.getByRole("button", { name: /alerts/i })
      await user.click(alertButton)

      await waitFor(() => {
        expect(screen.getByText("Active Alerts")).toBeInTheDocument()
        expect(screen.getByText("Safety Interlock Active")).toBeInTheDocument()
        expect(screen.getByText("Emergency stop circuit is active")).toBeInTheDocument()
      })
    })

    it("handles alert acknowledgment", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Open alerts panel
      const alertButton = screen.getByRole("button", { name: /alerts/i })
      await user.click(alertButton)

      await waitFor(() => {
        const acknowledgeButton = screen.getByRole("button", { name: /acknowledge/i })
        expect(acknowledgeButton).toBeInTheDocument()
      })
    })

    it("handles acknowledge all alerts", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Open alerts panel
      const alertButton = screen.getByRole("button", { name: /alerts/i })
      await user.click(alertButton)

      await waitFor(() => {
        const markAllButton = screen.getByRole("button", { name: /mark all read/i })
        await user.click(markAllButton)
        
        expect(mockHookReturn.acknowledgeAllAlerts).toHaveBeenCalled()
      })
    })
  })

  describe("Emergency Stop", () => {
    it("renders emergency stop button", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const emergencyButton = screen.getByRole("button", { name: /emergency stop/i })
      expect(emergencyButton).toBeInTheDocument()
      expect(emergencyButton).not.toBeDisabled()
    })

    it("shows confirmation dialog when emergency stop clicked", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const emergencyButton = screen.getByRole("button", { name: /emergency stop/i })
      await user.click(emergencyButton)

      await waitFor(() => {
        expect(screen.getByText("Emergency Stop Confirmation")).toBeInTheDocument()
        expect(screen.getByText(/This will immediately stop ALL controlled equipment/)).toBeInTheDocument()
      })
    })

    it("executes emergency stop when confirmed", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const emergencyButton = screen.getByRole("button", { name: /emergency stop/i })
      await user.click(emergencyButton)

      await waitFor(() => {
        const confirmButton = screen.getByRole("button", { name: /activate emergency stop/i })
        fireEvent.click(confirmButton)
        
        expect(mockHookReturn.activateEmergencyStop).toHaveBeenCalledWith(
          "User initiated emergency stop", 
          "all"
        )
      })
    })

    it("disables emergency stop when already active", () => {
      vi.mocked(useControlPanels).mockReturnValue({
        ...mockHookReturn,
        safety: {
          ...mockHookReturn.safety,
          emergencyStopActive: true,
        },
      })

      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const emergencyButton = screen.getByRole("button", { name: /emergency stop/i })
      expect(emergencyButton).toBeDisabled()
      
      expect(screen.getByText("EMERGENCY STOP ACTIVE")).toBeInTheDocument()
    })
  })

  describe("Search and Filtering", () => {
    it("renders search input", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText("Search control equipment...")
      expect(searchInput).toBeInTheDocument()
    })

    it("handles search input changes", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const searchInput = screen.getByPlaceholderText("Search control equipment...")
      await user.type(searchInput, "breaker")
      
      expect(mockHookReturn.setSearchQuery).toHaveBeenCalledWith("breaker")
    })

    it("opens filter panel when clicked", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const filterButton = screen.getByRole("button", { name: /filters/i })
      await user.click(filterButton)
      
      expect(mockHookReturn.toggleFilterPanel).toHaveBeenCalled()
    })
  })

  describe("Configuration", () => {
    it("opens settings panel when clicked", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const settingsButton = screen.getByRole("button", { name: /settings/i })
      await user.click(settingsButton)

      await waitFor(() => {
        expect(screen.getByText("Panel Configuration")).toBeInTheDocument()
      })
    })

    it("handles layout configuration changes", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const settingsButton = screen.getByRole("button", { name: /settings/i })
      await user.click(settingsButton)

      await waitFor(() => {
        const showDetailsSwitch = screen.getByRole("switch", { name: /show details/i })
        await user.click(showDetailsSwitch)
        
        expect(mockHookReturn.updateLayout).toHaveBeenCalledWith({ showDetails: false })
      })
    })
  })

  describe("Control Group Interaction", () => {
    it("renders control groups with equipment counts", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("Main Distribution")).toBeInTheDocument()
      expect(screen.getByText("Primary electrical distribution controls")).toBeInTheDocument()
      expect(screen.getByText("1/1 operational")).toBeInTheDocument()
    })

    it("shows emergency stop button for groups that support it", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const groupEmergencyButton = screen.getByRole("button", { name: /e-stop/i })
      expect(groupEmergencyButton).toBeInTheDocument()
    })

    it("toggles group expansion when clicked", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const expandButton = screen.getByTestId("chevron-down-icon").closest("button")
      expect(expandButton).toBeInTheDocument()
      
      await user.click(expandButton!)
      
      // After clicking, should show equipment cards
      await waitFor(() => {
        expect(screen.getByText("Main Feeder Breaker")).toBeInTheDocument()
      })
    })
  })

  describe("Loading and Error States", () => {
    it("shows loading skeleton when loading", () => {
      vi.mocked(useControlPanels).mockReturnValue({
        ...mockHookReturn,
        loading: true,
        controlGroups: [],
      })

      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const skeletons = screen.getAllByRole("generic").filter(el => 
        el.className.includes("animate-pulse")
      )
      expect(skeletons.length).toBeGreaterThan(0)
    })

    it("shows empty state when no control groups found", () => {
      vi.mocked(useControlPanels).mockReturnValue({
        ...mockHookReturn,
        controlGroups: [],
        loading: false,
      })

      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("No Control Groups Found")).toBeInTheDocument()
      expect(screen.getByText("No control equipment is currently configured or available.")).toBeInTheDocument()
    })

    it("shows error state when error occurs", () => {
      vi.mocked(useControlPanels).mockReturnValue({
        ...mockHookReturn,
        error: "Failed to load control data",
      })

      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("Control Panels Error")).toBeInTheDocument()
      expect(screen.getByText("Failed to load control data")).toBeInTheDocument()
    })

    it("handles refresh action", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      const refreshButton = screen.getByRole("button", { name: /refresh/i })
      await user.click(refreshButton)
      
      expect(mockHookReturn.refreshData).toHaveBeenCalled()
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA labels and roles", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Check for main container
      expect(screen.getByTestId("control-panels")).toBeInTheDocument()
      
      // Check for accessible button labels
      expect(screen.getByRole("button", { name: /alerts/i })).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /emergency stop/i })).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /filters/i })).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /settings/i })).toBeInTheDocument()
      expect(screen.getByRole("button", { name: /refresh/i })).toBeInTheDocument()
    })

    it("supports keyboard navigation", async () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Tab navigation should work
      await user.tab()
      expect(document.activeElement).toHaveAttribute("placeholder", "Search control equipment...")
      
      await user.tab()
      expect(document.activeElement).toHaveTextContent("Filters")
    })

    it("provides screen reader friendly content", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Connection status should be accessible
      expect(screen.getByText("connected")).toBeInTheDocument()
      
      // Emergency stop should have proper labeling
      const emergencyButton = screen.getByRole("button", { name: /emergency stop/i })
      expect(emergencyButton).toBeInTheDocument()
    })
  })

  describe("Safety and Electrical Engineering Features", () => {
    it("displays safety interlock badges", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Should show interlock badge in group header
      const interlockBadge = screen.getByTestId("shield-icon")
      expect(interlockBadge).toBeInTheDocument()
      expect(screen.getByText("1")).toBeInTheDocument() // Interlock count
    })

    it("shows electrical system types correctly", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Expand group to see equipment details
      const expandButton = screen.getByTestId("chevron-down-icon").closest("button")
      expect(expandButton).toBeInTheDocument()
    })

    it("displays control states with proper styling", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Should show operational status
      expect(screen.getByText("1 operational")).toBeInTheDocument()
    })

    it("handles permission levels correctly", () => {
      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Session should show permission level
      expect(screen.getByText(/Permission: Engineer/)).toBeInTheDocument()
    })

    it("shows offline equipment status", () => {
      const offlineEquipment = {
        ...mockEquipment,
        isOnline: false,
      }

      const offlineGroup = {
        ...mockGroup,
        equipment: [offlineEquipment],
      }

      vi.mocked(useControlPanels).mockReturnValue({
        ...mockHookReturn,
        controlGroups: [offlineGroup],
        filteredEquipment: [offlineEquipment],
        healthSummary: {
          ...mockHookReturn.healthSummary,
          offline: 1,
          operational: 0,
        },
      })

      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("0 operational")).toBeInTheDocument()
    })
  })

  describe("Performance", () => {
    it("renders large control group lists efficiently", () => {
      const manyGroups = Array.from({ length: 20 }, (_, i) => ({
        ...mockGroup,
        id: `group-${i.toString().padStart(3, "0")}`,
        name: `Control Group ${i + 1}`,
      }))

      vi.mocked(useControlPanels).mockReturnValue({
        ...mockHookReturn,
        controlGroups: manyGroups,
        healthSummary: {
          ...mockHookReturn.healthSummary,
          total: 20,
        },
      })

      render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      expect(screen.getByText("20 equipment units")).toBeInTheDocument()
      expect(screen.getByText("Control Group 1")).toBeInTheDocument()
      expect(screen.getByText("Control Group 20")).toBeInTheDocument()
    })

    it("handles real-time updates efficiently", () => {
      const { rerender } = render(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Update with new current values
      const updatedEquipment = {
        ...mockEquipment,
        currentValue: 375, // Updated current
        lastUpdate: new Date(),
      }

      const updatedGroup = {
        ...mockGroup,
        equipment: [updatedEquipment],
      }

      vi.mocked(useControlPanels).mockReturnValue({
        ...mockHookReturn,
        controlGroups: [updatedGroup],
        filteredEquipment: [updatedEquipment],
      })

      rerender(
        <TestWrapper>
          <ControlPanels />
        </TestWrapper>
      )

      // Component should handle the update smoothly
      expect(screen.getByText("Main Distribution")).toBeInTheDocument()
    })
  })
})