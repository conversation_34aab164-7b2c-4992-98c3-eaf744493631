/**
 * Control Panels Organism Types
 * 
 * TypeScript type definitions for the Control Panels organism,
 * providing professional electrical system control interfaces with safety interlocks.
 * 
 * Features:
 * - Professional electrical engineering types
 * - IEEE/IEC safety standards compliance
 * - Safety interlock system definitions
 * - WCAG 2.1 AA accessibility support
 * - TypeScript strict mode compliance
 */

import type { LucideIcon } from "lucide-react"

// Control System Types
export type ControlSystemType = 
  | "motor_control"
  | "lighting_control" 
  | "hvac_control"
  | "security_control"
  | "fire_safety"
  | "emergency_power"
  | "switchgear_control"
  | "transformer_control"
  | "generator_control"
  | "ups_control"

// Control Actions
export type ControlAction = 
  | "start"
  | "stop"
  | "reset"
  | "test"
  | "isolate"
  | "energize"
  | "emergency_stop"
  | "manual_override"
  | "auto_mode"
  | "local_control"
  | "remote_control"
  | "maintenance_mode"

// Control States
export type ControlState = 
  | "running"
  | "stopped"
  | "fault"
  | "tripped"
  | "isolated"
  | "maintenance"
  | "test_mode"
  | "manual"
  | "automatic"
  | "emergency"
  | "locked_out"
  | "interlocked"

// Safety Levels per IEC 61508
export type SafetyIntegrityLevel = "SIL0" | "SIL1" | "SIL2" | "SIL3" | "SIL4"

// Permission Levels
export type ControlPermissionLevel = 
  | "view_only"
  | "operator"
  | "supervisor"
  | "engineer"
  | "maintenance"
  | "administrator"
  | "emergency_override"

// Safety Interlock
export interface SafetyInterlock {
  readonly id: string
  readonly name: string
  readonly type: "hardware" | "software" | "procedural"
  readonly safetyLevel: SafetyIntegrityLevel
  readonly description: string
  readonly active: boolean
  readonly bypassable: boolean
  readonly bypassRequiresPermission: ControlPermissionLevel
  readonly conditions: ReadonlyArray<{
    readonly id: string
    readonly description: string
    readonly satisfied: boolean
    readonly required: boolean
  }>
  readonly lastChecked: Date
}

// Control Sequence Step
export interface ControlSequenceStep {
  readonly id: string
  readonly sequence: number
  readonly name: string
  readonly description: string
  readonly action: ControlAction
  readonly targetId: string
  readonly delayMs: number
  readonly conditions: ReadonlyArray<string>
  readonly interlocks: ReadonlyArray<string>
  readonly required: boolean
  readonly completed: boolean
  readonly timestamp?: Date
}

// Control Equipment
export interface ControlEquipment {
  readonly id: string
  readonly name: string
  readonly type: ControlSystemType
  readonly state: ControlState
  readonly location: string
  readonly description?: string
  readonly currentValue?: number
  readonly setpoint?: number
  readonly unit?: string
  readonly minValue?: number
  readonly maxValue?: number
  readonly isOnline: boolean
  readonly isControllable: boolean
  readonly lastUpdate: Date
  readonly interlocks: ReadonlyArray<SafetyInterlock>
  readonly availableActions: ReadonlyArray<ControlAction>
  readonly requiredPermission: ControlPermissionLevel
  readonly icon?: LucideIcon
  readonly tags?: ReadonlyArray<string>
}

// Control Group
export interface ControlGroup {
  readonly id: string
  readonly name: string
  readonly description?: string
  readonly equipment: ReadonlyArray<ControlEquipment>
  readonly groupType: "sequential" | "parallel" | "conditional"
  readonly sequence: ReadonlyArray<ControlSequenceStep>
  readonly globalInterlocks: ReadonlyArray<SafetyInterlock>
  readonly emergencyStop: boolean
  readonly manualOverride: boolean
  readonly icon?: LucideIcon
  readonly expanded: boolean
}

// Control Session
export interface ControlSession {
  readonly id: string
  readonly userId: string
  readonly userName: string
  readonly permissionLevel: ControlPermissionLevel
  readonly startTime: Date
  readonly lastActivity: Date
  readonly ipAddress: string
  readonly userAgent: string
  readonly active: boolean
}

// Control Log Entry
export interface ControlLogEntry {
  readonly id: string
  readonly timestamp: Date
  readonly userId: string
  readonly userName: string
  readonly action: ControlAction
  readonly equipmentId: string
  readonly equipmentName: string
  readonly previousState: ControlState
  readonly newState: ControlState
  readonly successful: boolean
  readonly errorMessage?: string
  readonly sessionId: string
  readonly acknowledged: boolean
}

// Control Panels Layout
export interface ControlPanelsLayout {
  readonly columns: 1 | 2 | 3 | 4
  readonly groupSize: "sm" | "md" | "lg"
  readonly showDetails: boolean
  readonly showInterlocks: boolean
  readonly compactMode: boolean
  readonly groupBy: "type" | "location" | "priority" | "none"
  readonly sortBy: "name" | "type" | "state" | "lastUpdate"
  readonly sortOrder: "asc" | "desc"
}

// Control Panels Configuration
export interface ControlPanelsConfig {
  readonly requireConfirmation: boolean
  readonly confirmationTimeout: number // seconds
  readonly showWarnings: boolean
  readonly enableSounds: boolean
  readonly logAllActions: boolean
  readonly autoRefresh: boolean
  readonly refreshInterval: number // seconds
  readonly emergencyStopBehavior: "stop_all" | "stop_group" | "confirm_each"
  readonly interlockDisplayMode: "always" | "active_only" | "on_hover"
  readonly sessionTimeout: number // minutes
}

// Control Filters
export interface ControlFilters {
  readonly systemType?: ReadonlyArray<ControlSystemType>
  readonly state?: ReadonlyArray<ControlState>
  readonly location?: string
  readonly hasInterlocks?: boolean
  readonly isControllable?: boolean
  readonly isOnline?: boolean
  readonly hasAlerts?: boolean
  readonly searchQuery?: string
}

// Control Alert
export interface ControlAlert {
  readonly id: string
  readonly equipmentId: string
  readonly equipmentName: string
  readonly type: "interlock" | "fault" | "warning" | "emergency"
  readonly severity: "critical" | "warning" | "info"
  readonly title: string
  readonly message: string
  readonly timestamp: Date
  readonly acknowledged: boolean
  readonly acknowledgingUser?: string
  readonly acknowledgedAt?: Date
  readonly autoAcknowledge: boolean
  readonly requiresAction: boolean
}

// Control Panels Props
export interface ControlPanelsProps {
  /** Control groups to display */
  controlGroups: ReadonlyArray<ControlGroup>
  /** Layout configuration */
  layout?: ControlPanelsLayout
  /** Panel configuration */
  config?: ControlPanelsConfig
  /** Active filters */
  filters?: ControlFilters
  /** Active control alerts */
  alerts?: ReadonlyArray<ControlAlert>
  /** Current user session */
  session?: ControlSession | null
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Control action callback */
  onControlAction?: (equipmentId: string, action: ControlAction) => void
  /** Group action callback */
  onGroupAction?: (groupId: string, action: ControlAction) => void
  /** Sequence execution callback */
  onSequenceExecute?: (groupId: string, sequence: ReadonlyArray<ControlSequenceStep>) => void
  /** Emergency stop callback */
  onEmergencyStop?: () => void
  /** Interlock bypass callback */
  onInterlockBypass?: (interlockId: string, reason: string) => void
  /** Filter change callback */
  onFiltersChange?: (filters: ControlFilters) => void
  /** Layout change callback */
  onLayoutChange?: (layout: ControlPanelsLayout) => void
  /** Configuration change callback */
  onConfigChange?: (config: ControlPanelsConfig) => void
  /** Alert acknowledgment callback */
  onAlertAcknowledge?: (alertId: string) => void
  /** Refresh callback */
  onRefresh?: () => void
  /** Custom class name */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

// Control Group Card Props
export interface ControlGroupCardProps {
  readonly group: ControlGroup
  readonly session: ControlSession | null
  readonly config: ControlPanelsConfig
  readonly size: "sm" | "md" | "lg"
  readonly showDetails: boolean
  readonly showInterlocks: boolean
  readonly onControlAction: (equipmentId: string, action: ControlAction) => void
  readonly onGroupAction: (groupId: string, action: ControlAction) => void
  readonly onSequenceExecute: (groupId: string, sequence: ReadonlyArray<ControlSequenceStep>) => void
  readonly onToggleExpand: (groupId: string) => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Equipment Control Props
export interface EquipmentControlProps {
  readonly equipment: ControlEquipment
  readonly session: ControlSession | null
  readonly config: ControlPanelsConfig
  readonly size: "sm" | "md" | "lg"
  readonly showDetails: boolean
  readonly showInterlocks: boolean
  readonly onAction: (equipmentId: string, action: ControlAction) => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Safety Interlock Panel Props
export interface SafetyInterlockPanelProps {
  readonly interlocks: ReadonlyArray<SafetyInterlock>
  readonly equipmentId: string
  readonly session: ControlSession | null
  readonly onBypass: (interlockId: string, reason: string) => void
  readonly showDetails?: boolean
  readonly className?: string
  readonly "data-testid"?: string
}

// Utility Types
export type ControlEquipmentSummary = Pick<ControlEquipment, "id" | "name" | "type" | "state" | "isControllable">
export type ControlGroupSummary = Pick<ControlGroup, "id" | "name" | "groupType" | "emergencyStop">

// Status Mapping Utilities
export const CONTROL_STATE_COLORS: Record<ControlState, string> = {
  running: "text-green-600",
  stopped: "text-gray-600",
  fault: "text-red-600",
  tripped: "text-red-700",
  isolated: "text-orange-600",
  maintenance: "text-blue-600",
  test_mode: "text-purple-600",
  manual: "text-indigo-600",
  automatic: "text-teal-600",
  emergency: "text-red-800",
  locked_out: "text-gray-800",
  interlocked: "text-amber-600",
} as const

export const CONTROL_SYSTEM_LABELS: Record<ControlSystemType, string> = {
  motor_control: "Motor Control",
  lighting_control: "Lighting Control",
  hvac_control: "HVAC Control",
  security_control: "Security Control",
  fire_safety: "Fire Safety",
  emergency_power: "Emergency Power",
  switchgear_control: "Switchgear Control",
  transformer_control: "Transformer Control",
  generator_control: "Generator Control",
  ups_control: "UPS Control",
} as const

export const SAFETY_LEVEL_LABELS: Record<SafetyIntegrityLevel, string> = {
  SIL0: "SIL 0 (Non-safety)",
  SIL1: "SIL 1 (Low)",
  SIL2: "SIL 2 (Medium)",
  SIL3: "SIL 3 (High)",
  SIL4: "SIL 4 (Very High)",
} as const

export const PERMISSION_LEVEL_LABELS: Record<ControlPermissionLevel, string> = {
  view_only: "View Only",
  operator: "Operator",
  supervisor: "Supervisor", 
  engineer: "Engineer",
  maintenance: "Maintenance",
  administrator: "Administrator",
  emergency_override: "Emergency Override",
} as const

// Default Values
export const DEFAULT_CONTROL_LAYOUT: ControlPanelsLayout = {
  columns: 2,
  groupSize: "md",
  showDetails: true,
  showInterlocks: true,
  compactMode: false,
  groupBy: "type",
  sortBy: "name",
  sortOrder: "asc",
} as const

export const DEFAULT_CONTROL_CONFIG: ControlPanelsConfig = {
  requireConfirmation: true,
  confirmationTimeout: 10,
  showWarnings: true,
  enableSounds: true,
  logAllActions: true,
  autoRefresh: true,
  refreshInterval: 15,
  emergencyStopBehavior: "confirm_each",
  interlockDisplayMode: "active_only",
  sessionTimeout: 30,
} as const

// Validation Functions
export const isActionPermitted = (
  action: ControlAction,
  equipment: ControlEquipment,
  session: ControlSession | null
): boolean => {
  if (!session) return false
  
  const permissionLevels: Record<ControlPermissionLevel, number> = {
    view_only: 0,
    operator: 1,
    supervisor: 2,
    engineer: 3,
    maintenance: 4,
    administrator: 5,
    emergency_override: 6,
  }
  
  const requiredLevel = permissionLevels[equipment.requiredPermission]
  const userLevel = permissionLevels[session.permissionLevel]
  
  return userLevel >= requiredLevel
}

export const hasActiveInterlocks = (equipment: ControlEquipment): boolean => {
  return equipment.interlocks.some(interlock => interlock.active)
}

export const canBypassInterlock = (
  interlock: SafetyInterlock,
  session: ControlSession | null
): boolean => {
  if (!interlock.bypassable || !session) return false
  
  const permissionLevels: Record<ControlPermissionLevel, number> = {
    view_only: 0,
    operator: 1,
    supervisor: 2,
    engineer: 3,
    maintenance: 4,
    administrator: 5,
    emergency_override: 6,
  }
  
  const requiredLevel = permissionLevels[interlock.bypassRequiresPermission]
  const userLevel = permissionLevels[session.permissionLevel]
  
  return userLevel >= requiredLevel
}