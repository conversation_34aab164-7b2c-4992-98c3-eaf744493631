# Organisms - Quick Reference Guide

## Overview

This directory contains the **organism-level components** of the Ultimate Electrical Designer's atomic design system. Organisms are complex, complete interface sections that combine atoms and molecules to create sophisticated electrical engineering interfaces with professional-grade quality.

## Available Organisms ✅

### 1. Equipment Dashboard Organism
**Status**: Production Ready ✅  
**File**: `./EquipmentDashboard/`  
**Purpose**: Complete electrical equipment monitoring interface

```typescript
import { EquipmentDashboard } from '@/components/organisms'

<EquipmentDashboard
  equipment={equipment}
  alerts={alerts}
  showSearch
  showFilters
  onEquipmentSelect={handleSelect}
/>
```

**Key Features:**
- Real-time equipment monitoring
- Professional electrical status indicators
- Alert management with severity levels
- Advanced search and filtering
- Motor control centers, transformers, distribution panels

### 2. Control Panels Organism  
**Status**: Production Ready ✅  
**File**: `./ControlPanels/`  
**Purpose**: Professional electrical system control interfaces

```typescript
import { ControlPanels } from '@/components/organisms'

<ControlPanels
  controlGroups={controlGroups}
  equipment={equipment}
  permissionLevel="engineer"
  showInterlocks
  onControlAction={handleAction}
  onEmergencyStop={emergencyStop}
/>
```

**Key Features:**
- Safe equipment operation controls
- Safety interlock systems (IEC 61508)
- Emergency shutdown procedures  
- Role-based access control
- Automated control sequences

## Quality Standards ✅

All organisms meet these **mandatory requirements**:
- ✅ **100% TypeScript Strict Mode** - Complete type safety
- ✅ **WCAG 2.1 AA Compliance** - Full accessibility support  
- ✅ **Professional Electrical Standards** - IEEE/IEC compliance
- ✅ **Atomic Design Integration** - Perfect composition with atoms/molecules
- ✅ **Production-Ready Quality** - Engineering-grade implementation

## Documentation

| Document | Purpose | Audience |
|----------|---------|----------|
| **[ATOMIC_DESIGN_GUIDE.md](./ATOMIC_DESIGN_GUIDE.md)** | Complete organism API and usage documentation | Developers |
| **[IMPLEMENTATION_SUMMARY.md](./IMPLEMENTATION_SUMMARY.md)** | Technical implementation summary | Technical Teams |
| **[EXECUTIVE_SUMMARY.md](./EXECUTIVE_SUMMARY.md)** | Business value and achievements | Leadership |
| **[MIGRATION_GUIDE.md](../MIGRATION_GUIDE.md)** | Legacy component migration strategies | Developers |

## Quick Start

### Installation
```bash
# Organisms are already available in the design system
import { EquipmentDashboard, ControlPanels } from '@/components/organisms'
```

### Basic Usage
```typescript
// Equipment monitoring
function EquipmentMonitoring() {
  return (
    <EquipmentDashboard
      equipment={equipment}
      alerts={alerts}
      showSearch
      onEquipmentSelect={handleSelection}
    />
  )
}

// Equipment controls  
function EquipmentControls() {
  return (
    <ControlPanels
      controlGroups={controlGroups}
      equipment={equipment}
      permissionLevel="engineer"
      onControlAction={handleAction}
    />
  )
}
```

### Advanced Integration
```typescript
// With state management
import { useEquipmentDashboard, useControlPanels } from '@/components/organisms'

function IntegratedInterface() {
  const { equipment, alerts, refetch } = useEquipmentDashboard({
    refreshInterval: 10000,
    enableRealTime: true
  })

  const { controlGroups, executeAction } = useControlPanels({
    operatorId: 'john.engineer',
    enableRealTime: true
  })

  return (
    <div>
      <EquipmentDashboard
        equipment={equipment}
        alerts={alerts}
        onRefresh={refetch}
      />
      <ControlPanels
        controlGroups={controlGroups}
        equipment={equipment}
        onControlAction={executeAction}
      />
    </div>
  )
}
```

## File Structure

```
organisms/
├── README.md                          # This quick reference guide
├── ATOMIC_DESIGN_GUIDE.md            # Complete organism documentation
├── IMPLEMENTATION_SUMMARY.md          # Technical implementation summary  
├── EXECUTIVE_SUMMARY.md              # Business value and achievements
├── index.ts                          # All organism exports
├── EquipmentDashboard/               # Equipment monitoring organism
│   ├── EquipmentDashboard.tsx        # Main organism component
│   ├── EquipmentDashboardTypes.ts    # TypeScript type definitions
│   ├── useEquipmentDashboard.ts      # React Query integration hook
│   ├── index.ts                      # Component exports
│   └── __tests__/                    # Component tests
└── ControlPanels/                    # Equipment control organism
    ├── ControlPanels.tsx             # Main organism component
    ├── ControlPanelsTypes.ts         # TypeScript type definitions
    ├── useControlPanels.ts           # React Query integration hook
    ├── index.ts                      # Component exports
    └── __tests__/                    # Component tests
```

## Electrical Engineering Context

### Equipment Dashboard Use Cases
- **Motor Control Centers**: Real-time monitoring of motor starter panels
- **Power Transformers**: Health monitoring with electrical measurements
- **Distribution Panels**: Load monitoring and fault detection
- **Instrumentation**: Process measurement and control loops

### Control Panels Use Cases
- **Motor Control**: Start/stop sequences with safety interlocks
- **Emergency Procedures**: Emergency shutdown and isolation
- **Lockout/Tagout**: NFPA 70E compliant electrical isolation
- **System Energization**: Safe energization with multi-level confirmation

## Safety Standards Integration

- **IEC 61508**: Functional Safety - Safety Integrity Levels (SIL 1-4)
- **NFPA 70E**: Electrical Safety in the Workplace
- **IEEE 1584**: Guide for Arc Flash Hazard Calculation  
- **IEC 61850**: Communication protocols for electrical substations

## Next Steps

### For Developers
1. Review [ATOMIC_DESIGN_GUIDE.md](./ATOMIC_DESIGN_GUIDE.md) for complete API documentation
2. Check [MIGRATION_GUIDE.md](../MIGRATION_GUIDE.md) for legacy component replacement
3. Examine organism source code for implementation patterns
4. Run tests to verify organism functionality

### For Project Teams
1. Review [IMPLEMENTATION_SUMMARY.md](./IMPLEMENTATION_SUMMARY.md) for technical details
2. Plan organism integration into existing electrical engineering workflows
3. Coordinate with electrical engineers for workflow validation
4. Schedule team training on organism usage patterns

### For Leadership
1. Review [EXECUTIVE_SUMMARY.md](./EXECUTIVE_SUMMARY.md) for business value
2. Approve next phase development (Phase 3B: ProjectNavigation, SystemConfiguration)
3. Plan organism deployment to production environment
4. Consider expansion opportunities for additional electrical engineering capabilities

## Support

- **Technical Issues**: Review organism source code and tests
- **Usage Questions**: Reference [ATOMIC_DESIGN_GUIDE.md](./ATOMIC_DESIGN_GUIDE.md)  
- **Migration Help**: Follow [MIGRATION_GUIDE.md](../MIGRATION_GUIDE.md)
- **Business Questions**: See [EXECUTIVE_SUMMARY.md](./EXECUTIVE_SUMMARY.md)

---

**Status**: Phase 3A Complete ✅  
**Quality**: Engineering-Grade ✅  
**Ready**: Production Deployment ✅