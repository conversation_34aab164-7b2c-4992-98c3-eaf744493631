/**
 * Organism Components - Complex Interface Sections
 * 
 * Atomic design organisms combining atoms and molecules to create
 * complete, complex interface sections with business logic.
 * 
 * Architecture:
 * - Composed of atoms and molecules
 * - Complex business logic integration
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Consistent design system integration
 * - Performance optimized
 * - Engineering-grade quality
 */

// Core Organisms
export {
  AuthForm,
  type AuthFormProps,
  type AuthFormData,
  type AuthFormSize,
  type AuthFormLayout,
  type AuthFormType,
  authFormVariants
} from "./AuthForm"

// Equipment Dashboard Organism
export {
  EquipmentDashboard,
  useEquipmentDashboard,
  type Equipment,
  type EquipmentAlert,
  type EquipmentFilters,
  type DashboardLayout,
  type DashboardConfig,
  type EquipmentDashboardProps,
  type EquipmentCardProps,
  type AlertPanelProps,
  type FilterPanelProps,
  type DashboardControlsProps,
  type EquipmentStatus,
  type ElectricalSystemType,
  type EquipmentPriority,
  type VoltageClass,
  type ElectricalMeasurement,
  type EquipmentLocation,
  type MaintenanceInfo,
  EQUIPMENT_STATUS_COLORS,
  SYSTEM_TYPE_LABELS,
  VOLTAGE_CLASS_LABELS,
  DEFAULT_DASHBOARD_LAYOUT,
  DEFAULT_DASHBOARD_CONFIG,
} from "./EquipmentDashboard"

// Control Panels Organism
export {
  ControlPanels,
  useControlPanels,
  type ControlGroup,
  type ControlEquipment,
  type ControlSession,
  type ControlAlert,
  type ControlLogEntry,
  type SafetyInterlock,
  type ControlSequenceStep,
  type ControlSystemType,
  type ControlAction,
  type ControlState,
  type SafetyIntegrityLevel,
  type ControlPermissionLevel,
  type ControlPanelsLayout,
  type ControlPanelsConfig,
  type ControlFilters,
  type ControlPanelsProps,
  type ControlGroupCardProps,
  type EquipmentControlProps,
  type SafetyInterlockPanelProps,
  type ControlEquipmentSummary,
  type ControlGroupSummary,
  CONTROL_STATE_COLORS,
  CONTROL_SYSTEM_LABELS,
  SAFETY_LEVEL_LABELS,
  PERMISSION_LEVEL_LABELS,
  DEFAULT_CONTROL_LAYOUT,
  DEFAULT_CONTROL_CONFIG,
  isActionPermitted,
  hasActiveInterlocks,
  canBypassInterlock,
} from "./ControlPanels"

// Project Navigation Organism
export {
  ProjectNavigation,
  useProjectNavigation,
  type ProjectNavigationProps,
  type ProjectInfo,
  type NavigationItem,
  type TeamMember,
  type ProjectPhase,
  type ProjectStatus,
  type ProjectPriority,
  type ElectricalStandard,
  type ProjectRole,
  type ProjectProgress,
  type NavigationConfig,
  type NavigationBreadcrumbProps,
  type ProjectHeaderProps,
  type PhaseNavigatorProps,
  type TeamPanelProps,
  type NavigationItemWithProgress,
  type ProjectSummary,
  type PhaseStatus,
  PROJECT_STATUS_COLORS,
  PROJECT_PHASE_LABELS,
  ELECTRICAL_STANDARD_LABELS,
  DEFAULT_NAVIGATION_CONFIG,
  PROJECT_PHASES_SEQUENCE,
  type UseProjectNavigationOptions,
  type UseProjectNavigationReturn,
} from "./ProjectNavigation"

// System Configuration Organism
export {
  SystemConfiguration,
  useSystemConfiguration,
  type SystemConfigurationProps,
  type ConfigurationSection,
  type ConfigurationField,
  type ConfigurationPreset,
  type ConfigurationHistory,
  type ConfigurationCategory,
  type ConfigurationLevel,
  type ConfigurationValueType,
  type ValidationRule,
  type ElectricalStandardConfig,
  type ConfigurationStatus,
  type ConfigurationOption,
  type ConfigurationValidation,
  type ConfigurationValidationResult,
  type ConfigurationFilter,
  type ConfigurationExport,
  type ConfigurationFieldProps,
  type ConfigurationSectionProps,
  type ConfigurationCategoryNavProps,
  type ConfigurationToolbarProps,
  type ConfigurationSummary,
  type ConfigurationFieldValue,
  CONFIGURATION_STATUS_COLORS,
  CONFIGURATION_CATEGORY_LABELS,
  ELECTRICAL_STANDARD_LABELS as CONFIG_ELECTRICAL_STANDARD_LABELS,
  CONFIGURATION_LEVEL_LABELS,
  DEFAULT_CONFIGURATION_FILTER,
  CONFIGURATION_CATEGORIES_SEQUENCE,
  BUILT_IN_PRESETS,
  type UseSystemConfigurationOptions,
  type UseSystemConfigurationReturn,
} from "./SystemConfiguration"