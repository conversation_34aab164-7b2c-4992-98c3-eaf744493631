# Atomic Design System - Organisms Guide

## Overview

Organisms represent complex, complete interface sections that combine atoms and molecules to create sophisticated business logic components. These are the largest components in our atomic design system, designed specifically for electrical engineering workflows with professional-grade quality and accessibility.

## Architecture Principles

### Design Philosophy
- **Composition-Based**: Built by combining atoms and molecules
- **Domain-Specific**: Tailored for electrical engineering workflows  
- **Accessibility-First**: WCAG 2.1 AA compliance throughout
- **Type-Safe**: 100% TypeScript strict mode compliance
- **Performance-Optimized**: Efficient rendering and state management
- **Engineering-Grade**: Professional electrical industry standards

### Quality Standards
- ✅ **100% TypeScript Strict Mode** - Complete type safety
- ✅ **WCAG 2.1 AA Compliance** - Full accessibility support
- ✅ **Professional Electrical Standards** - IEEE/IEC compliance
- ✅ **Atomic Design Integration** - Seamless composition patterns
- ✅ **Production-Ready Quality** - Engineering-grade implementation

## Organisms Catalog

### 1. Equipment Dashboard Organism

#### Purpose
Complete electrical equipment monitoring interface providing real-time status, health indicators, and alert management for professional electrical systems.

#### Composition
**Primary Components:**
- **StatusCard** (Molecule) - Equipment status display with metrics
- **HealthIndicator** (Molecule) - Equipment health monitoring with thresholds
- **AlertCard** (Molecule) - Critical system alert notifications  
- **StatusIndicator** (Atom) - Visual status representation
- **SearchBox** (Molecule) - Advanced equipment search functionality

#### Props Interface

```typescript
interface EquipmentDashboardProps {
  // Core Equipment Data
  equipment: Equipment[]
  alerts: EquipmentAlert[]
  
  // Layout Configuration
  layout?: DashboardLayout
  config?: DashboardConfig
  
  // State Management
  filters?: EquipmentFilters
  selectedEquipment?: string[]
  
  // Event Handlers
  onEquipmentSelect?: (equipment: Equipment) => void
  onAlertDismiss?: (alert: EquipmentAlert) => void
  onFilterChange?: (filters: EquipmentFilters) => void
  onRefresh?: () => void
  
  // Display Options
  showSearch?: boolean
  showFilters?: boolean
  showAlerts?: boolean
  compactMode?: boolean
  
  // Accessibility
  'aria-label'?: string
  id?: string
}

// Supporting Types
type Equipment = {
  id: string
  name: string
  type: ElectricalSystemType
  status: EquipmentStatus
  location: EquipmentLocation
  voltage: VoltageClass
  measurements: ElectricalMeasurement[]
  maintenance: MaintenanceInfo
  priority: EquipmentPriority
}

type EquipmentAlert = {
  id: string
  equipmentId: string
  title: string
  description: string
  severity: 'critical' | 'warning' | 'info'
  timestamp: Date
  acknowledged: boolean
}
```

#### Usage Examples

##### Motor Control Center Monitoring
```typescript
import { EquipmentDashboard } from '@/components/organisms'

function MotorControlCenter() {
  const [equipment, setEquipment] = useState<Equipment[]>([
    {
      id: 'MCC-001',
      name: 'Main Motor Control Center',
      type: 'motor_control_center',
      status: 'operational',
      location: { building: 'Plant A', floor: '1', room: 'Electrical Room' },
      voltage: 'low_voltage',
      measurements: [
        { name: 'Load Current', value: 245, unit: 'A', status: 'normal' },
        { name: 'Voltage', value: 480, unit: 'V', status: 'normal' },
        { name: 'Temperature', value: 35, unit: '°C', status: 'normal' }
      ],
      maintenance: {
        lastService: new Date('2024-01-15'),
        nextService: new Date('2024-07-15'),
        condition: 'good'
      },
      priority: 'high'
    }
  ])

  return (
    <EquipmentDashboard
      equipment={equipment}
      alerts={activeAlerts}
      layout="grid"
      showSearch
      showFilters
      onEquipmentSelect={(equipment) => handleEquipmentDetails(equipment)}
      onRefresh={refreshEquipmentData}
      aria-label="Motor Control Center Dashboard"
    />
  )
}
```

##### Power Transformer Health Tracking
```typescript
function TransformerHealthDashboard() {
  const transformerConfig: DashboardConfig = {
    refreshInterval: 5000, // 5 seconds
    alertThreshold: 'warning',
    autoRefresh: true,
    showMetrics: true,
    compactView: false
  }

  return (
    <EquipmentDashboard
      equipment={transformers}
      alerts={transformerAlerts}
      config={transformerConfig}
      filters={{
        type: ['power_transformer'],
        status: ['operational', 'warning'],
        voltage: ['high_voltage']
      }}
      onAlertDismiss={acknowledgeAlert}
      compactMode={false}
      aria-label="Power Transformer Health Monitoring"
    />
  )
}
```

#### State Management Integration

**React Query Integration:**
```typescript
import { useEquipmentDashboard } from '@/components/organisms/EquipmentDashboard'

function IntegratedEquipmentDashboard() {
  const {
    equipment,
    alerts,
    isLoading,
    error,
    refetch,
    updateEquipment,
    acknowledgeAlert
  } = useEquipmentDashboard({
    refreshInterval: 10000,
    enableRealTime: true
  })

  return (
    <EquipmentDashboard
      equipment={equipment}
      alerts={alerts}
      onRefresh={refetch}
      onAlertDismiss={acknowledgeAlert}
    />
  )
}
```

**Zustand Store Integration:**
```typescript
const useEquipmentStore = create<EquipmentState>((set, get) => ({
  selectedEquipment: [],
  filters: DEFAULT_FILTERS,
  layout: 'grid',
  
  selectEquipment: (equipment) => set((state) => ({
    selectedEquipment: [...state.selectedEquipment, equipment.id]
  })),
  
  updateFilters: (filters) => set({ filters }),
  setLayout: (layout) => set({ layout })
}))
```

#### Electrical Engineering Context

**IEEE/IEC Standards Compliance:**
- **IEC 61850** - Communication protocols for electrical substations
- **IEEE C57.104** - Power transformer monitoring guidelines
- **IEC 60034** - Rotating electrical machines standards
- **IEEE 3007** - Recommended practice for electrical safety

**Professional Workflows:**
- Real-time equipment monitoring with configurable thresholds
- Alert management with severity-based prioritization  
- Equipment health trending and predictive maintenance
- Comprehensive electrical measurement tracking
- Role-based access control for system operations

---

### 2. Control Panels Organism

#### Purpose
Professional electrical system control interfaces providing safe equipment operation, emergency controls, and automated sequence management with comprehensive safety interlocks.

#### Composition
**Primary Components:**
- **ButtonGroup** (Molecule) - System control button groupings
- **StatusIndicator** (Atom) - Real-time status visualization
- **AlertCard** (Molecule) - Safety alerts and system notifications
- **InputField** (Molecule) - Control parameter input
- **Avatar** (Atom) - Operator identification and role display

#### Props Interface

```typescript
interface ControlPanelsProps {
  // Core Control Data
  controlGroups: ControlGroup[]
  equipment: ControlEquipment[]
  activeSession?: ControlSession
  
  // Safety Systems
  interlocks: SafetyInterlock[]
  alerts: ControlAlert[]
  
  // Layout & Configuration
  layout?: ControlPanelsLayout
  config?: ControlPanelsConfig
  
  // Filtering & Selection
  filters?: ControlFilters
  selectedGroups?: string[]
  
  // Event Handlers
  onControlAction?: (action: ControlAction, equipment: ControlEquipment) => void
  onSequenceStart?: (sequence: ControlSequenceStep[]) => void
  onEmergencyStop?: () => void
  onInterlockBypass?: (interlock: SafetyInterlock) => void
  
  // Session Management
  onSessionStart?: (operator: string) => void
  onSessionEnd?: () => void
  
  // Display Options
  showInterlocks?: boolean
  showSequences?: boolean
  showHistory?: boolean
  enableEmergencyControls?: boolean
  
  // Security
  permissionLevel: ControlPermissionLevel
  operatorId?: string
  
  // Accessibility
  'aria-label'?: string
  id?: string
}

// Supporting Types
type ControlGroup = {
  id: string
  name: string
  type: ControlSystemType
  equipment: ControlEquipment[]
  sequences: ControlSequenceStep[]
  interlocks: SafetyInterlock[]
  permissionsRequired: ControlPermissionLevel[]
}

type ControlEquipment = {
  id: string
  name: string
  type: string
  state: ControlState
  availableActions: ControlAction[]
  interlocks: SafetyInterlock[]
  lastOperation: {
    action: ControlAction
    operator: string
    timestamp: Date
  }
}

type SafetyInterlock = {
  id: string
  name: string
  type: 'hardware' | 'software' | 'administrative'
  active: boolean
  bypassable: boolean
  silLevel: SafetyIntegrityLevel
  description: string
}
```

#### Usage Examples

##### Motor Start/Stop Control Sequences
```typescript
import { ControlPanels } from '@/components/organisms'

function MotorControlInterface() {
  const motorControlGroups: ControlGroup[] = [
    {
      id: 'motor-group-1',
      name: 'Process Pump Motors',
      type: 'motor_control',
      equipment: [
        {
          id: 'pump-001',
          name: 'Feed Pump P-001',
          type: 'centrifugal_pump',
          state: 'stopped',
          availableActions: ['start', 'jog'],
          interlocks: [
            {
              id: 'pump-001-pressure',
              name: 'Discharge Pressure Low',
              type: 'software',
              active: true,
              bypassable: false,
              silLevel: 'sil2',
              description: 'Discharge pressure below minimum threshold'
            }
          ],
          lastOperation: {
            action: 'stop',
            operator: 'john.engineer',
            timestamp: new Date('2024-01-15T10:30:00')
          }
        }
      ],
      sequences: [
        {
          id: 'startup-seq',
          name: 'Pump Startup Sequence',
          steps: [
            { action: 'check_interlocks', duration: 2000 },
            { action: 'open_discharge_valve', duration: 5000 },
            { action: 'start_motor', duration: 1000 },
            { action: 'ramp_to_setpoint', duration: 10000 }
          ]
        }
      ],
      interlocks: [],
      permissionsRequired: ['operator', 'engineer']
    }
  ]

  return (
    <ControlPanels
      controlGroups={motorControlGroups}
      equipment={motorEquipment}
      permissionLevel="engineer"
      operatorId="john.engineer"
      showInterlocks
      showSequences
      enableEmergencyControls
      onControlAction={handleControlAction}
      onSequenceStart={executeSequence}
      onEmergencyStop={emergencyShutdown}
      aria-label="Motor Control Interface"
    />
  )
}
```

##### Emergency Shutdown Procedures
```typescript
function EmergencyControlPanel() {
  const emergencyConfig: ControlPanelsConfig = {
    emergencyStopTimeout: 5000,
    confirmDestructiveActions: true,
    logAllActions: true,
    requireTwoPersonOperation: true,
    interlockBypassTimeout: 300000 // 5 minutes
  }

  const handleEmergencyStop = async () => {
    // Implement emergency shutdown sequence
    await executeEmergencySequence([
      'stop_all_motors',
      'close_emergency_valves', 
      'activate_fire_suppression',
      'notify_emergency_contacts'
    ])
  }

  return (
    <ControlPanels
      controlGroups={emergencyControlGroups}
      equipment={criticalEquipment}
      config={emergencyConfig}
      permissionLevel="emergency_operator"
      operatorId={currentOperator}
      showInterlocks
      enableEmergencyControls
      onEmergencyStop={handleEmergencyStop}
      onInterlockBypass={handleInterlockBypass}
      aria-label="Emergency Control Panel"
    />
  )
}
```

##### Electrical Isolation and Lockout/Tagout
```typescript
function IsolationControlPanel() {
  const isolationSequence: ControlSequenceStep[] = [
    {
      id: 'verify-zero-energy',
      name: 'Verify Zero Energy State',
      action: 'verify_isolation',
      duration: 30000,
      confirmationRequired: true,
      twoPersonRequired: true
    },
    {
      id: 'apply-locks',
      name: 'Apply Physical Locks',
      action: 'apply_lockout',
      duration: 60000,
      confirmationRequired: true
    },
    {
      id: 'verify-lockout',
      name: 'Verify Lockout Effectiveness',
      action: 'test_lockout',
      duration: 15000,
      confirmationRequired: true
    }
  ]

  return (
    <ControlPanels
      controlGroups={isolationGroups}
      equipment={isolationEquipment}
      permissionLevel="authorized_person"
      operatorId={qualifiedWorker}
      showInterlocks
      showSequences
      onSequenceStart={(sequence) => executeLockoutSequence(sequence)}
      onControlAction={logIsolationAction}
      aria-label="Electrical Isolation Control Panel"
    />
  )
}
```

#### State Management Integration

**React Query Integration:**
```typescript
import { useControlPanels } from '@/components/organisms/ControlPanels'

function IntegratedControlPanels() {
  const {
    controlGroups,
    equipment,
    activeSession,
    interlocks,
    executeAction,
    startSequence,
    emergencyStop
  } = useControlPanels({
    operatorId: 'john.engineer',
    enableRealTime: true,
    sessionTimeout: 1800000 // 30 minutes
  })

  return (
    <ControlPanels
      controlGroups={controlGroups}
      equipment={equipment}
      activeSession={activeSession}
      interlocks={interlocks}
      onControlAction={executeAction}
      onSequenceStart={startSequence}
      onEmergencyStop={emergencyStop}
    />
  )
}
```

#### Safety Standards Integration

**IEC 61508 (Functional Safety):**
- Safety Integrity Level (SIL) compliance for critical interlocks
- Systematic failure analysis and management
- Hardware fault tolerance implementation
- Safety lifecycle management

**IEEE 1584 (Arc Flash Analysis):**
- Arc flash hazard categorization
- Personal protective equipment (PPE) requirements
- Safe working distances calculation
- Incident energy analysis

**NFPA 70E (Electrical Safety):**
- Energized work permit management
- Lockout/tagout procedure enforcement
- Qualified person verification
- Risk assessment and hazard categorization

#### Professional Electrical Workflows

**Control Sequence Management:**
- Pre-defined operational sequences with safety checks
- Step-by-step execution with operator confirmation
- Automatic rollback on interlock activation
- Comprehensive action logging and audit trails

**Safety Interlock Systems:**
- Hardware and software interlock monitoring
- Bypass control with time limits and authorization
- Cascading interlock logic implementation
- Real-time interlock status visualization

**Emergency Response Procedures:**
- One-button emergency stop with confirmation
- Coordinated shutdown sequences across systems
- Automatic notification systems for critical events
- Recovery procedure guidance and checklists

## Migration from Legacy Components

### Equipment Dashboard Migration

**Legacy Component Replacement:**
```typescript
// BEFORE: Legacy Dashboard Components
import { 
  LegacyEquipmentGrid,
  LegacyAlertPanel,
  LegacyFilterSidebar 
} from '@/components/legacy'

function LegacyEquipmentView() {
  return (
    <div>
      <LegacyFilterSidebar filters={filters} />
      <LegacyEquipmentGrid equipment={equipment} />
      <LegacyAlertPanel alerts={alerts} />
    </div>
  )
}

// AFTER: Equipment Dashboard Organism
import { EquipmentDashboard } from '@/components/organisms'

function ModernEquipmentView() {
  return (
    <EquipmentDashboard
      equipment={equipment}
      alerts={alerts}
      filters={filters}
      showSearch
      showFilters
      onEquipmentSelect={handleSelect}
      onAlertDismiss={handleDismiss}
    />
  )
}
```

### Control Panels Migration

**Legacy Control Interface Replacement:**
```typescript
// BEFORE: Multiple Legacy Control Components
import {
  LegacyControlButtons,
  LegacyStatusDisplay,
  LegacyInterlockPanel
} from '@/components/legacy'

function LegacyControlInterface() {
  return (
    <div>
      <LegacyStatusDisplay equipment={equipment} />
      <LegacyControlButtons actions={actions} />
      <LegacyInterlockPanel interlocks={interlocks} />
    </div>
  )
}

// AFTER: Control Panels Organism
import { ControlPanels } from '@/components/organisms'

function ModernControlInterface() {
  return (
    <ControlPanels
      controlGroups={controlGroups}
      equipment={equipment}
      interlocks={interlocks}
      permissionLevel="engineer"
      showInterlocks
      onControlAction={handleAction}
      onEmergencyStop={handleEmergencyStop}
    />
  )
}
```

## Implementation Timeline

### Phase 3A: Completed Organisms ✅
- **EquipmentDashboard** - Complete electrical equipment monitoring
- **ControlPanels** - Professional system control interfaces

### Phase 3B: Remaining Organisms (Future)
- **ProjectNavigation** - Electrical project management interface
- **SystemConfiguration** - System settings and validation

## Quality Assurance

### Testing Requirements
- **Unit Tests**: 100% coverage for organism logic
- **Integration Tests**: Complete workflow testing
- **Accessibility Tests**: WCAG 2.1 AA compliance verification
- **E2E Tests**: End-to-end user workflow validation

### Performance Standards
- **Render Performance**: <16ms for 60fps
- **Memory Usage**: <50MB for complex dashboards
- **Bundle Size**: <200KB per organism
- **Network Efficiency**: Optimized data fetching

### Code Quality
- **TypeScript Strict**: 100% compliance
- **ESLint Clean**: Zero warnings or errors
- **Accessibility**: Full screen reader support
- **Mobile Responsive**: All breakpoints covered

## API Documentation

### Equipment Dashboard API
```typescript
// Hook API
const {
  equipment,        // Equipment[] - Current equipment data
  alerts,          // EquipmentAlert[] - Active alerts
  isLoading,       // boolean - Data loading state
  error,           // Error | null - Error state
  refetch,         // () => void - Refetch data
  updateEquipment, // (equipment: Equipment) => void - Update single equipment
  acknowledgeAlert // (alertId: string) => void - Acknowledge alert
} = useEquipmentDashboard(options)

// Component Props API
interface EquipmentDashboardProps {
  equipment: Equipment[]                    // Required equipment data
  alerts?: EquipmentAlert[]                // Optional alerts
  layout?: 'grid' | 'list' | 'compact'    // Layout options
  showSearch?: boolean                      // Enable search
  showFilters?: boolean                     // Enable filters
  onEquipmentSelect?: (equipment: Equipment) => void // Selection handler
  onRefresh?: () => void                   // Refresh handler
}
```

### Control Panels API
```typescript
// Hook API
const {
  controlGroups,     // ControlGroup[] - Control group data
  equipment,         // ControlEquipment[] - Equipment data
  activeSession,     // ControlSession | null - Current session
  interlocks,        // SafetyInterlock[] - Safety interlocks
  executeAction,     // (action: ControlAction, equipment: ControlEquipment) => Promise<void>
  startSequence,     // (sequence: ControlSequenceStep[]) => Promise<void>
  emergencyStop      // () => Promise<void> - Emergency shutdown
} = useControlPanels(options)

// Component Props API
interface ControlPanelsProps {
  controlGroups: ControlGroup[]            // Required control groups
  equipment: ControlEquipment[]            // Required equipment
  permissionLevel: ControlPermissionLevel  // Required permission level
  operatorId?: string                      // Optional operator ID
  showInterlocks?: boolean                 // Show safety interlocks
  showSequences?: boolean                  // Show control sequences
  onControlAction?: (action: ControlAction) => void // Action handler
  onEmergencyStop?: () => void            // Emergency stop handler
}
```

## Best Practices

### State Management
- Use React Query for server state management
- Use Zustand for client state (UI state, selections)
- Implement optimistic updates for control actions
- Cache equipment data with appropriate TTL

### Performance Optimization
- Implement virtualization for large equipment lists
- Use React.memo for expensive organism re-renders
- Debounce search and filter operations
- Lazy load non-critical organism features

### Accessibility
- Provide comprehensive ARIA labels and descriptions
- Implement keyboard navigation for all controls
- Use semantic HTML elements throughout
- Support screen reader announcements for state changes

### Error Handling
- Implement graceful degradation for network failures
- Provide clear error messages for user actions
- Include retry mechanisms for failed operations
- Log errors for debugging and monitoring

## Future Enhancements

### Planned Features
- **Real-time Collaboration** - Multi-operator session management
- **Mobile Optimization** - Touch-friendly control interfaces  
- **Advanced Analytics** - Equipment performance trending
- **AI Integration** - Predictive maintenance recommendations

### Extensibility
- Plugin architecture for custom organism features
- Theme customization for different electrical contexts
- Internationalization support for global deployments
- Integration APIs for third-party electrical systems

The organism components provide the foundation for sophisticated electrical engineering interfaces while maintaining the highest standards of safety, accessibility, and professional quality.