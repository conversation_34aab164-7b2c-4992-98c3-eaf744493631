/**
 * System Configuration Organism Tests
 * 
 * Comprehensive test suite for the SystemConfiguration organism component,
 * ensuring professional electrical engineering configuration functionality.
 * 
 * Test Coverage:
 * - Component rendering and props handling
 * - Configuration field interactions
 * - Section management functionality
 * - Category navigation
 * - Validation and error handling
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Error boundary behavior
 * - Loading and error states
 * - TypeScript strict mode compliance
 */

import React from "react"
import { render, screen, fireEvent, waitFor, within } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest"

import { SystemConfiguration } from "../SystemConfiguration"
import type { 
  SystemConfigurationProps, 
  ConfigurationSection,
  ConfigurationField,
  ConfigurationPreset,
  ConfigurationCategory
} from "../SystemConfigurationTypes"

// Mock the configuration hook
const mockConfigurationHook = {
  sections: [],
  presets: [],
  history: [],
  validationResult: null,
  loading: false,
  error: null,
  isValidating: false,
  isSaving: false,
  hasUnsavedChanges: false,
  activeCategory: "general" as ConfigurationCategory,
  filters: {
    categories: [],
    levels: [],
    status: [],
    searchQuery: "",
    modifiedOnly: false,
    errorOnly: false,
    requiredOnly: false,
    showHidden: false,
  },
  selectedFields: [],
  expandedSections: new Set<string>(),
  searchQuery: "",
  showAdvanced: false,
  filteredSections: [],
  categorySummary: [],
  validationSummary: {
    totalFields: 0,
    validFields: 0,
    errorFields: 0,
    warningFields: 0,
    requiredMissing: 0,
  },
  canSave: false,
  canReset: false,
  updateField: vi.fn(),
  resetField: vi.fn(),
  validateField: vi.fn(),
  toggleSection: vi.fn(),
  resetSection: vi.fn(),
  expandAllSections: vi.fn(),
  collapseAllSections: vi.fn(),
  setActiveCategory: vi.fn(),
  setCategoryFilter: vi.fn(),
  applyPreset: vi.fn(),
  createPreset: vi.fn(),
  deletePreset: vi.fn(),
  saveConfiguration: vi.fn(),
  resetConfiguration: vi.fn(),
  validateConfiguration: vi.fn(),
  exportConfiguration: vi.fn(),
  importConfiguration: vi.fn(),
  setSearchQuery: vi.fn(),
  setFilters: vi.fn(),
  clearFilters: vi.fn(),
  selectField: vi.fn(),
  deselectField: vi.fn(),
  selectAllFields: vi.fn(),
  clearSelection: vi.fn(),
  revertToHistoryEntry: vi.fn(),
  clearHistory: vi.fn(),
  toggleAdvancedMode: vi.fn(),
  refreshConfiguration: vi.fn(),
  getFieldDependencies: vi.fn(),
}

vi.mock("../useSystemConfiguration", () => ({
  useSystemConfiguration: () => mockConfigurationHook,
}))

vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}))

// Test data
const mockConfigurationFields: ConfigurationField[] = [
  {
    id: "voltage_tolerance",
    name: "voltage_tolerance",
    label: "Voltage Tolerance",
    description: "Acceptable voltage variation percentage",
    type: "number",
    category: "electrical_standards",
    level: "system",
    defaultValue: 5,
    currentValue: 7,
    unit: "%",
    min: 1,
    max: 10,
    isRequired: true,
    status: "modified",
    validations: [],
  },
  {
    id: "safety_shutdown",
    name: "safety_shutdown",
    label: "Enable Safety Shutdown",
    description: "Automatic safety shutdown on critical faults",
    type: "boolean",
    category: "safety_protocols",
    level: "system",
    defaultValue: true,
    currentValue: true,
    isRequired: true,
    status: "default",
    validations: [],
  },
  {
    id: "compliance_standard",
    name: "compliance_standard",
    label: "Primary Compliance Standard",
    description: "Primary electrical standard for compliance checking",
    type: "select",
    category: "compliance",
    level: "project",
    defaultValue: "IEC-60364",
    currentValue: "EN-50110",
    options: [
      { value: "IEC-60364", label: "IEC 60364 (Low-Voltage Installations)" },
      { value: "EN-50110", label: "EN 50110 (Operation)" },
      { value: "IEEE-80", label: "IEEE 80 (Ground Systems)" },
    ],
    isRequired: true,
    status: "modified",
    validations: [],
  },
]

const mockConfigurationSections: ConfigurationSection[] = [
  {
    id: "electrical-standards",
    title: "Electrical Standards",
    description: "Core electrical engineering standards and parameters",
    icon: "Zap" as any,
    category: "electrical_standards",
    fields: [mockConfigurationFields[0]],
    isCollapsible: true,
    isExpanded: true,
    canReset: true,
    hasChanges: true,
    hasErrors: false,
    hasWarnings: false,
    requiredFields: 1,
    completedFields: 1,
  },
  {
    id: "safety-protocols",
    title: "Safety Protocols",
    description: "Safety system configuration and protocols",
    icon: "Shield" as any,
    category: "safety_protocols", 
    fields: [mockConfigurationFields[1]],
    isCollapsible: true,
    isExpanded: false,
    canReset: false,
    hasChanges: false,
    hasErrors: false,
    hasWarnings: false,
    requiredFields: 1,
    completedFields: 1,
  },
  {
    id: "compliance-config",
    title: "Compliance Configuration",
    description: "Standards compliance and validation settings",
    icon: "CheckCircle" as any,
    category: "compliance",
    fields: [mockConfigurationFields[2]],
    isCollapsible: true,
    isExpanded: true,
    canReset: true,
    hasChanges: true,
    hasErrors: false,
    hasWarnings: false,
    requiredFields: 1,
    completedFields: 1,
  },
]

const mockPresets: ConfigurationPreset[] = [
  {
    id: "industrial-basic",
    name: "Industrial Basic",
    description: "Basic configuration for industrial electrical systems",
    category: "electrical_standards",
    isBuiltIn: true,
    isCustom: false,
    values: {
      voltage_tolerance: 5,
      safety_shutdown: true,
      compliance_standard: "IEC-60364",
    },
    applicableStandards: ["IEC-60364", "EN-50110"],
    createdDate: new Date("2024-01-01"),
  },
  {
    id: "safety-critical",
    name: "Safety Critical",
    description: "High-safety functional safety systems configuration",
    category: "safety_protocols",
    isBuiltIn: true,
    isCustom: false,
    values: {
      voltage_tolerance: 3,
      safety_shutdown: true,
      compliance_standard: "IEC-61508",
    },
    applicableStandards: ["IEC-61508", "IEC-60364"],
    createdDate: new Date("2024-01-01"),
  },
]

const defaultProps: Partial<SystemConfigurationProps> = {
  "data-testid": "system-configuration",
}

const renderSystemConfiguration = (props: Partial<SystemConfigurationProps> = {}) => {
  const mergedProps = { ...defaultProps, ...props } as SystemConfigurationProps
  return render(<SystemConfiguration {...mergedProps} />)
}

describe("SystemConfiguration", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock hook data
    Object.assign(mockConfigurationHook, {
      sections: mockConfigurationSections,
      presets: mockPresets,
      filteredSections: mockConfigurationSections,
      categorySummary: [
        {
          category: "electrical_standards" as ConfigurationCategory,
          label: "Electrical Standards",
          count: 1,
          hasChanges: true,
          hasErrors: false,
        },
        {
          category: "safety_protocols" as ConfigurationCategory,
          label: "Safety Protocols", 
          count: 1,
          hasChanges: false,
          hasErrors: false,
        },
        {
          category: "compliance" as ConfigurationCategory,
          label: "Compliance Management",
          count: 1,
          hasChanges: true,
          hasErrors: false,
        },
      ],
      expandedSections: new Set(["electrical-standards", "compliance-config"]),
      hasUnsavedChanges: true,
      canSave: true,
      canReset: true,
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe("Rendering", () => {
    it("renders system configuration with external sections data", () => {
      renderSystemConfiguration({ sections: mockConfigurationSections })

      expect(screen.getByTestId("system-configuration")).toBeInTheDocument()
      expect(screen.getByText("System Configuration")).toBeInTheDocument()
      expect(screen.getByText("Electrical Standards")).toBeInTheDocument()
      expect(screen.getByText("Safety Protocols")).toBeInTheDocument()
      expect(screen.getByText("Compliance Configuration")).toBeInTheDocument()
    })

    it("renders with hook data when no external sections provided", () => {
      renderSystemConfiguration()

      expect(screen.getByTestId("system-configuration")).toBeInTheDocument()
      expect(screen.getByText("System Configuration")).toBeInTheDocument()
    })

    it("renders loading state correctly", () => {
      Object.assign(mockConfigurationHook, { 
        loading: true, 
        filteredSections: [] 
      })

      renderSystemConfiguration()

      expect(screen.getByTestId("system-configuration")).toBeInTheDocument()
      // Loading skeleton should be present
      const loadingSkeleton = screen.container.querySelector(".animate-pulse")
      expect(loadingSkeleton).toBeInTheDocument()
    })

    it("renders error state correctly", () => {
      Object.assign(mockConfigurationHook, { 
        loading: false, 
        error: "Failed to load configuration",
        filteredSections: [] 
      })

      renderSystemConfiguration()

      expect(screen.getByText("Configuration Error")).toBeInTheDocument()
      expect(screen.getByText("Failed to load configuration")).toBeInTheDocument()
      expect(screen.getByText("Try Again")).toBeInTheDocument()
    })

    it("renders empty state when no sections available", () => {
      Object.assign(mockConfigurationHook, { 
        loading: false, 
        error: null,
        filteredSections: [] 
      })

      renderSystemConfiguration()

      expect(screen.getByText("No Configuration Sections")).toBeInTheDocument()
      expect(screen.getByText("No configuration sections match your current filters.")).toBeInTheDocument()
    })
  })

  describe("Toolbar", () => {
    it("displays configuration toolbar with correct state", () => {
      renderSystemConfiguration()

      expect(screen.getByText("System Configuration")).toBeInTheDocument()
      expect(screen.getByText("Save Changes")).toBeInTheDocument()
      expect(screen.getByText("Validate")).toBeInTheDocument()
      expect(screen.getByText("Export")).toBeInTheDocument()
      expect(screen.getByText("Import")).toBeInTheDocument()
      expect(screen.getByText("Reset")).toBeInTheDocument()
    })

    it("shows unsaved changes indicator", () => {
      renderSystemConfiguration()

      const unsavedIndicator = screen.container.querySelector('[data-testid*="status-indicator"]')
      expect(unsavedIndicator).toBeInTheDocument()
    })

    it("enables save button when changes exist", () => {
      renderSystemConfiguration()

      const saveButton = screen.getByRole("button", { name: /save changes/i })
      expect(saveButton).toBeEnabled()
    })

    it("disables save button when no changes", () => {
      Object.assign(mockConfigurationHook, { 
        hasUnsavedChanges: false,
        canSave: false 
      })

      renderSystemConfiguration()

      const saveButton = screen.getByRole("button", { name: /save changes/i })
      expect(saveButton).toBeDisabled()
    })

    it("calls save callback when save button clicked", async () => {
      const onSave = vi.fn()
      renderSystemConfiguration({ onSave })

      const saveButton = screen.getByRole("button", { name: /save changes/i })
      await userEvent.click(saveButton)

      expect(onSave).toHaveBeenCalledOnce()
    })

    it("calls validate callback when validate button clicked", async () => {
      const onValidate = vi.fn()
      renderSystemConfiguration({ onValidate })

      const validateButton = screen.getByRole("button", { name: /validate/i })
      await userEvent.click(validateButton)

      expect(onValidate).toHaveBeenCalledOnce()
    })
  })

  describe("Category Navigation", () => {
    it("renders category navigation correctly", () => {
      renderSystemConfiguration()

      expect(screen.getByText("Categories")).toBeInTheDocument()
      expect(screen.getByText("Electrical Standards")).toBeInTheDocument()
      expect(screen.getByText("Safety Protocols")).toBeInTheDocument()
      expect(screen.getByText("Compliance Management")).toBeInTheDocument()
    })

    it("shows category counts and status indicators", () => {
      renderSystemConfiguration()

      // Check for count badges
      const countBadges = screen.getAllByText("1")
      expect(countBadges.length).toBeGreaterThan(0)
    })

    it("highlights active category", () => {
      Object.assign(mockConfigurationHook, { activeCategory: "electrical_standards" })
      renderSystemConfiguration()

      const activeCategory = screen.getByRole("button", { name: /electrical standards/i })
      expect(activeCategory).toHaveClass("bg-primary")
    })

    it("handles category selection", async () => {
      const onCategoryChange = vi.fn()
      renderSystemConfiguration({ onCategoryChange })

      const safetyCategory = screen.getByRole("button", { name: /safety protocols/i })
      await userEvent.click(safetyCategory)

      expect(onCategoryChange).toHaveBeenCalledWith("safety_protocols")
    })
  })

  describe("Configuration Sections", () => {
    it("renders configuration sections correctly", () => {
      renderSystemConfiguration()

      expect(screen.getByText("Electrical Standards")).toBeInTheDocument()
      expect(screen.getByText("Core electrical engineering standards and parameters")).toBeInTheDocument()
      expect(screen.getByText("Safety Protocols")).toBeInTheDocument()
      expect(screen.getByText("Compliance Configuration")).toBeInTheDocument()
    })

    it("shows section status indicators", () => {
      renderSystemConfiguration()

      expect(screen.getByText("Modified")).toBeInTheDocument()
      expect(screen.getByText("1/1")).toBeInTheDocument() // Progress indicator
    })

    it("handles section expansion/collapse", async () => {
      renderSystemConfiguration()

      const collapsedSection = screen.getByText("Safety Protocols")
      await userEvent.click(collapsedSection)

      expect(mockConfigurationHook.toggleSection).toHaveBeenCalledWith("safety-protocols")
    })

    it("shows section reset button for modified sections", () => {
      renderSystemConfiguration()

      const resetButtons = screen.container.querySelectorAll('button[data-testid*="reset"], button svg')
      expect(resetButtons.length).toBeGreaterThan(0)
    })

    it("handles section reset", async () => {
      renderSystemConfiguration()

      // Find and click a reset button (the icon button)
      const resetButton = screen.container.querySelector('button svg[data-testid*="rotate"]')?.closest('button')
      if (resetButton) {
        await userEvent.click(resetButton)
        expect(mockConfigurationHook.resetSection).toHaveBeenCalled()
      }
    })
  })

  describe("Configuration Fields", () => {
    it("renders different field types correctly", () => {
      renderSystemConfiguration()

      // Number field
      expect(screen.getByLabelText("Voltage Tolerance *")).toBeInTheDocument()
      
      // Boolean field
      expect(screen.getByLabelText("Enable Safety Shutdown *")).toBeInTheDocument()
      
      // Select field  
      expect(screen.getByLabelText("Primary Compliance Standard *")).toBeInTheDocument()
    })

    it("shows field validation states", () => {
      renderSystemConfiguration()

      const modifiedFields = screen.getAllByText("Modified from default")
      expect(modifiedFields.length).toBeGreaterThan(0)
    })

    it("handles field value changes", async () => {
      const onFieldChange = vi.fn()
      renderSystemConfiguration({ onFieldChange })

      const voltageField = screen.getByLabelText("Voltage Tolerance *")
      await userEvent.clear(voltageField)
      await userEvent.type(voltageField, "8")

      expect(onFieldChange).toHaveBeenCalledWith("voltage_tolerance", 8)
    })

    it("handles boolean field changes", async () => {
      const onFieldChange = vi.fn()
      renderSystemConfiguration({ onFieldChange })

      const booleanField = screen.getByLabelText("Enable Safety Shutdown *")
      await userEvent.click(booleanField)

      expect(onFieldChange).toHaveBeenCalledWith("safety_shutdown", expect.any(Boolean))
    })

    it("handles select field changes", async () => {
      const onFieldChange = vi.fn()
      renderSystemConfiguration({ onFieldChange })

      const selectField = screen.getByLabelText("Primary Compliance Standard *")
      await userEvent.selectOptions(selectField, "IEEE-80")

      expect(onFieldChange).toHaveBeenCalledWith("compliance_standard", "IEEE-80")
    })

    it("shows required field indicators", () => {
      renderSystemConfiguration()

      const requiredIndicators = screen.getAllByText("*")
      expect(requiredIndicators.length).toBeGreaterThan(0)
    })

    it("displays field units correctly", () => {
      renderSystemConfiguration()

      expect(screen.getByText("%")).toBeInTheDocument()
    })
  })

  describe("Advanced Mode", () => {
    it("renders advanced mode toggle", () => {
      renderSystemConfiguration()

      expect(screen.getByLabelText("Show advanced options")).toBeInTheDocument()
    })

    it("handles advanced mode toggle", async () => {
      renderSystemConfiguration()

      const advancedToggle = screen.getByLabelText("Show advanced options")
      await userEvent.click(advancedToggle)

      expect(mockConfigurationHook.toggleAdvancedMode).toHaveBeenCalledOnce()
    })
  })

  describe("Search Functionality", () => {
    it("renders search box", () => {
      renderSystemConfiguration()

      expect(screen.getByPlaceholderText("Search configuration...")).toBeInTheDocument()
    })

    it("handles search input", async () => {
      renderSystemConfiguration()

      const searchBox = screen.getByPlaceholderText("Search configuration...")
      await userEvent.type(searchBox, "voltage")

      // Search would be handled by the hook
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA labels and roles", () => {
      renderSystemConfiguration()

      const buttons = screen.getAllByRole("button")
      expect(buttons.length).toBeGreaterThan(0)
      
      buttons.forEach(button => {
        expect(button).toBeVisible()
      })
    })

    it("supports keyboard navigation", async () => {
      renderSystemConfiguration()

      const firstButton = screen.getAllByRole("button")[0]
      firstButton.focus()
      expect(firstButton).toHaveFocus()

      await userEvent.keyboard("{Tab}")
      const focusedElement = document.activeElement
      expect(focusedElement).toBeInstanceOf(HTMLElement)
      expect(focusedElement).not.toBe(firstButton)
    })

    it("has proper form labels and associations", () => {
      renderSystemConfiguration()

      const voltageField = screen.getByLabelText("Voltage Tolerance *")
      expect(voltageField).toHaveAttribute("id")
      
      const label = screen.getByText("Voltage Tolerance")
      expect(label.closest("label")).toHaveAttribute("for", voltageField.id)
    })

    it("provides appropriate error messages", () => {
      // This would test validation error display
      renderSystemConfiguration()
      
      // Validation errors would be shown for invalid fields
    })
  })

  describe("Error Handling", () => {
    it("displays error boundary fallback on component errors", () => {
      // Mock console.error to prevent test noise
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})
      
      // Force an error by passing invalid props that would cause the component to throw
      const ThrowError = () => {
        throw new Error("Test error")
      }
      
      expect(() => render(<ThrowError />)).toThrow()
      
      consoleSpy.mockRestore()
    })

    it("handles missing configuration data gracefully", () => {
      Object.assign(mockConfigurationHook, { sections: [] })
      renderSystemConfiguration()

      expect(screen.getByText("No Configuration Sections")).toBeInTheDocument()
    })

    it("handles network errors appropriately", () => {
      Object.assign(mockConfigurationHook, { error: "Network connection failed" })
      renderSystemConfiguration({ sections: undefined })

      expect(screen.getByText("Configuration Error")).toBeInTheDocument()
      expect(screen.getByText("Network connection failed")).toBeInTheDocument()
    })
  })

  describe("Performance", () => {
    it("renders efficiently with large configuration sets", () => {
      const largeSections = Array.from({ length: 20 }, (_, i) => ({
        ...mockConfigurationSections[0],
        id: `section-${i}`,
        title: `Section ${i}`,
        fields: Array.from({ length: 10 }, (_, j) => ({
          ...mockConfigurationFields[0],
          id: `field-${i}-${j}`,
          name: `field_${i}_${j}`,
          label: `Field ${i}-${j}`,
        })),
      }))

      Object.assign(mockConfigurationHook, { 
        sections: largeSections, 
        filteredSections: largeSections 
      })

      const startTime = performance.now()
      renderSystemConfiguration()
      const endTime = performance.now()

      // Should render in reasonable time (less than 200ms for 20 sections with 200 fields)
      expect(endTime - startTime).toBeLessThan(200)
      
      expect(screen.getByText("Section 0")).toBeInTheDocument()
      expect(screen.getByText("Section 19")).toBeInTheDocument()
    })
  })

  describe("Integration", () => {
    it("integrates with external callbacks correctly", async () => {
      const callbacks = {
        onFieldChange: vi.fn(),
        onSectionToggle: vi.fn(),
        onCategoryChange: vi.fn(),
        onSave: vi.fn(),
        onReset: vi.fn(),
        onValidate: vi.fn(),
      }

      renderSystemConfiguration(callbacks)

      // Test save callback
      await userEvent.click(screen.getByRole("button", { name: /save changes/i }))
      expect(callbacks.onSave).toHaveBeenCalled()

      // Test validate callback
      await userEvent.click(screen.getByRole("button", { name: /validate/i }))
      expect(callbacks.onValidate).toHaveBeenCalled()
    })

    it("handles readonly mode properly", () => {
      renderSystemConfiguration({ readonly: true })

      const voltageField = screen.getByLabelText("Voltage Tolerance *")
      expect(voltageField).toBeDisabled()
    })

    it("integrates with presets correctly", async () => {
      const onApplyPreset = vi.fn()
      renderSystemConfiguration({ onApplyPreset, presets: mockPresets })

      // Preset functionality would be tested if UI was present
    })
  })
})