/**
 * System Configuration Organism Exports
 * 
 * Central export point for all System Configuration organism components, types, and utilities.
 * Provides clean imports for other parts of the application.
 */

// Main component exports
export { SystemConfiguration } from "./SystemConfiguration"
export { useSystemConfiguration } from "./useSystemConfiguration"

// Type exports
export type {
  SystemConfigurationProps,
  ConfigurationSection,
  ConfigurationField,
  ConfigurationPreset,
  ConfigurationHistory,
  ConfigurationCategory,
  ConfigurationLevel,
  ConfigurationValueType,
  ValidationRule,
  ElectricalStandardConfig,
  ConfigurationStatus,
  ConfigurationOption,
  ConfigurationValidation,
  ConfigurationValidationResult,
  ConfigurationFilter,
  ConfigurationExport,
  ConfigurationFieldProps,
  ConfigurationSectionProps,
  ConfigurationCategoryNavProps,
  ConfigurationToolbarProps,
  ConfigurationSummary,
  ConfigurationFieldValue,
} from "./SystemConfigurationTypes"

// Utility exports
export {
  CONFIGURATION_STATUS_COLORS,
  CONFIGURATION_CATEGORY_LABELS,
  ELECTRICAL_STANDARD_LABELS,
  CONFIGURATION_LEVEL_LABELS,
  DEFAULT_CONFIGURATION_FILTER,
  CONFIGURATION_CATEGORIES_SEQUENCE,
  BUILT_IN_PRESETS,
} from "./SystemConfigurationTypes"

// Hook exports
export type {
  UseSystemConfigurationOptions,
  UseSystemConfigurationReturn,
} from "./useSystemConfiguration"