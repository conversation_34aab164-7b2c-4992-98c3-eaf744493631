/**
 * System Configuration Organism Types
 * 
 * TypeScript type definitions for the System Configuration organism,
 * providing electrical standards configuration management interfaces.
 * 
 * Features:
 * - Professional electrical engineering configuration types
 * - IEEE/IEC standards compliance management
 * - Multi-level configuration hierarchy
 * - WCAG 2.1 AA accessibility support
 * - TypeScript strict mode compliance
 */

import type { LucideIcon } from "lucide-react"

// Configuration Category Types
export type ConfigurationCategory = 
  | "general"
  | "electrical_standards"
  | "safety_protocols"
  | "design_parameters"
  | "calculation_methods"
  | "reporting"
  | "compliance"
  | "notifications"
  | "integrations"
  | "advanced"

// Configuration Level Types
export type ConfigurationLevel = "system" | "project" | "user"

// Configuration Data Types
export type ConfigurationValueType = 
  | "string" 
  | "number" 
  | "boolean" 
  | "select" 
  | "multiselect" 
  | "range" 
  | "date" 
  | "time" 
  | "datetime"
  | "color"
  | "file"
  | "json"

// Validation Rule Types
export type ValidationRule = 
  | "required"
  | "min"
  | "max" 
  | "minLength"
  | "maxLength"
  | "pattern"
  | "custom"

// Electrical Standards Configuration
export type ElectricalStandardConfig = 
  | "IEC-60079" // ATEX
  | "IEC-61508" // Functional Safety
  | "IEC-60364" // Low-Voltage Installations
  | "IEC-60287" // Cable Current Rating
  | "EN-50110" // Operation
  | "EN-60204" // Machinery Safety
  | "EN-50522" // Earthing
  | "IEEE-80" // Ground Systems
  | "IEEE-519" // Harmonic Control
  | "NFPA-70" // National Electrical Code
  | "BS-7671" // UK Wiring Regulations

// Configuration Status
export type ConfigurationStatus = "default" | "modified" | "error" | "warning" | "info"

// Configuration Option Definition
export interface ConfigurationOption {
  readonly value: string | number | boolean
  readonly label: string
  readonly description?: string
  readonly disabled?: boolean
  readonly group?: string
}

// Configuration Validation
export interface ConfigurationValidation {
  readonly rule: ValidationRule
  readonly value?: string | number
  readonly message: string
  readonly severity: "error" | "warning" | "info"
}

// Configuration Field Definition
export interface ConfigurationField {
  readonly id: string
  readonly name: string
  readonly label: string
  readonly description?: string
  readonly type: ConfigurationValueType
  readonly category: ConfigurationCategory
  readonly level: ConfigurationLevel
  readonly defaultValue: any
  readonly currentValue?: any
  readonly options?: ReadonlyArray<ConfigurationOption>
  readonly validations?: ReadonlyArray<ConfigurationValidation>
  readonly dependencies?: ReadonlyArray<string> // Field IDs this field depends on
  readonly isRequired?: boolean
  readonly isReadonly?: boolean
  readonly isHidden?: boolean
  readonly unit?: string
  readonly min?: number
  readonly max?: number
  readonly step?: number
  readonly placeholder?: string
  readonly helpText?: string
  readonly warningText?: string
  readonly status?: ConfigurationStatus
  readonly lastModified?: Date
  readonly modifiedBy?: string
}

// Configuration Section Definition
export interface ConfigurationSection {
  readonly id: string
  readonly title: string
  readonly description?: string
  readonly icon: LucideIcon
  readonly category: ConfigurationCategory
  readonly fields: ReadonlyArray<ConfigurationField>
  readonly isCollapsible: boolean
  readonly isExpanded: boolean
  readonly canReset: boolean
  readonly hasChanges: boolean
  readonly hasErrors: boolean
  readonly hasWarnings: boolean
  readonly requiredFields: number
  readonly completedFields: number
}

// Configuration Preset Definition
export interface ConfigurationPreset {
  readonly id: string
  readonly name: string
  readonly description: string
  readonly category: ConfigurationCategory
  readonly isBuiltIn: boolean
  readonly isCustom: boolean
  readonly values: Record<string, any>
  readonly applicableStandards: ReadonlyArray<ElectricalStandardConfig>
  readonly createdBy?: string
  readonly createdDate: Date
  readonly lastModified?: Date
  readonly tags?: ReadonlyArray<string>
}

// Configuration History Entry
export interface ConfigurationHistory {
  readonly id: string
  readonly fieldId: string
  readonly fieldName: string
  readonly oldValue: any
  readonly newValue: any
  readonly timestamp: Date
  readonly userId: string
  readonly userName: string
  readonly reason?: string
  readonly source: "user" | "system" | "import" | "preset"
}

// Configuration Import/Export Format
export interface ConfigurationExport {
  readonly version: string
  readonly exportDate: Date
  readonly exportedBy: string
  readonly categories: ReadonlyArray<ConfigurationCategory>
  readonly configurations: Record<string, any>
  readonly metadata: {
    readonly systemVersion: string
    readonly standardsVersion: Record<ElectricalStandardConfig, string>
    readonly checksum: string
  }
}

// Configuration Validation Result
export interface ConfigurationValidationResult {
  readonly isValid: boolean
  readonly errors: ReadonlyArray<{
    readonly fieldId: string
    readonly fieldName: string
    readonly message: string
    readonly severity: "error" | "warning"
  }>
  readonly warnings: ReadonlyArray<{
    readonly fieldId: string
    readonly fieldName: string
    readonly message: string
  }>
  readonly summary: {
    readonly totalFields: number
    readonly validFields: number
    readonly errorFields: number
    readonly warningFields: number
    readonly requiredMissing: number
  }
}

// Configuration Search/Filter Options
export interface ConfigurationFilter {
  readonly categories?: ReadonlyArray<ConfigurationCategory>
  readonly levels?: ReadonlyArray<ConfigurationLevel>
  readonly status?: ReadonlyArray<ConfigurationStatus>
  readonly searchQuery?: string
  readonly modifiedOnly?: boolean
  readonly errorOnly?: boolean
  readonly requiredOnly?: boolean
  readonly showHidden?: boolean
}

// System Configuration Props
export interface SystemConfigurationProps {
  /** Configuration sections to display */
  sections?: ReadonlyArray<ConfigurationSection>
  /** Available presets */
  presets?: ReadonlyArray<ConfigurationPreset>
  /** Configuration history */
  history?: ReadonlyArray<ConfigurationHistory>
  /** Active filters */
  filters?: ConfigurationFilter
  /** Active category */
  activeCategory?: ConfigurationCategory
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Read-only mode */
  readonly?: boolean
  /** Show advanced options */
  showAdvanced?: boolean
  /** Field change callback */
  onFieldChange?: (fieldId: string, value: any) => void
  /** Section toggle callback */
  onSectionToggle?: (sectionId: string) => void
  /** Category change callback */
  onCategoryChange?: (category: ConfigurationCategory) => void
  /** Preset application callback */
  onApplyPreset?: (presetId: string) => void
  /** Configuration save callback */
  onSave?: () => void
  /** Configuration reset callback */
  onReset?: (sectionId?: string) => void
  /** Configuration export callback */
  onExport?: (categories: ReadonlyArray<ConfigurationCategory>) => void
  /** Configuration import callback */
  onImport?: (data: ConfigurationExport) => void
  /** Validation callback */
  onValidate?: () => ConfigurationValidationResult
  /** Custom class name */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

// Configuration Field Props
export interface ConfigurationFieldProps {
  readonly field: ConfigurationField
  readonly value: any
  readonly disabled?: boolean
  readonly readonly?: boolean
  readonly showHelp?: boolean
  readonly onChange: (value: any) => void
  readonly onBlur?: () => void
  readonly onFocus?: () => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Configuration Section Props
export interface ConfigurationSectionProps {
  readonly section: ConfigurationSection
  readonly values: Record<string, any>
  readonly expanded?: boolean
  readonly readonly?: boolean
  readonly showAdvanced?: boolean
  readonly onFieldChange: (fieldId: string, value: any) => void
  readonly onToggleExpanded?: () => void
  readonly onReset?: () => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Configuration Category Nav Props
export interface ConfigurationCategoryNavProps {
  readonly categories: ReadonlyArray<{
    readonly category: ConfigurationCategory
    readonly label: string
    readonly icon: LucideIcon
    readonly count: number
    readonly hasChanges: boolean
    readonly hasErrors: boolean
  }>
  readonly activeCategory: ConfigurationCategory
  readonly onCategoryChange: (category: ConfigurationCategory) => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Configuration Toolbar Props
export interface ConfigurationToolbarProps {
  readonly hasChanges: boolean
  readonly hasErrors: boolean
  readonly isValidating?: boolean
  readonly isSaving?: boolean
  readonly canSave: boolean
  readonly canReset: boolean
  readonly canExport: boolean
  readonly canImport: boolean
  readonly onSave: () => void
  readonly onReset: () => void
  readonly onValidate: () => void
  readonly onExport: () => void
  readonly onImport: () => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Utility Types
export type ConfigurationSummary = Pick<ConfigurationSection, "id" | "title" | "hasChanges" | "hasErrors" | "requiredFields" | "completedFields">
export type ConfigurationFieldValue = Pick<ConfigurationField, "id" | "currentValue" | "defaultValue" | "type">

// Status Mapping Utilities
export const CONFIGURATION_STATUS_COLORS: Record<ConfigurationStatus, string> = {
  default: "text-muted-foreground",
  modified: "text-blue-600",
  error: "text-red-600",
  warning: "text-amber-600",
  info: "text-cyan-600",
} as const

export const CONFIGURATION_CATEGORY_LABELS: Record<ConfigurationCategory, string> = {
  general: "General Settings",
  electrical_standards: "Electrical Standards",
  safety_protocols: "Safety Protocols", 
  design_parameters: "Design Parameters",
  calculation_methods: "Calculation Methods",
  reporting: "Reports & Documentation",
  compliance: "Compliance Management",
  notifications: "Notifications",
  integrations: "System Integrations",
  advanced: "Advanced Configuration",
} as const

export const ELECTRICAL_STANDARD_LABELS: Record<ElectricalStandardConfig, string> = {
  "IEC-60079": "IEC 60079 (ATEX)",
  "IEC-61508": "IEC 61508 (Functional Safety)",
  "IEC-60364": "IEC 60364 (Low-Voltage Installations)",
  "IEC-60287": "IEC 60287 (Cable Current Rating)",
  "EN-50110": "EN 50110 (Operation)",
  "EN-60204": "EN 60204 (Machinery Safety)",
  "EN-50522": "EN 50522 (Earthing)",
  "IEEE-80": "IEEE 80 (Ground Systems)",
  "IEEE-519": "IEEE 519 (Harmonic Control)",
  "NFPA-70": "NFPA 70 (National Electrical Code)",
  "BS-7671": "BS 7671 (UK Wiring Regulations)",
} as const

export const CONFIGURATION_LEVEL_LABELS: Record<ConfigurationLevel, string> = {
  system: "System Level",
  project: "Project Level", 
  user: "User Level",
} as const

// Default Values
export const DEFAULT_CONFIGURATION_FILTER: ConfigurationFilter = {
  categories: [],
  levels: [],
  status: [],
  searchQuery: "",
  modifiedOnly: false,
  errorOnly: false,
  requiredOnly: false,
  showHidden: false,
} as const

export const CONFIGURATION_CATEGORIES_SEQUENCE: ReadonlyArray<ConfigurationCategory> = [
  "general",
  "electrical_standards",
  "safety_protocols",
  "design_parameters",
  "calculation_methods",
  "reporting",
  "compliance",
  "notifications",
  "integrations",
  "advanced",
] as const

// Built-in Configuration Presets
export const BUILT_IN_PRESETS: ReadonlyArray<Partial<ConfigurationPreset>> = [
  {
    id: "industrial-basic",
    name: "Industrial Basic",
    description: "Basic configuration for industrial electrical systems",
    category: "electrical_standards",
    isBuiltIn: true,
    applicableStandards: ["IEC-60364", "EN-50110"],
  },
  {
    id: "hazardous-area",
    name: "Hazardous Area",
    description: "Configuration for ATEX/hazardous area installations",
    category: "electrical_standards", 
    isBuiltIn: true,
    applicableStandards: ["IEC-60079", "EN-50110"],
  },
  {
    id: "safety-critical",
    name: "Safety Critical",
    description: "High-safety functional safety systems configuration",
    category: "safety_protocols",
    isBuiltIn: true,
    applicableStandards: ["IEC-61508", "IEC-60364"],
  },
] as const