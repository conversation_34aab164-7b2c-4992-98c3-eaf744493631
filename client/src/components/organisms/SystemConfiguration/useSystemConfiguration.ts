/**
 * System Configuration Custom Hook
 * 
 * Centralized hook for System Configuration organism state management,
 * combining server state (React Query) with client state (Zustand).
 * 
 * Features:
 * - Unified state management interface
 * - Real-time configuration synchronization
 * - Performance optimized operations
 * - Error handling and loading states
 * - TypeScript strict mode compliance
 * - Professional electrical engineering integration
 */

import { useCallback, useEffect, useMemo } from "react"
import { toast } from "sonner"

import { 
  useSystemConfigurationStore, 
  useSystemConfigurationSelectors 
} from "@/stores/systemConfigurationStore"
import {
  useConfigurationSections,
  useConfigurationPresets,
  useConfigurationHistory,
  useUpdateConfiguration,
  useValidateConfiguration,
  useExportConfiguration,
  useImportConfiguration,
  useResetConfiguration,
} from "@/hooks/api/useConfiguration"
import type { 
  ConfigurationSection,
  ConfigurationField,
  ConfigurationPreset,
  ConfigurationHistory,
  ConfigurationCategory,
  ConfigurationFilter,
  ConfigurationValidationResult,
  ConfigurationExport,
} from "./SystemConfigurationTypes"

export interface UseSystemConfigurationOptions {
  /** Enable real-time configuration sync */
  enableRealTime?: boolean
  /** Custom refetch interval in milliseconds */
  refetchInterval?: number
  /** Enable automatic validation */
  enableAutoValidation?: boolean
  /** Show advanced configuration options */
  showAdvanced?: boolean
  /** Maximum history entries to keep */
  maxHistoryEntries?: number
}

export interface UseSystemConfigurationReturn {
  // Data
  sections: ReadonlyArray<ConfigurationSection>
  presets: ReadonlyArray<ConfigurationPreset>
  history: ReadonlyArray<ConfigurationHistory>
  validationResult: ConfigurationValidationResult | null
  
  // State  
  loading: boolean
  error: string | null
  isValidating: boolean
  isSaving: boolean
  hasUnsavedChanges: boolean
  
  // UI State
  activeCategory: ConfigurationCategory
  filters: ConfigurationFilter
  selectedFields: ReadonlyArray<string>
  expandedSections: Set<string>
  searchQuery: string
  showAdvanced: boolean
  
  // Computed State
  filteredSections: ReadonlyArray<ConfigurationSection>
  categorySummary: ReadonlyArray<{
    category: ConfigurationCategory
    label: string
    count: number
    hasChanges: boolean
    hasErrors: boolean
  }>
  validationSummary: {
    totalFields: number
    validFields: number
    errorFields: number
    warningFields: number
    requiredMissing: number
  }
  canSave: boolean
  canReset: boolean
  
  // Actions - Field Management
  updateField: (fieldId: string, value: any) => void
  resetField: (fieldId: string) => void
  validateField: (fieldId: string) => boolean
  
  // Actions - Section Management  
  toggleSection: (sectionId: string) => void
  resetSection: (sectionId: string) => void
  expandAllSections: () => void
  collapseAllSections: () => void
  
  // Actions - Category Management
  setActiveCategory: (category: ConfigurationCategory) => void
  setCategoryFilter: (categories: ReadonlyArray<ConfigurationCategory>) => void
  
  // Actions - Preset Management
  applyPreset: (presetId: string) => void
  createPreset: (name: string, description: string, values: Record<string, any>) => void
  deletePreset: (presetId: string) => void
  
  // Actions - Configuration Management
  saveConfiguration: () => void
  resetConfiguration: (sectionIds?: ReadonlyArray<string>) => void
  validateConfiguration: () => void
  exportConfiguration: (categories?: ReadonlyArray<ConfigurationCategory>) => void
  importConfiguration: (data: ConfigurationExport) => void
  
  // Actions - Search & Filter
  setSearchQuery: (query: string) => void
  setFilters: (filters: Partial<ConfigurationFilter>) => void
  clearFilters: () => void
  
  // Actions - Selection
  selectField: (fieldId: string) => void
  deselectField: (fieldId: string) => void
  selectAllFields: () => void
  clearSelection: () => void
  
  // Actions - History
  revertToHistoryEntry: (historyId: string) => void
  clearHistory: () => void
  
  // Actions - Advanced
  toggleAdvancedMode: () => void
  refreshConfiguration: () => void
  getFieldDependencies: (fieldId: string) => ReadonlyArray<string>
}

export function useSystemConfiguration(
  options: UseSystemConfigurationOptions = {}
): UseSystemConfigurationReturn {
  const {
    enableRealTime = true,
    refetchInterval = 60000,
    enableAutoValidation = true,
    showAdvanced: externalShowAdvanced = false,
    maxHistoryEntries = 100,
  } = options

  // Zustand store state and actions
  const store = useSystemConfigurationStore()
  const selectors = useSystemConfigurationSelectors()
  
  // Server state queries
  const sectionsQuery = useConfigurationSections({
    refetchInterval,
    enabled: true,
  })
  
  const presetsQuery = useConfigurationPresets({
    refetchInterval: enableRealTime ? 300000 : 600000,
    enabled: true,
  })
  
  const historyQuery = useConfigurationHistory({
    limit: maxHistoryEntries,
    refetchInterval: enableRealTime ? 120000 : 300000,
    enabled: true,
  })
  
  // Mutations
  const updateConfigMutation = useUpdateConfiguration()
  const validateConfigMutation = useValidateConfiguration()
  const exportConfigMutation = useExportConfiguration()
  const importConfigMutation = useImportConfiguration() 
  const resetConfigMutation = useResetConfiguration()
  
  // Compute filtered sections based on current filters
  const filteredSections = useMemo(() => {
    if (!sectionsQuery.data?.sections) return []
    
    return sectionsQuery.data.sections.filter(section => {
      // Category filter
      if (store.filters.categories && 
          store.filters.categories.length > 0 &&
          !store.filters.categories.includes(section.category)) {
        return false
      }
      
      // Active category filter
      if (store.activeCategory !== "general" && 
          section.category !== store.activeCategory) {
        return false
      }
      
      // Status filters
      if (store.filters.errorOnly && !section.hasErrors) return false
      if (store.filters.modifiedOnly && !section.hasChanges) return false
      
      // Search query filter
      if (store.filters.searchQuery) {
        const query = store.filters.searchQuery.toLowerCase()
        const titleMatch = section.title.toLowerCase().includes(query)
        const descriptionMatch = section.description?.toLowerCase().includes(query) || false
        const fieldMatch = section.fields.some(field => 
          field.name.toLowerCase().includes(query) ||
          field.label.toLowerCase().includes(query) ||
          field.description?.toLowerCase().includes(query) ||
          false
        )
        if (!titleMatch && !descriptionMatch && !fieldMatch) return false
      }
      
      return true
    })
  }, [sectionsQuery.data?.sections, store.filters, store.activeCategory])
  
  // Generate category summary
  const categorySummary = useMemo(() => {
    if (!sectionsQuery.data?.sections) return []
    
    const categoryMap = new Map<ConfigurationCategory, {
      count: number
      hasChanges: boolean
      hasErrors: boolean
    }>()
    
    sectionsQuery.data.sections.forEach(section => {
      const existing = categoryMap.get(section.category) || {
        count: 0,
        hasChanges: false, 
        hasErrors: false
      }
      
      categoryMap.set(section.category, {
        count: existing.count + 1,
        hasChanges: existing.hasChanges || section.hasChanges,
        hasErrors: existing.hasErrors || section.hasErrors,
      })
    })
    
    return Array.from(categoryMap.entries()).map(([category, data]) => ({
      category,
      label: selectors.getCategoryLabel(category),
      ...data,
    }))
  }, [sectionsQuery.data?.sections, selectors])
  
  // Validation summary
  const validationSummary = useMemo(() => {
    if (!store.validationResult) {
      return {
        totalFields: 0,
        validFields: 0,
        errorFields: 0,
        warningFields: 0,
        requiredMissing: 0,
      }
    }
    
    return store.validationResult.summary
  }, [store.validationResult])
  
  // Auto-validation on field changes
  useEffect(() => {
    if (!enableAutoValidation || !store.hasUnsavedChanges) return
    
    const debounceTimer = setTimeout(() => {
      validateConfiguration()
    }, 1000)
    
    return () => clearTimeout(debounceTimer)
  }, [store.hasUnsavedChanges, enableAutoValidation])
  
  // Handle configuration change notifications
  useEffect(() => {
    if (!enableRealTime) return
    
    // Listen for external configuration changes
    // This would typically be WebSocket or polling logic
    const handleConfigurationChange = (event: any) => {
      if (event.type === "configuration_updated") {
        toast.info("Configuration updated by another user", {
          action: {
            label: "Refresh",
            onClick: () => refreshConfiguration(),
          },
        })
      }
    }
    
    // Mock event listener - replace with actual WebSocket/SSE
    // window.addEventListener("configuration_change", handleConfigurationChange)
    // return () => window.removeEventListener("configuration_change", handleConfigurationChange)
  }, [enableRealTime])
  
  // Actions
  const updateField = useCallback((fieldId: string, value: any) => {
    store.updateFieldValue(fieldId, value)
    
    // Trigger field validation if auto-validation is enabled
    if (enableAutoValidation) {
      validateField(fieldId)
    }
  }, [store, enableAutoValidation])
  
  const resetField = useCallback((fieldId: string) => {
    store.resetFieldValue(fieldId)
  }, [store])
  
  const validateField = useCallback((fieldId: string): boolean => {
    return selectors.validateFieldValue(fieldId)
  }, [selectors])
  
  const toggleSection = useCallback((sectionId: string) => {
    store.toggleSectionExpanded(sectionId)
  }, [store])
  
  const resetSection = useCallback(async (sectionId: string) => {
    try {
      await resetConfigMutation.mutateAsync({ sectionIds: [sectionId] })
      store.resetSectionValues(sectionId)
      toast.success("Section reset successfully")
    } catch (error) {
      console.error("Failed to reset section:", error)
      toast.error("Failed to reset section")
    }
  }, [resetConfigMutation, store])
  
  const expandAllSections = useCallback(() => {
    store.setAllSectionsExpanded(true)
  }, [store])
  
  const collapseAllSections = useCallback(() => {
    store.setAllSectionsExpanded(false)
  }, [store])
  
  const setActiveCategory = useCallback((category: ConfigurationCategory) => {
    store.setActiveCategory(category)
  }, [store])
  
  const setCategoryFilter = useCallback((categories: ReadonlyArray<ConfigurationCategory>) => {
    store.setFilters({ categories })
  }, [store])
  
  const applyPreset = useCallback(async (presetId: string) => {
    const preset = presetsQuery.data?.presets.find(p => p.id === presetId)
    if (!preset) {
      toast.error("Preset not found")
      return
    }
    
    try {
      store.applyPresetValues(preset.values)
      toast.success(`Applied preset: ${preset.name}`)
    } catch (error) {
      console.error("Failed to apply preset:", error)
      toast.error("Failed to apply preset")
    }
  }, [presetsQuery.data?.presets, store])
  
  const createPreset = useCallback(async (
    name: string, 
    description: string, 
    values: Record<string, any>
  ) => {
    // Implementation for creating custom presets
    toast.info("Custom preset creation feature coming soon")
  }, [])
  
  const deletePreset = useCallback(async (presetId: string) => {
    // Implementation for deleting custom presets
    toast.info("Preset deletion feature coming soon")
  }, [])
  
  const saveConfiguration = useCallback(async () => {
    if (!store.hasUnsavedChanges) return
    
    try {
      const changes = selectors.getUnsavedChanges()
      await updateConfigMutation.mutateAsync(changes)
      
      store.markChangesSaved()
      toast.success("Configuration saved successfully")
      
      // Refresh sections to get updated data
      sectionsQuery.refetch()
    } catch (error) {
      console.error("Failed to save configuration:", error)
      toast.error("Failed to save configuration")
    }
  }, [store, selectors, updateConfigMutation, sectionsQuery])
  
  const resetConfiguration = useCallback(async (sectionIds?: ReadonlyArray<string>) => {
    try {
      await resetConfigMutation.mutateAsync({ sectionIds })
      
      if (sectionIds) {
        sectionIds.forEach(id => store.resetSectionValues(id))
      } else {
        store.resetAllValues()
      }
      
      toast.success("Configuration reset successfully")
    } catch (error) {
      console.error("Failed to reset configuration:", error)
      toast.error("Failed to reset configuration")
    }
  }, [resetConfigMutation, store])
  
  const validateConfiguration = useCallback(async () => {
    try {
      store.setValidating(true)
      const result = await validateConfigMutation.mutateAsync({})
      store.setValidationResult(result)
      
      if (result.errors.length > 0) {
        toast.error(`Validation failed: ${result.errors.length} errors found`)
      } else if (result.warnings.length > 0) {
        toast.warning(`Validation completed with ${result.warnings.length} warnings`)
      } else {
        toast.success("Configuration validation passed")
      }
    } catch (error) {
      console.error("Validation failed:", error)
      toast.error("Configuration validation failed")
    } finally {
      store.setValidating(false)
    }
  }, [validateConfigMutation, store])
  
  const exportConfiguration = useCallback(async (categories?: ReadonlyArray<ConfigurationCategory>) => {
    try {
      const exportData = await exportConfigMutation.mutateAsync({ categories })
      
      // Create download link
      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: "application/json",
      })
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.href = url
      link.download = `system-configuration-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast.success("Configuration exported successfully")
    } catch (error) {
      console.error("Export failed:", error)
      toast.error("Configuration export failed")
    }
  }, [exportConfigMutation])
  
  const importConfiguration = useCallback(async (data: ConfigurationExport) => {
    try {
      await importConfigMutation.mutateAsync(data)
      
      // Refresh sections to show imported data
      sectionsQuery.refetch()
      
      toast.success("Configuration imported successfully")
    } catch (error) {
      console.error("Import failed:", error)
      toast.error("Configuration import failed")
    }
  }, [importConfigMutation, sectionsQuery])
  
  const setSearchQuery = useCallback((query: string) => {
    store.setSearchQuery(query)
  }, [store])
  
  const setFilters = useCallback((filters: Partial<ConfigurationFilter>) => {
    store.setFilters(filters)
  }, [store])
  
  const clearFilters = useCallback(() => {
    store.clearFilters()
  }, [store])
  
  const selectField = useCallback((fieldId: string) => {
    store.selectField(fieldId)
  }, [store])
  
  const deselectField = useCallback((fieldId: string) => {
    store.deselectField(fieldId)
  }, [store])
  
  const selectAllFields = useCallback(() => {
    const allFieldIds = filteredSections.flatMap(section => 
      section.fields.map(field => field.id)
    )
    store.selectAllFields(allFieldIds)
  }, [filteredSections, store])
  
  const clearSelection = useCallback(() => {
    store.clearSelection()
  }, [store])
  
  const revertToHistoryEntry = useCallback(async (historyId: string) => {
    const historyEntry = historyQuery.data?.history.find(h => h.id === historyId)
    if (!historyEntry) {
      toast.error("History entry not found")
      return
    }
    
    try {
      store.updateFieldValue(historyEntry.fieldId, historyEntry.oldValue)
      toast.success(`Reverted ${historyEntry.fieldName} to previous value`)
    } catch (error) {
      console.error("Failed to revert to history entry:", error)
      toast.error("Failed to revert change")
    }
  }, [historyQuery.data?.history, store])
  
  const clearHistory = useCallback(() => {
    store.clearHistory()
  }, [store])
  
  const toggleAdvancedMode = useCallback(() => {
    store.setShowAdvanced(!store.showAdvanced)
  }, [store])
  
  const refreshConfiguration = useCallback(() => {
    Promise.all([
      sectionsQuery.refetch(),
      presetsQuery.refetch(),
      historyQuery.refetch(),
    ])
  }, [sectionsQuery, presetsQuery, historyQuery])
  
  const getFieldDependencies = useCallback((fieldId: string): ReadonlyArray<string> => {
    return selectors.getFieldDependencies(fieldId)
  }, [selectors])
  
  // Computed values
  const loading = sectionsQuery.isLoading
  const error = sectionsQuery.error?.message || null
  const showAdvanced = externalShowAdvanced || store.showAdvanced
  
  return {
    // Data
    sections: sectionsQuery.data?.sections || [],
    presets: presetsQuery.data?.presets || [],
    history: historyQuery.data?.history || [],
    validationResult: store.validationResult,
    
    // State
    loading,
    error,
    isValidating: store.isValidating || validateConfigMutation.isPending,
    isSaving: updateConfigMutation.isPending,
    hasUnsavedChanges: store.hasUnsavedChanges,
    
    // UI State
    activeCategory: store.activeCategory,
    filters: store.filters,
    selectedFields: store.selectedFields,
    expandedSections: store.expandedSections,
    searchQuery: store.searchQuery,
    showAdvanced,
    
    // Computed State
    filteredSections,
    categorySummary,
    validationSummary,
    canSave: store.hasUnsavedChanges && !updateConfigMutation.isPending,
    canReset: store.hasUnsavedChanges,
    
    // Actions
    updateField,
    resetField,
    validateField,
    toggleSection,
    resetSection,
    expandAllSections,
    collapseAllSections,
    setActiveCategory,
    setCategoryFilter,
    applyPreset,
    createPreset,
    deletePreset,
    saveConfiguration,
    resetConfiguration,
    validateConfiguration,
    exportConfiguration,
    importConfiguration,
    setSearchQuery,
    setFilters,
    clearFilters,
    selectField,
    deselectField,
    selectAllFields,
    clearSelection,
    revertToHistoryEntry,
    clearHistory,
    toggleAdvancedMode,
    refreshConfiguration,
    getFieldDependencies,
  }
}