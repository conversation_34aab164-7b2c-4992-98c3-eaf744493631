/**
 * Project Navigation Organism Exports
 * 
 * Central export point for all Project Navigation organism components, types, and utilities.
 * Provides clean imports for other parts of the application.
 */

// Main component exports
export { ProjectNavigation } from "./ProjectNavigation"
export { useProjectNavigation } from "./useProjectNavigation"

// Type exports
export type {
  ProjectNavigationProps,
  ProjectInfo,
  NavigationItem,
  TeamMember,
  ProjectPhase,
  ProjectStatus,
  ProjectPriority,
  ElectricalStandard,
  ProjectRole,
  ProjectProgress,
  NavigationConfig,
  NavigationBreadcrumbProps,
  ProjectHeaderProps,
  PhaseNavigatorProps,
  TeamPanelProps,
  NavigationItemWithProgress,
  ProjectSummary,
  PhaseStatus,
} from "./ProjectNavigationTypes"

// Utility exports
export {
  PROJECT_STATUS_COLORS,
  PROJECT_PHASE_LABELS,
  ELECTRICAL_STANDARD_LABELS,
  DEFAULT_NAVIGATION_CONFIG,
  PROJECT_PHASES_SEQUENCE,
} from "./ProjectNavigationTypes"

// Hook exports
export type {
  UseProjectNavigationOptions,
  UseProjectNavigationReturn,
} from "./useProjectNavigation"