/**
 * Project Navigation Organism
 * 
 * Comprehensive electrical project navigation interface combining atoms and molecules
 * to create a professional-grade project workflow navigation system.
 * 
 * Features:
 * - Professional electrical engineering project navigation
 * - IEEE/IEC standards compliance tracking
 * - Phase-based project workflow
 * - WCAG 2.1 AA accessibility compliance
 * - TypeScript strict mode compliance
 * - Performance optimized with virtualization
 * - Team collaboration integration
 * - Real-time progress tracking
 * - Multi-phase project management
 */

import React, { Suspense } from "react"
import { ErrorBoundary } from "react-error-boundary"
import { 
  FolderOpen,
  Users,
  Settings,
  Bell,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowRight,
  ArrowLeft,
  MoreHorizontal,
  FileText,
  Zap,
  Target,
  TrendingUp,
  Calendar,
  MapPin,
  User,
  Shield,
  Activity,
  ChevronRight,
  ChevronDown,
  Home,
  Briefcase
} from "lucide-react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/atoms/Button"
import { Badge } from "@/components/atoms/Badge"
import { StatusIndicator } from "@/components/atoms/StatusIndicator"
import { ProgressBar } from "@/components/atoms/ProgressBar"
import { Avatar } from "@/components/atoms/Avatar"
import { SearchBox } from "@/components/molecules/SearchBox"
import { HealthIndicator } from "@/components/molecules/HealthIndicator"
import { useProjectNavigation } from "./useProjectNavigation"
import type { 
  ProjectNavigationProps, 
  ProjectInfo, 
  NavigationItem,
  TeamMember,
  ProjectPhase
} from "./ProjectNavigationTypes"

// Project Header Component
const ProjectHeader: React.FC<{
  project: ProjectInfo
  progress: number
  showProgress: boolean
  showTeam: boolean
  teamMembers: ReadonlyArray<TeamMember>
  onSettingsClick: () => void
  onTeamClick: () => void
}> = ({ 
  project, 
  progress, 
  showProgress, 
  showTeam, 
  teamMembers,
  onSettingsClick,
  onTeamClick 
}) => (
  <div className="border-b bg-card p-4">
    <div className="flex items-center justify-between mb-3">
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <FolderOpen className="h-5 w-5 text-primary" />
          <div>
            <h2 className="font-semibold text-lg leading-none">{project.name}</h2>
            <p className="text-sm text-muted-foreground mt-1">
              {project.code} • {project.client}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline" size="sm">
            {project.status.replace("_", " ")}
          </Badge>
          <Badge 
            variant={project.priority === "critical" ? "destructive" : 
                    project.priority === "high" ? "secondary" : "outline"} 
            size="sm"
          >
            {project.priority}
          </Badge>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        {showTeam && teamMembers.length > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onTeamClick}
            className="h-8"
          >
            <Users className="h-4 w-4 mr-1" />
            {teamMembers.length}
          </Button>
        )}
        
        <Button
          variant="ghost"
          size="sm"
          onClick={onSettingsClick}
        >
          <Settings className="h-4 w-4" />
        </Button>
      </div>
    </div>
    
    {showProgress && (
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Overall Progress</span>
          <span className="font-medium">{Math.round(progress)}%</span>
        </div>
        <ProgressBar 
          value={progress} 
          className="h-2" 
        />
      </div>
    )}
    
    <div className="flex items-center gap-4 mt-3 text-sm text-muted-foreground">
      <div className="flex items-center gap-1">
        <MapPin className="h-3 w-3" />
        {project.location}
      </div>
      <div className="flex items-center gap-1">
        <Calendar className="h-3 w-3" />
        {project.estimatedEndDate ? 
          `Due ${project.estimatedEndDate.toLocaleDateString()}` : 
          "No end date"
        }
      </div>
      {project.applicableStandards.length > 0 && (
        <div className="flex items-center gap-1">
          <Shield className="h-3 w-3" />
          {project.applicableStandards.slice(0, 2).join(", ")}
          {project.applicableStandards.length > 2 && ` +${project.applicableStandards.length - 2}`}
        </div>
      )}
    </div>
  </div>
)

// Navigation Item Component
const NavigationItemComponent: React.FC<{
  item: NavigationItem
  level: number
  isExpanded: boolean
  onToggleExpanded: () => void
  onItemClick: (item: NavigationItem) => void
}> = ({ item, level, isExpanded, onToggleExpanded, onItemClick }) => {
  const hasChildren = item.children && item.children.length > 0
  const IconComponent = item.icon || FileText
  
  return (
    <div>
      <div
        className={cn(
          "flex items-center gap-2 p-2 rounded-md text-sm cursor-pointer transition-colors",
          "hover:bg-muted/50",
          item.isActive && "bg-primary/10 text-primary border-l-2 border-l-primary",
          item.isDisabled && "opacity-50 cursor-not-allowed",
          level > 0 && `ml-${level * 4}`
        )}
        onClick={() => !item.isDisabled && onItemClick(item)}
      >
        {hasChildren && (
          <button
            onClick={(e) => {
              e.stopPropagation()
              onToggleExpanded()
            }}
            className="p-0.5 hover:bg-muted rounded"
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </button>
        )}
        
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <IconComponent className={cn("h-4 w-4 flex-shrink-0", 
            item.isCompleted ? "text-green-600" : 
            item.hasIssues ? "text-red-500" : "text-muted-foreground"
          )} />
          
          <span className="truncate flex-1">{item.label}</span>
          
          {item.isCompleted && (
            <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
          )}
          
          {item.hasIssues && (
            <Badge variant="destructive" size="sm" className="flex-shrink-0">
              {item.issueCount}
            </Badge>
          )}
          
          {typeof item.progress === "number" && !item.isCompleted && (
            <div className="flex items-center gap-1 flex-shrink-0">
              <div className="w-8 h-1 bg-muted rounded-full overflow-hidden">
                <div 
                  className="h-full bg-primary transition-all"
                  style={{ width: `${item.progress}%` }}
                />
              </div>
              <span className="text-xs text-muted-foreground">
                {Math.round(item.progress)}%
              </span>
            </div>
          )}
        </div>
      </div>
      
      {hasChildren && isExpanded && (
        <div className="ml-4 space-y-1">
          {item.children!.map((child) => (
            <NavigationItemComponent
              key={child.id}
              item={child}
              level={level + 1}
              isExpanded={false}
              onToggleExpanded={() => {}}
              onItemClick={onItemClick}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Navigation Breadcrumbs Component
const NavigationBreadcrumbs: React.FC<{
  breadcrumbs: ReadonlyArray<NavigationItem>
  onItemClick: (item: NavigationItem) => void
}> = ({ breadcrumbs, onItemClick }) => {
  if (breadcrumbs.length === 0) return null
  
  return (
    <div className="flex items-center gap-1 text-sm text-muted-foreground border-b p-3">
      <Home className="h-3 w-3" />
      {breadcrumbs.map((item, index) => (
        <React.Fragment key={item.id}>
          {index > 0 && <ChevronRight className="h-3 w-3" />}
          <button
            onClick={() => onItemClick(item)}
            className="hover:text-foreground transition-colors truncate"
          >
            {item.label}
          </button>
        </React.Fragment>
      ))}
    </div>
  )
}

// Team Panel Component
const TeamPanel: React.FC<{
  team: ReadonlyArray<TeamMember>
  showWorkload: boolean
  onMemberClick: (member: TeamMember) => void
}> = ({ team, showWorkload, onMemberClick }) => (
  <div className="border-t bg-card p-4">
    <div className="flex items-center justify-between mb-3">
      <h3 className="font-medium text-sm">Project Team</h3>
      <Badge variant="outline" size="sm">
        {team.filter(m => m.isActive).length}/{team.length}
      </Badge>
    </div>
    
    <div className="space-y-2">
      {team.slice(0, 6).map((member) => (
        <div
          key={member.id}
          className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50 cursor-pointer transition-colors"
          onClick={() => onMemberClick(member)}
        >
          <Avatar
            src={member.avatar}
            alt={member.name}
            size="sm"
            className="flex-shrink-0"
          />
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium truncate">{member.name}</p>
              <StatusIndicator
                variant={member.isOnline ? "operational" : "offline"}
                size="sm"
              />
            </div>
            <p className="text-xs text-muted-foreground">
              {member.role.replace("_", " ")}
            </p>
          </div>
          
          {showWorkload && typeof member.workload === "number" && (
            <div className="flex items-center gap-1 flex-shrink-0">
              <div className="w-8 h-1 bg-muted rounded-full overflow-hidden">
                <div 
                  className={cn("h-full transition-all",
                    member.workload > 80 ? "bg-red-500" :
                    member.workload > 60 ? "bg-amber-500" : "bg-green-500"
                  )}
                  style={{ width: `${member.workload}%` }}
                />
              </div>
              <span className="text-xs text-muted-foreground">
                {member.workload}%
              </span>
            </div>
          )}
        </div>
      ))}
      
      {team.length > 6 && (
        <Button variant="ghost" size="sm" className="w-full mt-2">
          View All ({team.length})
        </Button>
      )}
    </div>
  </div>
)

// Error Fallback Component
const ErrorFallback: React.FC<{ error: Error; resetErrorBoundary: () => void }> = ({ 
  error, 
  resetErrorBoundary 
}) => (
  <div className="flex flex-col items-center justify-center p-8 text-center">
    <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
    <h3 className="text-lg font-semibold mb-2">Navigation Error</h3>
    <p className="text-muted-foreground mb-4">
      {error.message || "An unexpected error occurred"}
    </p>
    <Button onClick={resetErrorBoundary}>
      Try Again
    </Button>
  </div>
)

// Main Project Navigation Component
export const ProjectNavigation = React.forwardRef<HTMLDivElement, ProjectNavigationProps>(
  (
    {
      project: externalProject,
      config: externalConfig,
      activeItemId: externalActiveItemId,
      loading: externalLoading = false,
      error: externalError = null,
      onItemClick,
      onPhaseChange,
      onTeamMemberClick,
      onProjectSettings,
      onNotificationClick,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    // Use internal hook if no external data provided
    const hookData = useProjectNavigation({
      projectId: externalProject?.id || "",
      enableRealTime: true,
      enableNotifications: true,
    })
    
    // Use external props or fall back to hook data
    const project = externalProject || hookData.project
    const config = externalConfig || hookData.config
    const activeItemId = externalActiveItemId || hookData.activeItemId
    const loading = externalLoading || hookData.loading
    const error = externalError || hookData.error
    
    // Use external callbacks or hook callbacks
    const handleItemClick = onItemClick || hookData.navigateToItem
    const handlePhaseChange = onPhaseChange || hookData.navigateToPhase
    const handleTeamMemberClick = onTeamMemberClick || ((member: TeamMember) => {})
    const handleProjectSettings = onProjectSettings || (() => {})
    const handleNotificationClick = onNotificationClick || (() => {})
    
    const [expandedItems, setExpandedItems] = React.useState<Set<string>>(new Set())
    
    const toggleItemExpanded = (itemId: string) => {
      setExpandedItems(prev => {
        const next = new Set(prev)
        if (next.has(itemId)) {
          next.delete(itemId)
        } else {
          next.add(itemId)
        }
        return next
      })
    }
    
    if (!project) {
      return (
        <div 
          ref={ref}
          className={cn("flex items-center justify-center p-8", className)}
          data-testid={testId}
          {...props}
        >
          {loading ? (
            <div className="space-y-4">
              <div className="h-16 bg-muted animate-pulse rounded-lg" />
              <div className="space-y-2">
                {Array.from({ length: 6 }).map((_, i) => (
                  <div key={i} className="h-8 bg-muted/60 animate-pulse rounded" />
                ))}
              </div>
            </div>
          ) : error ? (
            <ErrorFallback 
              error={new Error(error)} 
              resetErrorBoundary={() => hookData.refreshProject()}
            />
          ) : (
            <div className="text-center text-muted-foreground">
              <Briefcase className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>No project data available</p>
            </div>
          )}
        </div>
      )
    }
    
    return (
      <ErrorBoundary FallbackComponent={ErrorFallback} onReset={hookData.refreshProject}>
        <nav
          ref={ref}
          className={cn(
            "flex flex-col h-full bg-background border-r",
            config.compactMode && "w-64",
            !config.compactMode && "w-80",
            className
          )}
          data-testid={testId || "project-navigation"}
          {...props}
        >
          {/* Project Header */}
          <ProjectHeader
            project={project}
            progress={hookData.overallProgress}
            showProgress={config.showProgress}
            showTeam={config.showTeamMembers}
            teamMembers={hookData.activeTeamMembers}
            onSettingsClick={handleProjectSettings}
            onTeamClick={hookData.toggleTeamPanel}
          />
          
          {/* Breadcrumbs */}
          <NavigationBreadcrumbs
            breadcrumbs={hookData.breadcrumbs}
            onItemClick={handleItemClick}
          />
          
          {/* Navigation Items */}
          <div className="flex-1 overflow-y-auto">
            <Suspense fallback={<div className="p-4">Loading navigation...</div>}>
              {loading && hookData.navigationItems.length === 0 ? (
                <div className="space-y-2 p-4">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <div key={i} className="h-8 bg-muted/50 rounded animate-pulse" />
                  ))}
                </div>
              ) : hookData.navigationItems.length === 0 ? (
                <div className="text-center py-12 px-4">
                  <Target className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                  <h3 className="text-sm font-medium mb-2">No Navigation Items</h3>
                  <p className="text-xs text-muted-foreground">
                    Project phases will appear here when configured.
                  </p>
                </div>
              ) : (
                <div className="p-4 space-y-1">
                  {hookData.navigationItems.map((item) => (
                    <NavigationItemComponent
                      key={item.id}
                      item={item}
                      level={0}
                      isExpanded={expandedItems.has(item.id)}
                      onToggleExpanded={() => toggleItemExpanded(item.id)}
                      onItemClick={handleItemClick}
                    />
                  ))}
                </div>
              )}
            </Suspense>
          </div>
          
          {/* Team Panel */}
          {config.showTeamMembers && hookData.isTeamPanelOpen && (
            <TeamPanel
              team={hookData.team}
              showWorkload={true}
              onMemberClick={handleTeamMemberClick}
            />
          )}
        </nav>
      </ErrorBoundary>
    )
  }
)

ProjectNavigation.displayName = "ProjectNavigation"

// Export all components and types
export {
  type ProjectNavigationProps,
  type ProjectInfo,
  type NavigationItem,
  type TeamMember,
  type ProjectPhase,
  type NavigationConfig,
  type ProjectProgress,
} from "./ProjectNavigationTypes"

export { useProjectNavigation } from "./useProjectNavigation"