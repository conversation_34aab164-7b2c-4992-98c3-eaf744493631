/**
 * Project Navigation Organism Types
 * 
 * TypeScript type definitions for the Project Navigation organism,
 * providing professional electrical project workflow navigation interfaces.
 * 
 * Features:
 * - Professional electrical engineering project types
 * - IEEE/IEC standards compliance tracking
 * - Project phase management
 * - WCAG 2.1 AA accessibility support
 * - TypeScript strict mode compliance
 */

import type { LucideIcon } from "lucide-react"
import type { HealthStatus } from "@/components/molecules/HealthIndicator"

// Project Status Types
export type ProjectStatus = 
  | "draft"
  | "planning"
  | "design"
  | "review"
  | "approved"
  | "construction"
  | "testing"
  | "commissioning"
  | "completed"
  | "on_hold"
  | "cancelled"

// Project Phase Types for Electrical Engineering
export type ProjectPhase = 
  | "initial_consultation"
  | "site_survey"
  | "preliminary_design"
  | "detailed_design"
  | "standards_compliance"
  | "documentation"
  | "approval_submission"
  | "construction_planning"
  | "installation"
  | "testing_commissioning"
  | "final_documentation"
  | "handover"

// Project Priority Levels
export type ProjectPriority = "critical" | "high" | "medium" | "low"

// Electrical Standards Types
export type ElectricalStandard = 
  | "IEC-60079" // ATEX
  | "IEC-61508" // Functional Safety
  | "IEC-60364" // Low-Voltage Installations
  | "IEC-60287" // Cable Current Rating
  | "EN-50110" // Operation
  | "EN-60204" // Machinery Safety
  | "EN-50522" // Earthing
  | "IEEE-80" // Ground Systems
  | "IEEE-519" // Harmonic Control
  | "NEC" // National Electrical Code

// Project Role Types
export type ProjectRole = 
  | "project_manager"
  | "lead_engineer"
  | "design_engineer"
  | "compliance_specialist"
  | "site_supervisor"
  | "technician"
  | "reviewer"
  | "client_representative"

// Navigation Item Types
export interface NavigationItem {
  readonly id: string
  readonly label: string
  readonly icon: LucideIcon
  readonly href: string
  readonly phase?: ProjectPhase
  readonly isActive: boolean
  readonly isCompleted: boolean
  readonly isDisabled?: boolean
  readonly hasIssues?: boolean
  readonly issueCount?: number
  readonly progress?: number // 0-100
  readonly children?: ReadonlyArray<NavigationItem>
}

// Project Progress Information
export interface ProjectProgress {
  readonly phase: ProjectPhase
  readonly phaseProgress: number // 0-100
  readonly overallProgress: number // 0-100
  readonly completedTasks: number
  readonly totalTasks: number
  readonly nextMilestone?: string
  readonly nextMilestoneDate?: Date
  readonly estimatedCompletion?: Date
  readonly health: HealthStatus
  readonly issues: ReadonlyArray<{
    readonly id: string
    readonly phase: ProjectPhase
    readonly severity: "critical" | "warning" | "info"
    readonly title: string
    readonly description: string
    readonly assignee?: string
  }>
}

// Team Member Information
export interface TeamMember {
  readonly id: string
  readonly name: string
  readonly role: ProjectRole
  readonly email: string
  readonly avatar?: string
  readonly isOnline: boolean
  readonly isActive: boolean
  readonly currentPhase?: ProjectPhase
  readonly workload?: number // 0-100
}

// Project Information
export interface ProjectInfo {
  readonly id: string
  readonly name: string
  readonly code: string
  readonly description?: string
  readonly client: string
  readonly location: string
  readonly status: ProjectStatus
  readonly priority: ProjectPriority
  readonly startDate: Date
  readonly endDate?: Date
  readonly estimatedEndDate?: Date
  readonly budget?: number
  readonly budgetSpent?: number
  readonly applicableStandards: ReadonlyArray<ElectricalStandard>
  readonly projectManager: TeamMember
  readonly leadEngineer: TeamMember
  readonly team: ReadonlyArray<TeamMember>
  readonly progress: ProjectProgress
  readonly lastActivity: Date
  readonly isTemplate?: boolean
}

// Navigation Configuration
export interface NavigationConfig {
  readonly showProgress: boolean
  readonly showTeamMembers: boolean
  readonly showNotifications: boolean
  readonly compactMode: boolean
  readonly groupByPhase: boolean
  readonly showCompletedPhases: boolean
  readonly autoCollapse: boolean
}

// Project Navigation Props
export interface ProjectNavigationProps {
  /** Current project information */
  project: ProjectInfo
  /** Navigation configuration */
  config?: NavigationConfig
  /** Active navigation item ID */
  activeItemId?: string
  /** Loading state */
  loading?: boolean
  /** Error state */
  error?: string | null
  /** Navigation item click callback */
  onItemClick?: (item: NavigationItem) => void
  /** Phase change callback */
  onPhaseChange?: (phase: ProjectPhase) => void
  /** Team member click callback */
  onTeamMemberClick?: (member: TeamMember) => void
  /** Project settings callback */
  onProjectSettings?: () => void
  /** Notification click callback */
  onNotificationClick?: (notificationId: string) => void
  /** Custom class name */
  className?: string
  /** Test identifier */
  "data-testid"?: string
}

// Navigation Breadcrumb Props
export interface NavigationBreadcrumbProps {
  readonly project: ProjectInfo
  readonly currentPhase: ProjectPhase
  readonly currentItem?: NavigationItem
  readonly showProjectInfo?: boolean
  readonly onItemClick?: (item: NavigationItem) => void
  readonly onPhaseClick?: (phase: ProjectPhase) => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Project Header Props
export interface ProjectHeaderProps {
  readonly project: ProjectInfo
  readonly showProgress?: boolean
  readonly showTeam?: boolean
  readonly compact?: boolean
  readonly onSettingsClick?: () => void
  readonly onTeamClick?: () => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Phase Navigator Props
export interface PhaseNavigatorProps {
  readonly phases: ReadonlyArray<{
    readonly phase: ProjectPhase
    readonly label: string
    readonly isActive: boolean
    readonly isCompleted: boolean
    readonly progress: number
  }>
  readonly currentPhase: ProjectPhase
  readonly orientation: "horizontal" | "vertical"
  readonly showProgress?: boolean
  readonly onPhaseClick?: (phase: ProjectPhase) => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Team Panel Props
export interface TeamPanelProps {
  readonly team: ReadonlyArray<TeamMember>
  readonly currentPhase?: ProjectPhase
  readonly showWorkload?: boolean
  readonly maxMembers?: number
  readonly onMemberClick?: (member: TeamMember) => void
  readonly onViewAllClick?: () => void
  readonly className?: string
  readonly "data-testid"?: string
}

// Utility Types
export type NavigationItemWithProgress = NavigationItem & {
  readonly progress: number
  readonly health: HealthStatus
}

export type ProjectSummary = Pick<ProjectInfo, "id" | "name" | "code" | "status" | "priority" | "progress">
export type PhaseStatus = "not_started" | "in_progress" | "completed" | "blocked"

// Status Mapping Utilities
export const PROJECT_STATUS_COLORS: Record<ProjectStatus, string> = {
  draft: "text-gray-500",
  planning: "text-blue-600",
  design: "text-purple-600",
  review: "text-amber-600",
  approved: "text-green-600",
  construction: "text-orange-600",
  testing: "text-cyan-600",
  commissioning: "text-indigo-600",
  completed: "text-emerald-600",
  on_hold: "text-yellow-500",
  cancelled: "text-red-500",
} as const

export const PROJECT_PHASE_LABELS: Record<ProjectPhase, string> = {
  initial_consultation: "Initial Consultation",
  site_survey: "Site Survey",
  preliminary_design: "Preliminary Design",
  detailed_design: "Detailed Design",
  standards_compliance: "Standards Compliance",
  documentation: "Documentation",
  approval_submission: "Approval Submission",
  construction_planning: "Construction Planning",
  installation: "Installation",
  testing_commissioning: "Testing & Commissioning",
  final_documentation: "Final Documentation",
  handover: "Handover",
} as const

export const ELECTRICAL_STANDARD_LABELS: Record<ElectricalStandard, string> = {
  "IEC-60079": "IEC 60079 (ATEX)",
  "IEC-61508": "IEC 61508 (Functional Safety)",
  "IEC-60364": "IEC 60364 (Low-Voltage Installations)",
  "IEC-60287": "IEC 60287 (Cable Current Rating)",
  "EN-50110": "EN 50110 (Operation)",
  "EN-60204": "EN 60204 (Machinery Safety)",
  "EN-50522": "EN 50522 (Earthing)",
  "IEEE-80": "IEEE 80 (Ground Systems)",
  "IEEE-519": "IEEE 519 (Harmonic Control)",
  "NEC": "NEC (National Electrical Code)",
} as const

// Default Values
export const DEFAULT_NAVIGATION_CONFIG: NavigationConfig = {
  showProgress: true,
  showTeamMembers: true,
  showNotifications: true,
  compactMode: false,
  groupByPhase: true,
  showCompletedPhases: true,
  autoCollapse: false,
} as const

export const PROJECT_PHASES_SEQUENCE: ReadonlyArray<ProjectPhase> = [
  "initial_consultation",
  "site_survey",
  "preliminary_design",
  "detailed_design",
  "standards_compliance",
  "documentation",
  "approval_submission",
  "construction_planning",
  "installation",
  "testing_commissioning",
  "final_documentation",
  "handover",
] as const