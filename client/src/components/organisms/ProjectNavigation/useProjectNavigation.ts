/**
 * Project Navigation Custom Hook
 * 
 * Centralized hook for Project Navigation organism state management,
 * combining server state (React Query) with client state (Zustand).
 * 
 * Features:
 * - Unified state management interface
 * - Real-time project data synchronization
 * - Performance optimized operations
 * - Error handling and loading states
 * - TypeScript strict mode compliance
 * - Professional electrical engineering integration
 */

import { useCallback, useEffect, useMemo } from "react"
import { useRouter } from "next/navigation"
import { toast } from "sonner"

import { 
  useProjectNavigationStore, 
  useProjectNavigationSelectors 
} from "@/stores/projectNavigationStore"
import {
  useProjectDetail,
  useProjectTeam,
  useProjectProgress,
  useProjectNotifications,
  useUpdateProjectPhase,
  useProjectActivityLog,
} from "@/hooks/api/useProjects"
import type { 
  ProjectInfo, 
  ProjectPhase, 
  NavigationItem, 
  TeamMember,
  NavigationConfig,
  ProjectProgress
} from "./ProjectNavigationTypes"

export interface UseProjectNavigationOptions {
  /** Project ID to navigate */
  projectId: string
  /** Enable real-time updates */
  enableRealTime?: boolean
  /** Custom refetch interval in milliseconds */
  refetchInterval?: number
  /** Enable automatic notifications */
  enableNotifications?: boolean
  /** Maximum team members to show */
  maxTeamMembers?: number
}

export interface UseProjectNavigationReturn {
  // Data
  project: ProjectInfo | null
  team: ReadonlyArray<TeamMember>
  progress: ProjectProgress | null
  notifications: ReadonlyArray<any>
  navigationItems: ReadonlyArray<NavigationItem>
  
  // State
  loading: boolean
  error: string | null
  isUpdating: boolean
  currentPhase: ProjectPhase | null
  activeItemId: string | null
  
  // UI State
  config: NavigationConfig
  isTeamPanelOpen: boolean
  isNotificationPanelOpen: boolean
  breadcrumbs: ReadonlyArray<NavigationItem>
  
  // Computed State
  completedPhases: ReadonlyArray<ProjectPhase>
  currentPhaseProgress: number
  overallProgress: number
  hasUnreadNotifications: boolean
  activeTeamMembers: ReadonlyArray<TeamMember>
  
  // Actions - Navigation
  navigateToItem: (item: NavigationItem) => void
  navigateToPhase: (phase: ProjectPhase) => void
  setActiveItem: (itemId: string) => void
  goBack: () => void
  goForward: () => void
  
  // Actions - Phase Management
  markPhaseComplete: (phase: ProjectPhase) => void
  updatePhaseProgress: (phase: ProjectPhase, progress: number) => void
  moveToNextPhase: () => void
  moveToPreviousPhase: () => void
  
  // Actions - UI Management
  updateConfig: (config: Partial<NavigationConfig>) => void
  toggleTeamPanel: () => void
  toggleNotificationPanel: () => void
  
  // Actions - Team Management
  assignTeamMember: (phase: ProjectPhase, memberId: string) => void
  removeTeamMember: (phase: ProjectPhase, memberId: string) => void
  
  // Actions - Notifications
  markNotificationRead: (notificationId: string) => void
  markAllNotificationsRead: () => void
  
  // Actions - Project Management
  refreshProject: () => void
  exportProjectData: () => void
}

export function useProjectNavigation(
  options: UseProjectNavigationOptions
): UseProjectNavigationReturn {
  const {
    projectId,
    enableRealTime = true,
    refetchInterval = 30000,
    enableNotifications = true,
    maxTeamMembers = 8,
  } = options

  const router = useRouter()
  
  // Zustand store state and actions
  const store = useProjectNavigationStore()
  const selectors = useProjectNavigationSelectors()
  
  // Server state queries
  const projectQuery = useProjectDetail(projectId, {
    refetchInterval,
    enabled: !!projectId,
  })
  
  const teamQuery = useProjectTeam(projectId, {
    refetchInterval: enableRealTime ? 60000 : 300000,
    enabled: !!projectId,
  })
  
  const progressQuery = useProjectProgress(projectId, {
    refetchInterval: enableRealTime ? 30000 : 120000,
    enabled: !!projectId,
  })
  
  const notificationsQuery = useProjectNotifications(projectId, {
    refetchInterval: 15000,
    enabled: enableNotifications && !!projectId,
  })
  
  const activityLogQuery = useProjectActivityLog(projectId, {
    refetchInterval: 60000,
    enabled: !!projectId,
  })
  
  // Mutations
  const updatePhaseMutation = useUpdateProjectPhase()
  
  // Generate navigation items based on project phases
  const navigationItems = useMemo(() => {
    if (!projectQuery.data || !progressQuery.data) return []
    
    const project = projectQuery.data
    const progress = progressQuery.data
    
    return project.progress.phases?.map((phaseInfo, index) => ({
      id: `phase-${phaseInfo.phase}`,
      label: phaseInfo.label,
      icon: phaseInfo.icon,
      href: `/projects/${projectId}/phases/${phaseInfo.phase}`,
      phase: phaseInfo.phase,
      isActive: store.activeItemId === `phase-${phaseInfo.phase}`,
      isCompleted: phaseInfo.progress === 100,
      isDisabled: index > 0 && !project.progress.phases[index - 1]?.isCompleted,
      hasIssues: phaseInfo.issues?.length > 0,
      issueCount: phaseInfo.issues?.length || 0,
      progress: phaseInfo.progress,
      children: phaseInfo.subPhases?.map(subPhase => ({
        id: `subphase-${subPhase.id}`,
        label: subPhase.label,
        icon: subPhase.icon,
        href: `/projects/${projectId}/phases/${phaseInfo.phase}/${subPhase.id}`,
        isActive: store.activeItemId === `subphase-${subPhase.id}`,
        isCompleted: subPhase.isCompleted,
        progress: subPhase.progress,
      })) || [],
    })) || []
  }, [projectQuery.data, progressQuery.data, projectId, store.activeItemId])
  
  // Generate breadcrumbs
  const breadcrumbs = useMemo(() => {
    if (!store.activeItemId || !navigationItems.length) return []
    
    const activeItem = navigationItems.find(item => 
      item.id === store.activeItemId || 
      item.children?.some(child => child.id === store.activeItemId)
    )
    
    if (!activeItem) return []
    
    const breadcrumbs: NavigationItem[] = [
      {
        id: "project-root",
        label: projectQuery.data?.name || "Project",
        icon: "FolderOpen" as any,
        href: `/projects/${projectId}`,
        isActive: false,
        isCompleted: false,
      },
      activeItem,
    ]
    
    // Add child if active item is a sub-phase
    const activeChild = activeItem.children?.find(child => child.id === store.activeItemId)
    if (activeChild) {
      breadcrumbs.push(activeChild)
    }
    
    return breadcrumbs
  }, [store.activeItemId, navigationItems, projectQuery.data?.name, projectId])
  
  // Current phase detection
  const currentPhase = useMemo(() => {
    if (!progressQuery.data?.phase) return null
    return progressQuery.data.phase
  }, [progressQuery.data?.phase])
  
  // Handle navigation notifications
  useEffect(() => {
    if (!enableNotifications || !notificationsQuery.data?.notifications) return
    
    const newNotifications = notificationsQuery.data.notifications.filter(
      notification => 
        !notification.isRead &&
        !store.readNotifications.includes(notification.id) &&
        notification.priority === "high"
    )
    
    newNotifications.forEach(notification => {
      toast.info(notification.title, {
        description: notification.message,
        duration: 8000,
        action: {
          label: "View",
          onClick: () => navigateToItem({ 
            id: notification.relatedItemId,
            label: notification.title,
            icon: "Bell" as any,
            href: notification.actionUrl,
            isActive: false,
            isCompleted: false,
          }),
        },
      })
    })
  }, [notificationsQuery.data?.notifications, enableNotifications, store])
  
  // Actions
  const navigateToItem = useCallback((item: NavigationItem) => {
    store.setActiveItem(item.id)
    router.push(item.href)
  }, [store, router])
  
  const navigateToPhase = useCallback((phase: ProjectPhase) => {
    const phaseItem = navigationItems.find(item => item.phase === phase)
    if (phaseItem) {
      navigateToItem(phaseItem)
    }
  }, [navigationItems, navigateToItem])
  
  const setActiveItem = useCallback((itemId: string) => {
    store.setActiveItem(itemId)
  }, [store])
  
  const goBack = useCallback(() => {
    router.back()
  }, [router])
  
  const goForward = useCallback(() => {
    router.forward()
  }, [router])
  
  const markPhaseComplete = useCallback(
    async (phase: ProjectPhase) => {
      if (!projectId) return
      
      try {
        await updatePhaseMutation.mutateAsync({
          projectId,
          phase,
          status: "completed",
          progress: 100,
        })
        
        toast.success(`Phase "${phase}" marked as complete`)
      } catch (error) {
        console.error("Failed to mark phase complete:", error)
        toast.error("Failed to update phase status")
      }
    },
    [projectId, updatePhaseMutation]
  )
  
  const updatePhaseProgress = useCallback(
    async (phase: ProjectPhase, progress: number) => {
      if (!projectId) return
      
      try {
        await updatePhaseMutation.mutateAsync({
          projectId,
          phase,
          progress,
        })
      } catch (error) {
        console.error("Failed to update phase progress:", error)
      }
    },
    [projectId, updatePhaseMutation]
  )
  
  const moveToNextPhase = useCallback(() => {
    // Implementation for moving to next phase
    // This would typically involve validation and business logic
  }, [])
  
  const moveToPreviousPhase = useCallback(() => {
    // Implementation for moving to previous phase
  }, [])
  
  const updateConfig = useCallback(
    (configUpdate: Partial<NavigationConfig>) => {
      store.setConfig(configUpdate)
    },
    [store]
  )
  
  const toggleTeamPanel = useCallback(() => {
    store.setTeamPanelOpen(!store.isTeamPanelOpen)
  }, [store])
  
  const toggleNotificationPanel = useCallback(() => {
    store.setNotificationPanelOpen(!store.isNotificationPanelOpen)
  }, [store])
  
  const assignTeamMember = useCallback(
    (phase: ProjectPhase, memberId: string) => {
      // Implementation for team assignment
    },
    []
  )
  
  const removeTeamMember = useCallback(
    (phase: ProjectPhase, memberId: string) => {
      // Implementation for team member removal
    },
    []
  )
  
  const markNotificationRead = useCallback(
    (notificationId: string) => {
      store.markNotificationRead(notificationId)
    },
    [store]
  )
  
  const markAllNotificationsRead = useCallback(() => {
    store.markAllNotificationsRead()
  }, [store])
  
  const refreshProject = useCallback(() => {
    Promise.all([
      projectQuery.refetch(),
      teamQuery.refetch(),
      progressQuery.refetch(),
      notificationsQuery.refetch(),
    ])
  }, [projectQuery, teamQuery, progressQuery, notificationsQuery])
  
  const exportProjectData = useCallback(() => {
    // Implementation for project data export
    toast.info("Project export feature coming soon")
  }, [])
  
  // Computed values
  const loading = projectQuery.isLoading || progressQuery.isLoading
  const error = projectQuery.error?.message || progressQuery.error?.message || null
  
  return {
    // Data
    project: projectQuery.data || null,
    team: teamQuery.data?.team || [],
    progress: progressQuery.data || null,
    notifications: notificationsQuery.data?.notifications || [],
    navigationItems,
    
    // State
    loading,
    error,
    isUpdating: updatePhaseMutation.isPending,
    currentPhase,
    activeItemId: store.activeItemId,
    
    // UI State
    config: store.config,
    isTeamPanelOpen: store.isTeamPanelOpen,
    isNotificationPanelOpen: store.isNotificationPanelOpen,
    breadcrumbs,
    
    // Computed State
    completedPhases: selectors.getCompletedPhases(),
    currentPhaseProgress: selectors.getCurrentPhaseProgress(),
    overallProgress: progressQuery.data?.overallProgress || 0,
    hasUnreadNotifications: selectors.hasUnreadNotifications(),
    activeTeamMembers: teamQuery.data?.team?.filter(member => member.isActive).slice(0, maxTeamMembers) || [],
    
    // Actions
    navigateToItem,
    navigateToPhase,
    setActiveItem,
    goBack,
    goForward,
    markPhaseComplete,
    updatePhaseProgress,
    moveToNextPhase,
    moveToPreviousPhase,
    updateConfig,
    toggleTeamPanel,
    toggleNotificationPanel,
    assignTeamMember,
    removeTeamMember,
    markNotificationRead,
    markAllNotificationsRead,
    refreshProject,
    exportProjectData,
  }
}