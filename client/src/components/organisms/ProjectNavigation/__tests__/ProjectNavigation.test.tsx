/**
 * Project Navigation Organism Tests
 * 
 * Comprehensive test suite for the ProjectNavigation organism component,
 * ensuring professional electrical engineering navigation functionality.
 * 
 * Test Coverage:
 * - Component rendering and props handling
 * - Navigation item interactions
 * - Phase management functionality
 * - Team panel integration
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Error boundary behavior
 * - Loading and error states
 * - TypeScript strict mode compliance
 */

import React from "react"
import { render, screen, fireEvent, waitFor, within } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { vi, describe, it, expect, beforeEach, afterEach } from "vitest"

import { ProjectNavigation } from "../ProjectNavigation"
import type { ProjectNavigationProps, ProjectInfo, TeamMember } from "../ProjectNavigationTypes"

// Mock the navigation hook
const mockNavigationHook = {
  project: null,
  team: [],
  progress: null,
  notifications: [],
  navigationItems: [],
  loading: false,
  error: null,
  isUpdating: false,
  currentPhase: null,
  activeItemId: null,
  config: {
    showProgress: true,
    showTeamMembers: true,
    showNotifications: true,
    compactMode: false,
    groupByPhase: true,
    showCompletedPhases: true,
    autoCollapse: false,
  },
  isTeamPanelOpen: false,
  isNotificationPanelOpen: false,
  breadcrumbs: [],
  completedPhases: [],
  currentPhaseProgress: 0,
  overallProgress: 0,
  hasUnreadNotifications: false,
  activeTeamMembers: [],
  navigateToItem: vi.fn(),
  navigateToPhase: vi.fn(),
  setActiveItem: vi.fn(),
  goBack: vi.fn(),
  goForward: vi.fn(),
  markPhaseComplete: vi.fn(),
  updatePhaseProgress: vi.fn(),
  moveToNextPhase: vi.fn(),
  moveToPreviousPhase: vi.fn(),
  updateConfig: vi.fn(),
  toggleTeamPanel: vi.fn(),
  toggleNotificationPanel: vi.fn(),
  assignTeamMember: vi.fn(),
  removeTeamMember: vi.fn(),
  markNotificationRead: vi.fn(),
  markAllNotificationsRead: vi.fn(),
  refreshProject: vi.fn(),
  exportProjectData: vi.fn(),
}

vi.mock("../useProjectNavigation", () => ({
  useProjectNavigation: () => mockNavigationHook,
}))

vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  }),
}))

vi.mock("sonner", () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
  },
}))

// Test data
const mockProject: ProjectInfo = {
  id: "proj-1",
  name: "Electrical Distribution Upgrade",
  code: "EDU-2024-001",
  description: "Main electrical distribution panel upgrade for manufacturing facility",
  client: "Industrial Manufacturing Corp",
  location: "Building A, Floor 2",
  status: "design",
  priority: "high",
  startDate: new Date("2024-01-15"),
  endDate: new Date("2024-06-30"),
  estimatedEndDate: new Date("2024-06-15"),
  budget: 250000,
  budgetSpent: 125000,
  applicableStandards: ["IEC-60364", "EN-50110", "IEEE-80"],
  projectManager: {
    id: "user-1",
    name: "John Smith",
    role: "project_manager",
    email: "<EMAIL>",
    isOnline: true,
    isActive: true,
  } as TeamMember,
  leadEngineer: {
    id: "user-2", 
    name: "Sarah Johnson",
    role: "lead_engineer",
    email: "<EMAIL>",
    isOnline: true,
    isActive: true,
  } as TeamMember,
  team: [],
  progress: {
    phase: "detailed_design",
    phaseProgress: 65,
    overallProgress: 35,
    completedTasks: 12,
    totalTasks: 34,
    nextMilestone: "Design Review",
    nextMilestoneDate: new Date("2024-03-15"),
    estimatedCompletion: new Date("2024-06-15"),
    health: "normal" as const,
    issues: [],
  },
  lastActivity: new Date("2024-02-10"),
  isTemplate: false,
}

const mockNavigationItems = [
  {
    id: "phase-initial_consultation",
    label: "Initial Consultation", 
    icon: "MessageSquare" as any,
    href: "/projects/proj-1/phases/initial_consultation",
    phase: "initial_consultation" as const,
    isActive: false,
    isCompleted: true,
    progress: 100,
  },
  {
    id: "phase-detailed_design",
    label: "Detailed Design",
    icon: "Zap" as any,
    href: "/projects/proj-1/phases/detailed_design",
    phase: "detailed_design" as const,
    isActive: true,
    isCompleted: false,
    progress: 65,
    hasIssues: true,
    issueCount: 2,
  },
  {
    id: "phase-standards_compliance",
    label: "Standards Compliance",
    icon: "Shield" as any,
    href: "/projects/proj-1/phases/standards_compliance",
    phase: "standards_compliance" as const,
    isActive: false,
    isCompleted: false,
    isDisabled: true,
    progress: 0,
  },
]

const mockTeamMembers: TeamMember[] = [
  {
    id: "user-1",
    name: "John Smith",
    role: "project_manager",
    email: "<EMAIL>",
    avatar: "/avatars/john.jpg",
    isOnline: true,
    isActive: true,
    currentPhase: "detailed_design",
    workload: 75,
  },
  {
    id: "user-2",
    name: "Sarah Johnson", 
    role: "lead_engineer",
    email: "<EMAIL>",
    avatar: "/avatars/sarah.jpg",
    isOnline: true,
    isActive: true,
    currentPhase: "detailed_design",
    workload: 90,
  },
  {
    id: "user-3",
    name: "Mike Wilson",
    role: "design_engineer",
    email: "<EMAIL>", 
    isOnline: false,
    isActive: true,
    workload: 45,
  },
]

const defaultProps: Partial<ProjectNavigationProps> = {
  "data-testid": "project-navigation",
}

const renderProjectNavigation = (props: Partial<ProjectNavigationProps> = {}) => {
  const mergedProps = { ...defaultProps, ...props } as ProjectNavigationProps
  return render(<ProjectNavigation {...mergedProps} />)
}

describe("ProjectNavigation", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset mock hook data
    Object.assign(mockNavigationHook, {
      project: mockProject,
      team: mockTeamMembers,
      navigationItems: mockNavigationItems,
      activeTeamMembers: mockTeamMembers.slice(0, 2),
      breadcrumbs: [
        { id: "project-root", label: "Project", icon: "FolderOpen" as any, href: "/projects/proj-1", isActive: false, isCompleted: false },
        mockNavigationItems[1], // Active detailed_design phase
      ],
      overallProgress: 35,
    })
  })

  afterEach(() => {
    vi.resetAllMocks()
  })

  describe("Rendering", () => {
    it("renders project navigation with external project data", () => {
      renderProjectNavigation({ project: mockProject })

      expect(screen.getByTestId("project-navigation")).toBeInTheDocument()
      expect(screen.getByText("Electrical Distribution Upgrade")).toBeInTheDocument()
      expect(screen.getByText("EDU-2024-001 • Industrial Manufacturing Corp")).toBeInTheDocument()
    })

    it("renders with hook data when no external project provided", () => {
      renderProjectNavigation()

      expect(screen.getByTestId("project-navigation")).toBeInTheDocument()
      expect(screen.getByText("Electrical Distribution Upgrade")).toBeInTheDocument()
    })

    it("renders loading state correctly", () => {
      Object.assign(mockNavigationHook, { 
        project: null, 
        loading: true, 
        navigationItems: [] 
      })

      renderProjectNavigation()

      expect(screen.getByTestId("project-navigation")).toBeInTheDocument()
      // Loading skeleton should be present
      const loadingSkeleton = screen.container.querySelector(".animate-pulse")
      expect(loadingSkeleton).toBeInTheDocument()
    })

    it("renders error state correctly", () => {
      Object.assign(mockNavigationHook, { 
        project: null, 
        loading: false, 
        error: "Failed to load project",
        navigationItems: [] 
      })

      renderProjectNavigation()

      expect(screen.getByText("Navigation Error")).toBeInTheDocument()
      expect(screen.getByText("Failed to load project")).toBeInTheDocument()
      expect(screen.getByText("Try Again")).toBeInTheDocument()
    })

    it("renders empty state when no project data", () => {
      Object.assign(mockNavigationHook, { 
        project: null, 
        loading: false, 
        error: null,
        navigationItems: [] 
      })

      renderProjectNavigation()

      expect(screen.getByText("No project data available")).toBeInTheDocument()
    })
  })

  describe("Project Header", () => {
    it("displays project information correctly", () => {
      renderProjectNavigation()

      expect(screen.getByText("Electrical Distribution Upgrade")).toBeInTheDocument()
      expect(screen.getByText("EDU-2024-001 • Industrial Manufacturing Corp")).toBeInTheDocument()
      expect(screen.getByText("Building A, Floor 2")).toBeInTheDocument()
      expect(screen.getByText(/Due.*2024/)).toBeInTheDocument()
      expect(screen.getByText(/IEC-60364, EN-50110/)).toBeInTheDocument()
    })

    it("displays project status and priority badges", () => {
      renderProjectNavigation()

      expect(screen.getByText("design")).toBeInTheDocument()
      expect(screen.getByText("high")).toBeInTheDocument()
    })

    it("shows progress bar when enabled", () => {
      renderProjectNavigation()

      expect(screen.getByText("Overall Progress")).toBeInTheDocument()
      expect(screen.getByText("35%")).toBeInTheDocument()
      
      const progressBar = screen.container.querySelector('[role="progressbar"]')
      expect(progressBar).toBeInTheDocument()
    })

    it("shows team count when team members are present", () => {
      renderProjectNavigation()

      const teamButton = screen.getByRole("button", { name: /2/ })
      expect(teamButton).toBeInTheDocument()
      
      const usersIcon = within(teamButton).getByTestId("lucide-users") || teamButton.querySelector('svg')
      expect(usersIcon).toBeInTheDocument()
    })

    it("calls settings callback when settings button clicked", async () => {
      const onSettingsClick = vi.fn()
      renderProjectNavigation({ onProjectSettings: onSettingsClick })

      const settingsButton = screen.getByRole("button", { name: /settings/i })
      await userEvent.click(settingsButton)

      expect(onSettingsClick).toHaveBeenCalledOnce()
    })
  })

  describe("Navigation Items", () => {
    it("renders navigation items correctly", () => {
      renderProjectNavigation()

      expect(screen.getByText("Initial Consultation")).toBeInTheDocument()
      expect(screen.getByText("Detailed Design")).toBeInTheDocument()
      expect(screen.getByText("Standards Compliance")).toBeInTheDocument()
    })

    it("shows completed status for finished phases", () => {
      renderProjectNavigation()

      const initialConsultation = screen.getByText("Initial Consultation").closest("div")
      const checkIcon = within(initialConsultation!).getByTestId("lucide-check-circle") || 
                       initialConsultation!.querySelector('[data-testid*="check"]')
      expect(checkIcon).toBeInTheDocument()
    })

    it("shows active state styling for current phase", () => {
      renderProjectNavigation()

      const detailedDesign = screen.getByText("Detailed Design").closest("div")
      expect(detailedDesign).toHaveClass("bg-primary/10", "text-primary")
    })

    it("shows issue badges for phases with problems", () => {
      renderProjectNavigation()

      const issueCountBadge = screen.getByText("2")
      expect(issueCountBadge).toBeInTheDocument()
      expect(issueCountBadge.closest(".bg-destructive, .border-destructive")).toBeInTheDocument()
    })

    it("shows progress indicators for in-progress phases", () => {
      renderProjectNavigation()

      expect(screen.getByText("65%")).toBeInTheDocument()
    })

    it("disables future phases correctly", () => {
      renderProjectNavigation()

      const standardsCompliance = screen.getByText("Standards Compliance").closest("div")
      expect(standardsCompliance).toHaveClass("opacity-50", "cursor-not-allowed")
    })

    it("handles navigation item clicks", async () => {
      const onItemClick = vi.fn()
      renderProjectNavigation({ onItemClick })

      const detailedDesignItem = screen.getByText("Detailed Design")
      await userEvent.click(detailedDesignItem)

      expect(onItemClick).toHaveBeenCalledWith(
        expect.objectContaining({
          id: "phase-detailed_design",
          label: "Detailed Design",
        })
      )
    })

    it("does not handle clicks on disabled items", async () => {
      const onItemClick = vi.fn()
      renderProjectNavigation({ onItemClick })

      const disabledItem = screen.getByText("Standards Compliance")
      await userEvent.click(disabledItem)

      expect(onItemClick).not.toHaveBeenCalled()
    })
  })

  describe("Breadcrumbs", () => {
    it("renders breadcrumb navigation correctly", () => {
      renderProjectNavigation()

      expect(screen.getByText("Project")).toBeInTheDocument()
      expect(screen.getByText("Detailed Design")).toBeInTheDocument()
      
      const breadcrumbSeparators = screen.container.querySelectorAll('[data-testid*="chevron-right"]')
      expect(breadcrumbSeparators.length).toBeGreaterThan(0)
    })

    it("handles breadcrumb item clicks", async () => {
      const onItemClick = vi.fn()
      renderProjectNavigation({ onItemClick })

      const projectBreadcrumb = screen.getByText("Project")
      await userEvent.click(projectBreadcrumb)

      expect(onItemClick).toHaveBeenCalledWith(
        expect.objectContaining({
          id: "project-root",
          label: "Project",
        })
      )
    })
  })

  describe("Team Panel", () => {
    beforeEach(() => {
      Object.assign(mockNavigationHook, {
        isTeamPanelOpen: true,
      })
    })

    it("displays team panel when opened", () => {
      renderProjectNavigation()

      expect(screen.getByText("Project Team")).toBeInTheDocument()
      expect(screen.getByText("2/3")).toBeInTheDocument() // Active members count
    })

    it("renders team member information correctly", () => {
      renderProjectNavigation()

      expect(screen.getByText("John Smith")).toBeInTheDocument()
      expect(screen.getByText("Sarah Johnson")).toBeInTheDocument()
      expect(screen.getByText("project manager")).toBeInTheDocument()
      expect(screen.getByText("lead engineer")).toBeInTheDocument()
    })

    it("shows workload indicators for team members", () => {
      renderProjectNavigation()

      expect(screen.getByText("75%")).toBeInTheDocument() // John's workload
      expect(screen.getByText("90%")).toBeInTheDocument() // Sarah's workload
    })

    it("shows online status for team members", () => {
      renderProjectNavigation()

      const onlineIndicators = screen.container.querySelectorAll('[data-testid*="status-indicator"]')
      expect(onlineIndicators.length).toBeGreaterThanOrEqual(2) // At least 2 online members
    })

    it("handles team member clicks", async () => {
      const onTeamMemberClick = vi.fn()
      renderProjectNavigation({ onTeamMemberClick })

      const johnSmith = screen.getByText("John Smith")
      await userEvent.click(johnSmith)

      expect(onTeamMemberClick).toHaveBeenCalledWith(
        expect.objectContaining({
          id: "user-1",
          name: "John Smith",
          role: "project_manager",
        })
      )
    })

    it("shows 'View All' button when there are many team members", () => {
      Object.assign(mockNavigationHook, {
        team: [...mockTeamMembers, ...Array.from({ length: 5 }, (_, i) => ({
          ...mockTeamMembers[0],
          id: `user-${i + 4}`,
          name: `Team Member ${i + 4}`,
        }))],
      })

      renderProjectNavigation()

      expect(screen.getByText(/View All \(8\)/)).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA labels and roles", () => {
      renderProjectNavigation()

      const nav = screen.getByRole("navigation")
      expect(nav).toBeInTheDocument()

      const buttons = screen.getAllByRole("button")
      expect(buttons.length).toBeGreaterThan(0)
      
      buttons.forEach(button => {
        expect(button).toBeVisible()
      })
    })

    it("supports keyboard navigation", async () => {
      renderProjectNavigation()

      const firstButton = screen.getAllByRole("button")[0]
      firstButton.focus()
      expect(firstButton).toHaveFocus()

      await userEvent.keyboard("{Tab}")
      const focusedElement = document.activeElement
      expect(focusedElement).toBeInstanceOf(HTMLElement)
      expect(focusedElement).not.toBe(firstButton)
    })

    it("has proper color contrast for status indicators", () => {
      renderProjectNavigation()

      const statusElements = screen.container.querySelectorAll('[class*="text-green"], [class*="text-red"], [class*="text-amber"]')
      statusElements.forEach(element => {
        expect(element).toBeVisible()
      })
    })

    it("provides alternative text for icons", () => {
      renderProjectNavigation()

      // Check that important interactive elements have accessible names
      const settingsButton = screen.getByRole("button", { name: /settings/i })
      expect(settingsButton).toBeInTheDocument()
    })
  })

  describe("Error Handling", () => {
    it("displays error boundary fallback on component errors", () => {
      // Mock console.error to prevent test noise
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})
      
      // Force an error by passing invalid props that would cause the component to throw
      const ThrowError = () => {
        throw new Error("Test error")
      }
      
      expect(() => render(<ThrowError />)).toThrow()
      
      consoleSpy.mockRestore()
    })

    it("handles missing project data gracefully", () => {
      Object.assign(mockNavigationHook, { project: null })
      renderProjectNavigation()

      expect(screen.getByText("No project data available")).toBeInTheDocument()
    })

    it("handles network errors appropriately", () => {
      Object.assign(mockNavigationHook, { error: "Network connection failed" })
      renderProjectNavigation({ project: undefined })

      expect(screen.getByText("Navigation Error")).toBeInTheDocument()
      expect(screen.getByText("Network connection failed")).toBeInTheDocument()
    })
  })

  describe("Performance", () => {
    it("renders efficiently with large navigation lists", () => {
      const largeNavigationItems = Array.from({ length: 50 }, (_, i) => ({
        id: `phase-${i}`,
        label: `Phase ${i}`,
        icon: "FileText" as any,
        href: `/projects/proj-1/phases/phase-${i}`,
        isActive: false,
        isCompleted: false,
        progress: Math.random() * 100,
      }))

      Object.assign(mockNavigationHook, { navigationItems: largeNavigationItems })

      const startTime = performance.now()
      renderProjectNavigation()
      const endTime = performance.now()

      // Should render in reasonable time (less than 100ms for 50 items)
      expect(endTime - startTime).toBeLessThan(100)
      
      expect(screen.getByText("Phase 0")).toBeInTheDocument()
      expect(screen.getByText("Phase 49")).toBeInTheDocument()
    })
  })

  describe("Integration", () => {
    it("integrates with external callbacks correctly", async () => {
      const callbacks = {
        onItemClick: vi.fn(),
        onPhaseChange: vi.fn(),
        onTeamMemberClick: vi.fn(),
        onProjectSettings: vi.fn(),
      }

      renderProjectNavigation(callbacks)

      // Test navigation item callback
      await userEvent.click(screen.getByText("Detailed Design"))
      expect(callbacks.onItemClick).toHaveBeenCalled()

      // Test settings callback
      await userEvent.click(screen.getByRole("button", { name: /settings/i }))
      expect(callbacks.onProjectSettings).toHaveBeenCalled()
    })

    it("handles configuration changes properly", () => {
      const customConfig = {
        showProgress: false,
        showTeamMembers: false,
        compactMode: true,
      }

      renderProjectNavigation({ config: customConfig })

      // Progress should be hidden
      expect(screen.queryByText("Overall Progress")).not.toBeInTheDocument()
      
      // Team button should be hidden
      const teamButtons = screen.queryAllByRole("button", { name: /\d+/ })
      expect(teamButtons.length).toBe(0)
    })
  })
})