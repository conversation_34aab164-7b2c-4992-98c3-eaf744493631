"""Project Phase and Milestone Database Models.

SQLAlchemy models for project lifecycle management following IEEE electrical
project management standards with real-time collaboration support.

Key models:
- ProjectPhase: IEEE/IEC electrical project phases (Planning, Design, Implementation, Testing, Commissioning)
- ProjectMilestone: Key deliverable tracking with dependencies
- ProjectTemplate: Standardized project templates for different electrical system types
"""

import datetime
from typing import TYPE_CHECKING, List, Optional
import uuid

from sqlalchemy import Boolean, DateTime, ForeignKey, String, Text, UniqueConstraint, Integer, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.dialects.postgresql import UUID

from src.core.models.base import Base, CommonColumns, SoftDeleteColumns, EnumType
from src.core.enums.project_management_enums import ProjectPhaseType, MilestoneStatus, ProjectTemplateType
from src.core.utils.datetime_utils import utcnow_naive

if TYPE_CHECKING:
    from .project import Project
    from .user import User


class ProjectPhase(CommonColumns, SoftDeleteColumns, Base):
    """Project phase model representing IEEE electrical project lifecycle phases.
    
    Supports electrical engineering project management with phases like:
    - Conceptual Design
    - Schematic Design  
    - Design Development
    - Construction Documents
    - Bidding/Negotiation
    - Construction Administration
    - Testing & Commissioning
    
    Attributes:
        phase_id: Unique UUID identifier for the phase
        project_id: Foreign key to the associated project
        phase_type: Type of project phase (planning, design, implementation, etc.)
        start_date: Phase start date
        end_date: Phase end date (optional)
        progress_percentage: Current completion percentage (0-100)
        is_active: Whether this phase is currently active
        prerequisites: JSON array of prerequisite phase IDs
        deliverables: JSON array of expected deliverables
        project: Related project entity
        milestones: List of milestones within this phase
    """
    
    __tablename__ = "project_phases"
    
    # Unique phase identifier (UUID)
    phase_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the phase"
    )
    
    # Project relationship
    project_id: Mapped[int] = mapped_column(
        ForeignKey("projects.id"), nullable=False, index=True,
        comment="Foreign key to the associated project"
    )
    
    # Phase details
    phase_type: Mapped[ProjectPhaseType] = mapped_column(
        EnumType(ProjectPhaseType), nullable=False, index=True,
        comment="Type of project phase"
    )
    
    start_date: Mapped[datetime.datetime] = mapped_column(
        DateTime, nullable=False, index=True,
        comment="Phase start date"
    )
    
    end_date: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True, index=True,
        comment="Phase end date"
    )
    
    progress_percentage: Mapped[int] = mapped_column(
        Integer, default=0, nullable=False,
        comment="Current completion percentage (0-100)"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False, index=True,
        comment="Whether this phase is currently active"
    )
    
    # Phase configuration
    prerequisites: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True,
        comment="JSON array of prerequisite phase IDs"
    )
    
    deliverables: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True,
        comment="JSON array of expected deliverables"
    )
    
    notes: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True,
        comment="Phase notes and comments"
    )
    
    # Relationships
    project: Mapped["Project"] = relationship(
        "Project", back_populates="phases", foreign_keys=[project_id]
    )
    
    milestones: Mapped[List["ProjectMilestone"]] = relationship(
        "ProjectMilestone", back_populates="phase", cascade="all, delete-orphan"
    )
    
    __table_args__ = (
        UniqueConstraint("phase_id", name="uq_project_phase_id"),
        UniqueConstraint("project_id", "phase_type", name="uq_project_phase_type"),
    )
    
    def __repr__(self) -> str:
        return f"<ProjectPhase(phase_id={self.phase_id}, type={self.phase_type.value if self.phase_type else 'N/A'}, progress={self.progress_percentage}%)>"


class ProjectMilestone(CommonColumns, SoftDeleteColumns, Base):
    """Project milestone model for tracking key deliverables and checkpoints.
    
    Represents important project checkpoints like:
    - Design Review Approval
    - Equipment Procurement Complete
    - Installation Complete
    - Testing Complete
    - Commissioning Complete
    - Final Acceptance
    
    Attributes:
        milestone_id: Unique UUID identifier for the milestone
        phase_id: Foreign key to the associated project phase
        title: Milestone title
        description: Detailed milestone description
        due_date: Milestone due date
        completion_date: Actual completion date (optional)
        status: Milestone status (not_started, in_progress, completed, etc.)
        assigned_user_id: User responsible for milestone completion
        dependencies: JSON array of dependent milestone IDs
        completion_criteria: JSON array of completion criteria
        phase: Related project phase
        assigned_user: User responsible for completion
    """
    
    __tablename__ = "project_milestones"
    
    # Unique milestone identifier (UUID)
    milestone_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the milestone"
    )
    
    # Phase relationship
    phase_id: Mapped[int] = mapped_column(
        ForeignKey("project_phases.id"), nullable=False, index=True,
        comment="Foreign key to the associated project phase"
    )
    
    # Milestone details
    title: Mapped[str] = mapped_column(
        String(255), nullable=False,
        comment="Milestone title"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True,
        comment="Detailed milestone description"
    )
    
    due_date: Mapped[datetime.datetime] = mapped_column(
        DateTime, nullable=False, index=True,
        comment="Milestone due date"
    )
    
    completion_date: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True, index=True,
        comment="Actual completion date"
    )
    
    status: Mapped[MilestoneStatus] = mapped_column(
        EnumType(MilestoneStatus), nullable=False,
        default=MilestoneStatus.NOT_STARTED, index=True,
        comment="Milestone status"
    )
    
    # Assignment
    assigned_user_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id"), nullable=True, index=True,
        comment="User responsible for milestone completion"
    )
    
    # Milestone configuration
    dependencies: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True,
        comment="JSON array of dependent milestone IDs"
    )
    
    completion_criteria: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True,
        comment="JSON array of completion criteria"
    )
    
    # Relationships
    phase: Mapped["ProjectPhase"] = relationship(
        "ProjectPhase", back_populates="milestones", foreign_keys=[phase_id]
    )
    
    assigned_user: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[assigned_user_id]
    )
    
    __table_args__ = (
        UniqueConstraint("milestone_id", name="uq_project_milestone_id"),
    )
    
    def __repr__(self) -> str:
        return f"<ProjectMilestone(milestone_id={self.milestone_id}, title='{self.title}', status={self.status.value if self.status else 'N/A'})>"


class ProjectTemplate(CommonColumns, SoftDeleteColumns, Base):
    """Project template model for standardized electrical project types.
    
    Provides template definitions for different types of electrical projects:
    - Industrial Power Distribution
    - Motor Control Systems
    - Lighting Systems
    - UPS and Backup Power
    - Heat Tracing Systems
    - Building Electrical Systems
    
    Attributes:
        template_id: Unique UUID identifier for the template
        template_type: Type of electrical project template
        category: Template category (industrial, commercial, residential)
        phases_config: JSON configuration of default phases
        milestones_config: JSON configuration of default milestones
        default_settings: JSON of default project settings
        compliance_standards: JSON array of applicable standards
        is_public: Whether template is available to all users
        created_by_user_id: User who created the template
        created_by_user: User who created the template
    """
    
    __tablename__ = "project_templates"
    
    # Unique template identifier (UUID)
    template_id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        default=lambda: str(uuid.uuid4()),
        unique=True,
        nullable=False,
        index=True,
        comment="Unique UUID identifier for the template"
    )
    
    # Template details
    template_type: Mapped[ProjectTemplateType] = mapped_column(
        EnumType(ProjectTemplateType), nullable=False, index=True,
        comment="Type of electrical project template"
    )
    
    category: Mapped[str] = mapped_column(
        String(100), nullable=False, index=True,
        comment="Template category (industrial, commercial, residential)"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True,
        comment="Template description"
    )
    
    # Template configuration
    phases_config: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True,
        comment="JSON configuration of default phases"
    )
    
    milestones_config: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True,
        comment="JSON configuration of default milestones"
    )
    
    default_settings: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True,
        comment="JSON of default project settings"
    )
    
    compliance_standards: Mapped[Optional[str]] = mapped_column(
        JSON, nullable=True,
        comment="JSON array of applicable standards (IEC, IEEE, EN)"
    )
    
    # Template sharing
    is_public: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False, index=True,
        comment="Whether template is available to all users"
    )
    
    created_by_user_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id"), nullable=True, index=True,
        comment="User who created the template"
    )
    
    # Relationships
    created_by_user: Mapped[Optional["User"]] = relationship(
        "User", foreign_keys=[created_by_user_id]
    )
    
    __table_args__ = (
        UniqueConstraint("template_id", name="uq_project_template_id"),
        UniqueConstraint("name", "created_by_user_id", name="uq_user_template_name"),
    )
    
    def __repr__(self) -> str:
        return f"<ProjectTemplate(template_id={self.template_id}, type={self.template_type.value if self.template_type else 'N/A'}, public={self.is_public})>"