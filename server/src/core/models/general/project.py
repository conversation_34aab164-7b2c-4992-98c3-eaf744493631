"""Project Database Model.

the SQLAlchemy model for the Project entity,
including validation and relationships with other entities.
"""

import datetime

# Define schema for voltage validation
import json
from locale import currency
from typing import TYPE_CHECKING, Any, List, Optional

from pydantic import BaseModel, Field, field_validator
from sqlalchemy import ForeignKey, Text, UniqueConstraint, event
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.config.logging_config import logger
from src.core.enums import InstallationEnvironment
from src.core.errors.exceptions import DataValidationError
from src.core.models.base import Base, CommonColumns, EnumType, SoftDeleteColumns
from src.core.models.general.user import User
from src.core.models.general.user_role import UserRole
from src.core.utils.datetime_utils import utcnow_aware, utcnow_naive
from src.core.utils.json_validation import FlexibleJSON

# Lazy import to avoid circular dependencies - handle_validation_errors imported at end of file


if TYPE_CHECKING:
    from .synchronization_log import SynchronizationLog
    from .task import Task
    from .user import User


class VoltagesSchema(BaseModel):
    """Schema for validating voltages JSON data."""

    voltages: List[float] = Field(..., description="List of available voltages")

    @field_validator("voltages")
    @classmethod
    def validate_voltages(cls, v: List[float]) -> List[float]:
        """Validate that all voltages are positive numbers."""
        for voltage in v:
            if voltage <= 0:
                raise ValueError("All voltages must be positive numbers")
        return v


class Project(CommonColumns, SoftDeleteColumns, Base):
    """Project model representing a heat tracing design project.

    This model stores project-level information including environmental parameters,
    design specifications, and default settings. It serves as the root entity
    for all project-related components (pipes, tanks, circuits, etc.).

    Attributes:
        project_number: Unique project identifier/code
        description: Optional project description
        designer: Name of the project designer
        min_ambient_temp_c: Minimum ambient temperature in Celsius
        max_ambient_temp_c: Maximum ambient temperature in Celsius
        desired_maintenance_temp_c: Target maintenance temperature in Celsius
        wind_speed_ms: Wind speed in meters per second (optional)
        installation_environment: Indoor/outdoor installation environment
        available_voltages_json: JSON string of available voltages
        default_cable_manufacturer: Default cable manufacturer name
        default_control_device_manufacturer: Default control device manufacturer

    Relationships:


    """

    __tablename__ = "projects"

    project_number: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Client and project management fields
    client: Mapped[Optional[str]] = mapped_column(nullable=True)
    location: Mapped[Optional[str]] = mapped_column(nullable=True)
    language: Mapped[Optional[str]] = mapped_column(nullable=True, default="en")
    status: Mapped[str] = mapped_column(nullable=False, default="active")
    is_offline: Mapped[bool] = mapped_column(default=False, nullable=False)
    database_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    currency: Mapped[Optional[str]] = mapped_column(nullable=True, default="EUR")
    wind_speed_ms: Mapped[Optional[float]] = mapped_column(nullable=True)
    installation_environment: Mapped[Optional[InstallationEnvironment]] = mapped_column(
        EnumType(InstallationEnvironment), nullable=True
    )
    available_voltages_json: Mapped[Optional[str]] = mapped_column(FlexibleJSON, nullable=True)

    default_voltage: Mapped[Optional[str]] = mapped_column(nullable=True, default="230V")
    default_frequency: Mapped[Optional[str]] = mapped_column(nullable=True, default="50Hz")
    default_temperature_unit: Mapped[Optional[str]] = mapped_column(nullable=True, default="°C")
    default_min_ambient_temp: Mapped[Optional[float]] = mapped_column(nullable=True)
    default_max_ambient_temp: Mapped[Optional[float]] = mapped_column(nullable=True)
    default_desired_maintenance_temp: Mapped[Optional[float]] = mapped_column(nullable=True)
    default_safety_margin_percent: Mapped[float] = mapped_column(default=0.0, nullable=False)

    preferred_power_cable_manufacturers_json: Mapped[Optional[str]] = mapped_column(FlexibleJSON, nullable=True)
    preferred_control_cable_manufacturers_json: Mapped[Optional[str]] = mapped_column(FlexibleJSON, nullable=True)

    # Relationships
    members: Mapped[List["ProjectMember"]] = relationship(
        "ProjectMember",
        back_populates="project",
        cascade="all, delete-orphan",
    )

    synchronization_logs: Mapped[List["SynchronizationLog"]] = relationship(
        "SynchronizationLog",
        back_populates="project",
        cascade="all, delete-orphan",
    )

    tasks: Mapped[List["Task"]] = relationship(
        "Task",
        back_populates="project",
        cascade="all, delete-orphan",
    )

    phases: Mapped[List["ProjectPhase"]] = relationship(
        "ProjectPhase",
        back_populates="project",
        cascade="all, delete-orphan",
    )

    __table_args__ = (
        UniqueConstraint("name", name="uq_project_name"),
        UniqueConstraint("project_number", name="uq_project_number"),
    )

    def __repr__(self) -> str:
        return f"<Project(id={self.id}, name={self.name}, project_number={self.project_number})>"


# --- ProjectMember Definition ---
class ProjectMember(CommonColumns, SoftDeleteColumns, Base):
    """
    Association model linking Users, Projects, and their specific UserRole within that project.
    """

    __tablename__ = "project_members"

    name: Mapped[str] = mapped_column(nullable=False)
    user_id: Mapped[int] = mapped_column(ForeignKey("users.id"), nullable=False)
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.id"), nullable=False)
    role_id: Mapped[int] = mapped_column(ForeignKey("user_roles.id"), nullable=False)  # Project-specific role

    is_active: Mapped[bool] = mapped_column(default=True, nullable=False)
    assigned_at: Mapped[datetime.datetime] = mapped_column(default=utcnow_naive, nullable=False)
    expires_at: Mapped[Optional[datetime.datetime]] = mapped_column(nullable=True)

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="project_memberships", foreign_keys=[user_id])
    project: Mapped["Project"] = relationship("Project", back_populates="members", foreign_keys=[project_id])
    role: Mapped["UserRole"] = relationship("UserRole", back_populates="project_assignments", foreign_keys=[role_id])

    __table_args__ = (UniqueConstraint("user_id", "project_id", "role_id", name="uq_project_user_role"),)

    def __repr__(self) -> str:
        return f"<ProjectMember(user_id={self.user_id}, project_id={self.project_id}, role={self.role.name if self.role else 'N/A'})>"


def validate_project_data(mapper: Any, connection: Any, target: Any) -> None:
    """Validate project data before insert/update.

    Args:
        mapper: SQLAlchemy mapper
        connection: Database connection
        target: Project instance being validated

    Raises:
        DataValidationError: If validation fails

    """
    errors = []

    # Validate temperature ranges (only if both values are not None)
    if (
        target.default_min_ambient_temp is not None
        and target.default_max_ambient_temp is not None
        and target.default_min_ambient_temp >= target.default_max_ambient_temp
    ):
        errors.append(
            {
                "loc": ["default_min_ambient_temp", "default_max_ambient_temp"],
                "msg": "Minimum ambient temperature must be less than maximum ambient temperature",
                "type": "value_error",
            }
        )

    # Validate maintenance temperature (only if both values are not None)
    if (
        target.default_desired_maintenance_temp is not None
        and target.default_max_ambient_temp is not None
        and target.default_desired_maintenance_temp <= target.default_max_ambient_temp
    ):
        errors.append(
            {
                "loc": ["default_desired_maintenance_temp"],
                "msg": "Desired maintenance temperature must be greater than maximum ambient temperature",
                "type": "value_error",
            }
        )

    # Validate temperature ranges are reasonable (only if values are not None)
    if target.default_min_ambient_temp is not None and (
        target.default_min_ambient_temp < -100 or target.default_min_ambient_temp > 100
    ):
        errors.append(
            {
                "loc": ["default_min_ambient_temp"],
                "msg": "Minimum ambient temperature must be between -100°C and 100°C",
                "type": "value_error",
            }
        )

    if target.default_max_ambient_temp is not None and (
        target.default_max_ambient_temp < -50 or target.default_max_ambient_temp > 150
    ):
        errors.append(
            {
                "loc": ["default_max_ambient_temp"],
                "msg": "Maximum ambient temperature must be between -50°C and 150°C",
                "type": "value_error",
            }
        )

    if target.default_desired_maintenance_temp is not None and (
        target.default_desired_maintenance_temp < 0 or target.default_desired_maintenance_temp > 500
    ):
        errors.append(
            {
                "loc": ["default_desired_maintenance_temp"],
                "msg": "Desired maintenance temperature must be between 0°C and 500°C",
                "type": "value_error",
            }
        )

    # Validate wind speed
    if target.wind_speed_ms is not None and target.wind_speed_ms < 0:
        errors.append(
            {
                "loc": ["wind_speed_ms"],
                "msg": "Wind speed cannot be negative",
                "type": "value_error",
            }
        )

    # Validate available voltages JSON
    if target.available_voltages_json:
        voltages_data = json.loads(target.available_voltages_json)

        # Handle both array format [120, 240, 480] and object format {"voltages": [120, 240, 480]}
        voltage_list = None
        if isinstance(voltages_data, list):
            # Direct array format: [120, 240, 480]
            voltage_list = voltages_data
        elif isinstance(voltages_data, dict) and "voltages" in voltages_data:
            # Object format: {"voltages": [120, 240, 480]}
            if isinstance(voltages_data["voltages"], list):
                voltage_list = voltages_data["voltages"]
            else:
                errors.append(
                    {
                        "loc": ["available_voltages_json"],
                        "msg": "Voltages must be an array",
                        "type": "value_error",
                    }
                )
        else:
            errors.append(
                {
                    "loc": ["available_voltages_json"],
                    "msg": "Available voltages must be a JSON array or object with 'voltages' key",
                    "type": "value_error",
                }
            )

        # Validate voltage values if we have a valid list
        if voltage_list is not None:
            for voltage in voltage_list:
                if not isinstance(voltage, (int, float)) or voltage <= 0:
                    errors.append(
                        {
                            "loc": ["available_voltages_json"],
                            "msg": "All voltage values must be positive numbers",
                            "type": "value_error",
                        }
                    )
                    break

    # Validate project name and number are not empty
    if not target.name or not target.name.strip():
        errors.append(
            {
                "loc": ["name"],
                "msg": "Project name cannot be empty",
                "type": "value_error",
            }
        )

    if not target.project_number or not target.project_number.strip():
        errors.append(
            {
                "loc": ["project_number"],
                "msg": "Project number cannot be empty",
                "type": "value_error",
            }
        )

    # If there are validation errors, raise exception
    if errors:
        logger.warning(f"Project validation failed: {len(errors)} errors")
        raise DataValidationError(details={"validation_errors": errors})

    logger.debug(f"Project validation passed for: {target.name}")


# Register event listeners for Project model validation
@event.listens_for(Project, "before_insert")
def before_insert_listener(mapper: Any, connection: Any, target: Any) -> None:
    """Validate project data before insert."""
    validate_project_data(mapper, connection, target)


@event.listens_for(Project, "before_update")
def before_update_listener(mapper: Any, connection: Any, target: Any) -> None:
    """Validate project data before update."""
    validate_project_data(mapper, connection, target)


# Apply unified error handling to functions after definition to avoid circular imports
def _apply_unified_error_handling_to_project() -> None:
    """Apply unified error handling decorators to project validation functions."""
    try:
        from src.core.errors.unified_error_handler import handle_validation_errors

        # Apply error handling to the validation function
        global validate_project_data
        if not hasattr(validate_project_data, "_unified_error_wrapped"):
            validate_project_data = handle_validation_errors("project_data_validation")(validate_project_data)
            try:
                validate_project_data._unified_error_wrapped = True  # type: ignore
            except AttributeError:
                # If we can"t set the attribute, just continue
                pass

    except ImportError:
        # Unified error handler not available, skip wrapping
        pass


# Apply the error handling
_apply_unified_error_handling_to_project()
