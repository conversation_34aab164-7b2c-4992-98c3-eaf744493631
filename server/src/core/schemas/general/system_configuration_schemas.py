"""System Configuration schemas.

This module provides Pydantic schemas for system configuration operations,
supporting electrical standards compliance and user preference management.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator
from src.core.enums.common_enums import TemperatureUnit, FrequencyType
from src.core.enums.standards_enums import ElectricalStandardType, ComplianceLevel
from src.core.schemas.base_schemas import (
    BaseSchema,
    PaginatedResponseSchema,
    TimestampMixin,
)


class SystemConfigurationBaseSchema(BaseSchema):
    """Base schema for system configuration."""

    name: str = Field(..., min_length=1, max_length=255, description="Configuration name")
    scope: str = Field("global", description="Configuration scope (global, project, user)")
    category: str = Field(..., min_length=1, max_length=100, description="Configuration category")
    voltage_system: Optional[str] = Field(None, description="Default voltage system")
    frequency_system: Optional[FrequencyType] = Field(None, description="Default frequency")
    temperature_unit: Optional[TemperatureUnit] = Field(TemperatureUnit.CELSIUS, description="Temperature unit")
    calculation_precision: int = Field(2, ge=0, le=10, description="Calculation decimal places")
    rounding_method: str = Field("round_half_up", description="Rounding method")
    safety_factors: Optional[Dict[str, float]] = Field({}, description="Safety factors by equipment type")
    environmental_conditions: Optional[Dict[str, Any]] = Field({}, description="Environmental parameters")
    standards_compliance: Optional[List[str]] = Field([], description="Applicable standards")
    validation_rules: Optional[Dict[str, Any]] = Field({}, description="Validation rule configuration")
    is_active: bool = Field(True, description="Whether configuration is active")

    @field_validator("safety_factors")
    @classmethod
    def validate_safety_factors(cls, v: Optional[Dict[str, float]]) -> Dict[str, float]:
        """Validate safety factors are positive."""
        if v is None:
            return {}
        for key, factor in v.items():
            if factor <= 0:
                raise ValueError(f"Safety factor '{key}' must be positive")
        return v

    @field_validator("scope")
    @classmethod
    def validate_scope(cls, v: str) -> str:
        """Validate configuration scope."""
        valid_scopes = ["global", "project", "user"]
        if v not in valid_scopes:
            raise ValueError(f"Scope must be one of {valid_scopes}")
        return v


class SystemConfigurationCreateSchema(SystemConfigurationBaseSchema):
    """Schema for creating system configuration."""
    
    project_id: Optional[int] = Field(None, description="Project ID for project-specific config")


class SystemConfigurationUpdateSchema(BaseModel):
    """Schema for updating system configuration."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=255, description="Configuration name")
    voltage_system: Optional[str] = Field(None, description="Default voltage system")
    frequency_system: Optional[FrequencyType] = Field(None, description="Default frequency")
    temperature_unit: Optional[TemperatureUnit] = Field(None, description="Temperature unit")
    calculation_precision: Optional[int] = Field(None, ge=0, le=10, description="Calculation decimal places")
    rounding_method: Optional[str] = Field(None, description="Rounding method")
    safety_factors: Optional[Dict[str, float]] = Field(None, description="Safety factors")
    environmental_conditions: Optional[Dict[str, Any]] = Field(None, description="Environmental parameters")
    standards_compliance: Optional[List[str]] = Field(None, description="Applicable standards")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")
    is_active: Optional[bool] = Field(None, description="Whether configuration is active")


class SystemConfigurationReadSchema(SystemConfigurationBaseSchema, TimestampMixin):
    """Schema for reading system configuration."""
    
    id: int = Field(..., description="Configuration database ID")
    config_id: str = Field(..., description="Configuration UUID")
    project_id: Optional[int] = Field(None, description="Project ID if project-specific")


class ElectricalStandardBaseSchema(BaseSchema):
    """Base schema for electrical standards."""

    name: str = Field(..., min_length=1, max_length=255, description="Standard name")
    standard_type: ElectricalStandardType = Field(..., description="Standard type")
    standard_number: str = Field(..., min_length=1, max_length=50, description="Standard number")
    title: str = Field(..., min_length=1, max_length=500, description="Standard title")
    version: Optional[str] = Field(None, max_length=50, description="Standard version")
    publication_date: Optional[datetime] = Field(None, description="Publication date")
    compliance_level: ComplianceLevel = Field(..., description="Compliance level")
    applicable_regions: Optional[List[str]] = Field([], description="Applicable regions")
    scope_description: Optional[str] = Field(None, max_length=2000, description="Scope description")
    calculation_methods: Optional[Dict[str, Any]] = Field({}, description="Calculation methods")
    validation_rules: Optional[Dict[str, Any]] = Field({}, description="Validation rules")
    parameter_limits: Optional[Dict[str, Any]] = Field({}, description="Parameter limits")
    is_active: bool = Field(True, description="Whether standard is active")

    @field_validator("standard_number")
    @classmethod
    def validate_standard_number(cls, v: str) -> str:
        """Validate standard number format."""
        if not v.strip():
            raise ValueError("Standard number cannot be empty")
        return v.strip()


class ElectricalStandardCreateSchema(ElectricalStandardBaseSchema):
    """Schema for creating electrical standard."""
    pass


class ElectricalStandardUpdateSchema(BaseModel):
    """Schema for updating electrical standard."""
    
    title: Optional[str] = Field(None, min_length=1, max_length=500, description="Standard title")
    version: Optional[str] = Field(None, max_length=50, description="Standard version")
    publication_date: Optional[datetime] = Field(None, description="Publication date")
    compliance_level: Optional[ComplianceLevel] = Field(None, description="Compliance level")
    applicable_regions: Optional[List[str]] = Field(None, description="Applicable regions")
    scope_description: Optional[str] = Field(None, max_length=2000, description="Scope description")
    calculation_methods: Optional[Dict[str, Any]] = Field(None, description="Calculation methods")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")
    parameter_limits: Optional[Dict[str, Any]] = Field(None, description="Parameter limits")
    is_active: Optional[bool] = Field(None, description="Whether standard is active")


class ElectricalStandardReadSchema(ElectricalStandardBaseSchema, TimestampMixin):
    """Schema for reading electrical standard."""
    
    id: int = Field(..., description="Standard database ID")
    standard_id: str = Field(..., description="Standard UUID")


class UserPreferencesBaseSchema(BaseSchema):
    """Base schema for user preferences."""

    name: str = Field(..., min_length=1, max_length=255, description="Preference name")
    category: str = Field(..., min_length=1, max_length=100, description="Preference category")
    default_units: Optional[Dict[str, str]] = Field({}, description="Preferred units")
    calculation_preferences: Optional[Dict[str, Any]] = Field({}, description="Calculation preferences")
    ui_preferences: Optional[Dict[str, Any]] = Field({}, description="UI preferences")
    notification_settings: Optional[Dict[str, Any]] = Field({}, description="Notification settings")
    workflow_preferences: Optional[Dict[str, Any]] = Field({}, description="Workflow preferences")
    is_active: bool = Field(True, description="Whether preferences are active")

    @field_validator("default_units")
    @classmethod
    def validate_units(cls, v: Optional[Dict[str, str]]) -> Dict[str, str]:
        """Validate unit preferences."""
        if v is None:
            return {}
        # Basic validation - could be enhanced with actual unit validation
        return v


class UserPreferencesCreateSchema(UserPreferencesBaseSchema):
    """Schema for creating user preferences."""
    
    user_id: int = Field(..., description="User ID")


class UserPreferencesUpdateSchema(BaseModel):
    """Schema for updating user preferences."""
    
    default_units: Optional[Dict[str, str]] = Field(None, description="Preferred units")
    calculation_preferences: Optional[Dict[str, Any]] = Field(None, description="Calculation preferences")
    ui_preferences: Optional[Dict[str, Any]] = Field(None, description="UI preferences")
    notification_settings: Optional[Dict[str, Any]] = Field(None, description="Notification settings")
    workflow_preferences: Optional[Dict[str, Any]] = Field(None, description="Workflow preferences")
    is_active: Optional[bool] = Field(None, description="Whether preferences are active")


class UserPreferencesReadSchema(UserPreferencesBaseSchema, TimestampMixin):
    """Schema for reading user preferences."""
    
    id: int = Field(..., description="Preference database ID")
    preference_id: str = Field(..., description="Preference UUID")
    user_id: int = Field(..., description="User ID")


class ConfigurationTemplateBaseSchema(BaseSchema):
    """Base schema for configuration templates."""

    name: str = Field(..., min_length=1, max_length=255, description="Template name")
    template_type: str = Field(..., min_length=1, max_length=100, description="Template type")
    region: Optional[str] = Field(None, max_length=100, description="Target region")
    standards_set: Optional[List[str]] = Field([], description="Applicable standards")
    default_values: Optional[Dict[str, Any]] = Field({}, description="Default values")
    validation_rules: Optional[Dict[str, Any]] = Field({}, description="Validation rules")
    description: Optional[str] = Field(None, max_length=2000, description="Template description")
    is_public: bool = Field(False, description="Whether template is public")


class ConfigurationTemplateCreateSchema(ConfigurationTemplateBaseSchema):
    """Schema for creating configuration template."""
    
    created_by_user_id: Optional[int] = Field(None, description="Creator user ID")


class ConfigurationTemplateUpdateSchema(BaseModel):
    """Schema for updating configuration template."""
    
    template_type: Optional[str] = Field(None, min_length=1, max_length=100, description="Template type")
    region: Optional[str] = Field(None, max_length=100, description="Target region")
    standards_set: Optional[List[str]] = Field(None, description="Applicable standards")
    default_values: Optional[Dict[str, Any]] = Field(None, description="Default values")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")
    description: Optional[str] = Field(None, max_length=2000, description="Template description")
    is_public: Optional[bool] = Field(None, description="Whether template is public")


class ConfigurationTemplateReadSchema(ConfigurationTemplateBaseSchema, TimestampMixin):
    """Schema for reading configuration template."""
    
    id: int = Field(..., description="Template database ID")
    template_id: str = Field(..., description="Template UUID")
    created_by_user_id: Optional[int] = Field(None, description="Creator user ID")


# System Configuration organism-specific schemas
class SystemConfigurationSectionSchema(BaseModel):
    """Schema for configuration sections in the organism."""
    
    section_id: str = Field(..., description="Section identifier")
    title: str = Field(..., description="Section title")
    description: Optional[str] = Field(None, description="Section description")
    settings: List[Dict[str, Any]] = Field([], description="Section settings")
    validation_status: str = Field("valid", description="Validation status")
    compliance_standards: List[str] = Field([], description="Applicable standards")


class SystemConfigurationOrganism(BaseModel):
    """Complete system configuration data for the organism."""
    
    global_configuration: Dict[str, Any] = Field({}, description="Global configuration settings")
    project_configuration: Optional[Dict[str, Any]] = Field(None, description="Project-specific overrides")
    user_preferences: Dict[str, Any] = Field({}, description="User preference settings")
    configuration_sections: List[SystemConfigurationSectionSchema] = Field([], description="Configuration sections")
    available_templates: List[ConfigurationTemplateReadSchema] = Field([], description="Available templates")
    electrical_standards: List[ElectricalStandardReadSchema] = Field([], description="Applicable standards")
    validation_results: Dict[str, Any] = Field({}, description="Configuration validation results")
    compliance_status: Dict[str, str] = Field({}, description="Standards compliance status")


# Response schemas
class SystemConfigurationListResponseSchema(PaginatedResponseSchema[SystemConfigurationReadSchema]):
    """Response schema for system configuration list."""
    pass


class ElectricalStandardListResponseSchema(PaginatedResponseSchema[ElectricalStandardReadSchema]):
    """Response schema for electrical standards list."""
    pass


class UserPreferencesListResponseSchema(PaginatedResponseSchema[UserPreferencesReadSchema]):
    """Response schema for user preferences list."""
    pass


class ConfigurationTemplateListResponseSchema(PaginatedResponseSchema[ConfigurationTemplateReadSchema]):
    """Response schema for configuration templates list."""
    pass