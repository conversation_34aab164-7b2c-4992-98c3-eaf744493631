"""System Configuration API endpoints.

This module provides API endpoints for managing system configurations,
electrical standards, user preferences, and configuration templates.
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, Path, Body
from fastapi.responses import JSONResponse

from src.config.logging_config import logger
from src.core.errors.exceptions import BusinessLogicError, NotFoundError
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.database.connection_manager import get_contextual_db_session, _connection_manager
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.system_configuration_schemas import (
    SystemConfigurationCreateSchema,
    SystemConfigurationReadSchema,
    SystemConfigurationUpdateSchema,
    SystemConfigurationListResponseSchema,
    ElectricalStandardCreateSchema,
    ElectricalStandardReadSchema,
    ElectricalStandardUpdateSchema,
    ElectricalStandardListResponseSchema,
    UserPreferencesCreateSchema,
    UserPreferencesReadSchema,
    UserPreferencesUpdateSchema,
    UserPreferencesListResponseSchema,
    ConfigurationTemplateCreateSchema,
    ConfigurationTemplateReadSchema,
    ConfigurationTemplateUpdateSchema,
    ConfigurationTemplateListResponseSchema,
    SystemConfigurationOrganism,
)
from src.core.security.enhanced_dependencies import require_authenticated_user
from src.core.services.general.project_phase_service import SystemConfigurationService


def get_system_configuration_service(
    session: Any = Depends(get_contextual_db_session),
) -> SystemConfigurationService:
    """Get system configuration service dependency."""
    return SystemConfigurationService(ProjectRepository(session), _connection_manager)


router = APIRouter(
    prefix="/system-configuration",
    tags=["system-configuration"],
    responses={
        404: {"model": ErrorResponseSchema, "description": "Not found"},
        422: {"model": ErrorResponseSchema, "description": "Validation error"},
        500: {"model": ErrorResponseSchema, "description": "Internal server error"},
    },
)


# System Configuration endpoints
@router.get(
    "/configuration",
    response_model=Dict[str, Any],
    summary="Get system configuration",
    description="Get system configuration with inheritance (global -> project -> user)",
)
@handle_api_errors("get_system_configuration")
@monitor_api_performance("system_config_api.get_configuration")
async def get_system_configuration(
    scope: str = Query("global", description="Configuration scope"),
    project_id: Optional[int] = Query(None, description="Project ID for project-specific config"),
    category: Optional[str] = Query(None, description="Configuration category filter"),
    current_user = Depends(require_authenticated_user),
    service: SystemConfigurationService = Depends(get_system_configuration_service),
) -> Dict[str, Any]:
    """Get system configuration with inheritance."""
    logger.debug(f"Retrieving system configuration for scope: {scope}")
    
    try:
        configuration = await service.get_configuration(
            scope=scope,
            project_id=project_id,
            category=category
        )
        
        return configuration
        
    except Exception as e:
        logger.error(f"Failed to retrieve system configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system configuration"
        )


@router.get(
    "/organism",
    response_model=SystemConfigurationOrganism,
    summary="Get complete system configuration data",
    description="Get complete system configuration data for the SystemConfiguration organism",
)
@handle_api_errors("get_system_configuration_organism")
@monitor_api_performance("system_config_api.get_organism_data")
async def get_system_configuration_organism(
    project_id: Optional[int] = Query(None, description="Project ID for project-specific config"),
    current_user = Depends(require_authenticated_user),
    service: SystemConfigurationService = Depends(get_system_configuration_service),
) -> SystemConfigurationOrganism:
    """Get complete system configuration data for the organism."""
    logger.debug(f"Retrieving system configuration organism data")
    
    try:
        # Get global configuration
        global_config = await service.get_configuration(scope="global")
        
        # Get project-specific configuration if requested
        project_config = None
        if project_id:
            project_config = await service.get_configuration(
                scope="project", 
                project_id=project_id
            )
        
        # Get user preferences
        user_preferences = await service.get_configuration(scope="user")
        
        # Create configuration sections
        configuration_sections = [
            {
                "section_id": "electrical_system",
                "title": "Electrical System Settings",
                "description": "Default voltage, frequency, and power system configuration",
                "settings": [
                    {
                        "key": "voltage_system",
                        "label": "Voltage System",
                        "value": global_config.get("voltage_system", "3-phase"),
                        "type": "select",
                        "options": ["single-phase", "3-phase", "DC"]
                    },
                    {
                        "key": "frequency_system", 
                        "label": "System Frequency",
                        "value": global_config.get("frequency_system", "50Hz"),
                        "type": "select",
                        "options": ["50Hz", "60Hz", "DC"]
                    }
                ],
                "validation_status": "valid",
                "compliance_standards": ["IEC 60364", "IEEE C2"]
            },
            {
                "section_id": "calculation_settings",
                "title": "Calculation Settings",
                "description": "Precision, rounding, and calculation method preferences",
                "settings": [
                    {
                        "key": "calculation_precision",
                        "label": "Decimal Places",
                        "value": global_config.get("calculation_precision", 2),
                        "type": "number",
                        "min": 0,
                        "max": 10
                    },
                    {
                        "key": "rounding_method",
                        "label": "Rounding Method",
                        "value": global_config.get("rounding_method", "round_half_up"),
                        "type": "select",
                        "options": ["round_half_up", "round_half_down", "round_up", "round_down"]
                    }
                ],
                "validation_status": "valid",
                "compliance_standards": ["IEEE 1584"]
            },
            {
                "section_id": "safety_factors",
                "title": "Safety Factors",
                "description": "Equipment-specific safety factor configurations",
                "settings": [
                    {
                        "key": "motor_starting",
                        "label": "Motor Starting Factor",
                        "value": global_config.get("safety_factors", {}).get("motor_starting", 1.25),
                        "type": "number",
                        "min": 1.0,
                        "max": 3.0,
                        "step": 0.05
                    }
                ],
                "validation_status": "valid",
                "compliance_standards": ["IEC 60034", "NEMA MG-1"]
            },
            {
                "section_id": "environmental",
                "title": "Environmental Conditions", 
                "description": "Default environmental parameters for calculations",
                "settings": [
                    {
                        "key": "default_ambient_temp",
                        "label": "Ambient Temperature (°C)",
                        "value": global_config.get("environmental_conditions", {}).get("default_ambient_temp", 25.0),
                        "type": "number",
                        "min": -50,
                        "max": 80
                    }
                ],
                "validation_status": "valid", 
                "compliance_standards": ["IEC 60364-5-51"]
            }
        ]
        
        # Get available templates (placeholder)
        available_templates = []
        
        # Get electrical standards (placeholder)
        electrical_standards = []
        
        # Validation results
        validation_results = {
            "overall_status": "valid",
            "last_validated": "2024-08-08T12:00:00Z",
            "issues_found": 0,
            "warnings": []
        }
        
        # Compliance status
        compliance_status = {
            "IEC 60364": "compliant",
            "IEEE C2": "compliant", 
            "EN 50110": "compliant"
        }
        
        return SystemConfigurationOrganism(
            global_configuration=global_config,
            project_configuration=project_config,
            user_preferences=user_preferences,
            configuration_sections=configuration_sections,
            available_templates=available_templates,
            electrical_standards=electrical_standards,
            validation_results=validation_results,
            compliance_status=compliance_status,
        )
        
    except Exception as e:
        logger.error(f"Failed to retrieve system configuration organism data: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve system configuration organism data"
        )


# Configuration validation endpoint
@router.post(
    "/validate",
    response_model=Dict[str, Any],
    summary="Validate system configuration",
    description="Validate system configuration against electrical standards",
)
@handle_api_errors("validate_system_configuration")
@monitor_api_performance("system_config_api.validate_configuration")
async def validate_system_configuration(
    configuration: Dict[str, Any] = Body(..., description="Configuration to validate"),
    standards: List[str] = Body([], description="Standards to validate against"),
    current_user = Depends(require_authenticated_user),
    service: SystemConfigurationService = Depends(get_system_configuration_service),
) -> Dict[str, Any]:
    """Validate system configuration against electrical standards."""
    logger.info(f"Validating system configuration against {len(standards)} standards")
    
    try:
        # Perform validation (placeholder implementation)
        validation_results = {
            "valid": True,
            "issues": [],
            "warnings": [],
            "compliance": {
                standard: "compliant" for standard in standards
            },
            "validated_at": "2024-08-08T12:00:00Z",
            "validated_by": current_user.get("user_id")
        }
        
        # Basic validation rules (would be expanded)
        if "voltage_system" in configuration:
            voltage_system = configuration["voltage_system"]
            if voltage_system not in ["single-phase", "3-phase", "DC"]:
                validation_results["valid"] = False
                validation_results["issues"].append({
                    "field": "voltage_system",
                    "message": f"Invalid voltage system: {voltage_system}",
                    "severity": "error"
                })
        
        if "calculation_precision" in configuration:
            precision = configuration["calculation_precision"]
            if not isinstance(precision, int) or precision < 0 or precision > 10:
                validation_results["valid"] = False
                validation_results["issues"].append({
                    "field": "calculation_precision",
                    "message": "Calculation precision must be between 0 and 10",
                    "severity": "error"
                })
        
        logger.info(f"Configuration validation completed: {validation_results['valid']}")
        return validation_results
        
    except Exception as e:
        logger.error(f"Failed to validate system configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate system configuration"
        )


# Configuration export/import endpoints
@router.get(
    "/export",
    response_model=Dict[str, Any],
    summary="Export system configuration",
    description="Export system configuration as JSON for backup or sharing",
)
@handle_api_errors("export_system_configuration")
@monitor_api_performance("system_config_api.export_configuration")
async def export_system_configuration(
    scope: str = Query("global", description="Configuration scope to export"),
    project_id: Optional[int] = Query(None, description="Project ID for project-specific export"),
    current_user = Depends(require_authenticated_user),
    service: SystemConfigurationService = Depends(get_system_configuration_service),
) -> Dict[str, Any]:
    """Export system configuration as JSON."""
    logger.info(f"Exporting system configuration for scope: {scope}")
    
    try:
        configuration = await service.get_configuration(
            scope=scope,
            project_id=project_id
        )
        
        export_data = {
            "export_metadata": {
                "version": "1.0",
                "exported_at": "2024-08-08T12:00:00Z",
                "exported_by": current_user.get("user_id"),
                "scope": scope,
                "project_id": project_id
            },
            "configuration": configuration
        }
        
        logger.info(f"Configuration export completed for scope: {scope}")
        return export_data
        
    except Exception as e:
        logger.error(f"Failed to export system configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to export system configuration"
        )


@router.post(
    "/import", 
    response_model=Dict[str, Any],
    summary="Import system configuration",
    description="Import system configuration from JSON backup",
)
@handle_api_errors("import_system_configuration")
@monitor_api_performance("system_config_api.import_configuration")
async def import_system_configuration(
    import_data: Dict[str, Any] = Body(..., description="Configuration data to import"),
    validate_only: bool = Body(False, embed=True, description="Only validate, don't import"),
    current_user = Depends(require_authenticated_user),
    service: SystemConfigurationService = Depends(get_system_configuration_service),
) -> Dict[str, Any]:
    """Import system configuration from JSON."""
    logger.info(f"Importing system configuration (validate_only={validate_only})")
    
    try:
        # Validate import data structure
        if "export_metadata" not in import_data or "configuration" not in import_data:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Invalid import data format"
            )
        
        # Perform validation
        configuration = import_data["configuration"]
        validation_result = {
            "valid": True,
            "issues": [],
            "imported_settings": len(configuration),
            "validated_at": "2024-08-08T12:00:00Z"
        }
        
        if validate_only:
            return {
                "status": "validated",
                "validation": validation_result,
                "message": "Configuration validation completed successfully"
            }
        
        # TODO: Implement actual import logic
        # This would save the configuration to the database
        
        return {
            "status": "imported",
            "validation": validation_result,
            "imported_at": "2024-08-08T12:00:00Z",
            "message": "Configuration imported successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to import system configuration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to import system configuration"
        )