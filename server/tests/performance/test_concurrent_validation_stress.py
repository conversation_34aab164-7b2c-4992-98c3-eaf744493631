"""Concurrent Validation Stress Tests.

This module implements comprehensive stress testing for validation logic under
high concurrency scenarios. It focuses on testing race conditions, deadlocks,
and performance degradation in validation pipelines when subjected to
simultaneous requests.

Test categories:
1. Race condition testing for email uniqueness validation
2. Concurrent user registration stress testing
3. Project creation validation under concurrent load
4. Validation error handling under concurrent conditions
5. Database connection pool behavior during validation stress
"""

import asyncio
import pytest
import time
import uuid
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Tuple, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
import psutil
import threading
from collections import defaultdict, Counter

from src.core.models.general.user import User
from src.core.models.general.project import Project
from src.core.repositories.general.user_repository import UserRepository
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.services.general.user_service import UserService
from src.core.services.general.project_service import ProjectService
from src.core.schemas.general.user_schemas import UserCreateSchema, UserUpdateSchema
from src.core.schemas.general.project_schemas import ProjectCreateSchema
from src.core.errors.exceptions import (
    InvalidInputError,
    DuplicateEntryError,
    DataValidationError,
    ServiceError,
)
from src.core.enums import ProjectStatus

pytestmark = [pytest.mark.performance]


class ConcurrentValidationMetrics:
    """Collect and analyze metrics during concurrent validation testing."""

    def __init__(self):
        self.operation_times: Dict[str, List[float]] = defaultdict(list)
        self.success_counts: Dict[str, int] = defaultdict(int)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.error_types: Dict[str, Counter] = defaultdict(Counter)
        self.lock = threading.Lock()

    def record_operation(self, operation: str, duration: float, success: bool, error_type: str = None):
        """Thread-safe recording of operation metrics."""
        with self.lock:
            self.operation_times[operation].append(duration)
            if success:
                self.success_counts[operation] += 1
            else:
                self.error_counts[operation] += 1
                if error_type:
                    self.error_types[operation][error_type] += 1

    def get_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary."""
        with self.lock:
            summary = {}
            for operation in self.operation_times:
                times = self.operation_times[operation]
                if times:
                    summary[operation] = {
                        "total_operations": len(times),
                        "success_count": self.success_counts[operation],
                        "error_count": self.error_counts[operation],
                        "success_rate": (self.success_counts[operation] / len(times)) * 100,
                        "avg_time_ms": sum(times) / len(times),
                        "min_time_ms": min(times),
                        "max_time_ms": max(times),
                        "p95_time_ms": sorted(times)[int(len(times) * 0.95)] if len(times) > 20 else max(times),
                        "error_types": dict(self.error_types[operation]),
                    }
            return summary


class TestConcurrentValidationStress:
    """Stress test validation logic under high concurrency."""

    @pytest.fixture(autouse=True)
    def setup_metrics(self):
        """Set up metrics collection for each test."""
        self.metrics = ConcurrentValidationMetrics()
        self.start_memory = psutil.Process().memory_info().rss
        yield
        # Metrics will be accessed in test methods

    def execute_concurrent_user_registration(
        self,
        db_session: Session,
        base_email: str,
        thread_id: int,
        operations_per_thread: int,
    ) -> List[Tuple[bool, str, float]]:
        """Execute user registration operations in a single thread.

        Returns:
            List of (success, error_type, duration_ms) tuples
        """
        from src.core.repositories.general.user_repository import UserRepository
        from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

        user_repo = UserRepository(db_session)
        preference_repo = UserPreferenceRepository(db_session)
        user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)
        results = []

        for i in range(operations_per_thread):
            start_time = time.perf_counter()

            # Create unique email for this operation
            email = f"{base_email}_{thread_id}_{i}@concurrenttest.com"
            user_data = UserCreateSchema(
                name=f"Concurrent User {thread_id}-{i}",
                email=email,
                password="ConcurrentTest123!",
            )

            try:
                created_user = user_service.create_user(user_data)
                end_time = time.perf_counter()
                duration = (end_time - start_time) * 1000

                results.append((True, None, duration))
                self.metrics.record_operation("user_registration", duration, True)

            except (InvalidInputError, DuplicateEntryError, DataValidationError) as e:
                end_time = time.perf_counter()
                duration = (end_time - start_time) * 1000

                error_type = type(e).__name__
                results.append((False, error_type, duration))
                self.metrics.record_operation("user_registration", duration, False, error_type)

            except Exception as e:
                end_time = time.perf_counter()
                duration = (end_time - start_time) * 1000

                error_type = f"Unexpected_{type(e).__name__}"
                results.append((False, error_type, duration))
                self.metrics.record_operation("user_registration", duration, False, error_type)

        return results

    def test_concurrent_user_registration_race_conditions(self, db_session: Session):
        """Test user registration under high concurrency for race conditions.

        This test specifically targets:
        - Email uniqueness validation race conditions
        - Database constraint violations under concurrent inserts
        - Validation pipeline stability under load
        """
        unique_suffix = str(uuid.uuid4())[:8]
        base_email = f"race_test_{unique_suffix}"

        # Test parameters
        num_threads = 20
        operations_per_thread = 25
        total_operations = num_threads * operations_per_thread

        print(f"\n🔄 Testing concurrent user registration with {num_threads} threads")
        print(f"📊 Total operations: {total_operations}")

        start_time = time.time()

        # Execute concurrent user registrations
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(
                    self.execute_concurrent_user_registration,
                    db_session,
                    base_email,
                    thread_id,
                    operations_per_thread,
                )
                for thread_id in range(num_threads)
            ]

            # Collect results
            all_results = []
            for future in as_completed(futures):
                thread_results = future.result()
                all_results.extend(thread_results)

        total_time = time.time() - start_time

        # Analyze results
        successful_registrations = sum(1 for success, _, _ in all_results if success)
        failed_registrations = total_operations - successful_registrations
        success_rate = (successful_registrations / total_operations) * 100
        throughput = total_operations / total_time

        # Categorize errors
        error_types = Counter()
        for success, error_type, _ in all_results:
            if not success and error_type:
                error_types[error_type] += 1

        print(f"✅ Successful registrations: {successful_registrations}")
        print(f"❌ Failed registrations: {failed_registrations}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        print(f"⚡ Throughput: {throughput:.1f} operations/sec")
        print(f"🔍 Error breakdown: {dict(error_types)}")

        # Performance assertions
        assert success_rate >= 85.0, f"Success rate should be ≥85%, got {success_rate:.1f}%"
        assert throughput >= 10.0, f"Throughput should be ≥10 ops/sec, got {throughput:.1f}"

        # Check for unexpected errors (should be mostly validation errors)
        unexpected_errors = sum(count for error_type, count in error_types.items() if "Unexpected" in error_type)
        assert unexpected_errors < total_operations * 0.05, f"Too many unexpected errors: {unexpected_errors}"

    def execute_concurrent_email_uniqueness_checks(
        self, db_session: Session, test_emails: List[str], thread_id: int
    ) -> List[Tuple[str, bool, float]]:
        """Execute email uniqueness checks concurrently.

        Returns:
            List of (email, exists, duration_ms) tuples
        """
        from tests.performance.sync_repository_adapter import SyncUserRepositoryAdapter

        user_repo = SyncUserRepositoryAdapter(db_session)
        results = []

        for email in test_emails:
            start_time = time.perf_counter()

            try:
                exists = user_repo.check_email_exists(email)
                end_time = time.perf_counter()
                duration = (end_time - start_time) * 1000

                results.append((email, exists, duration))
                self.metrics.record_operation("email_uniqueness_check", duration, True)

            except Exception as e:
                end_time = time.perf_counter()
                duration = (end_time - start_time) * 1000

                results.append((email, False, duration))
                self.metrics.record_operation("email_uniqueness_check", duration, False, type(e).__name__)

        return results

    def test_concurrent_email_uniqueness_validation_stress(self, db_session: Session):
        """Test email uniqueness validation under concurrent access stress.

        This test focuses on:
        - Database query performance under concurrent email lookups
        - Case-insensitive email comparison under load
        - Connection pool behavior during validation stress
        """
        unique_suffix = str(uuid.uuid4())[:8]

        # Pre-create a set of test users for existence checking
        from tests.performance.sync_repository_adapter import SyncUserRepositoryAdapter

        user_repo = SyncUserRepositoryAdapter(db_session)
        existing_emails = []

        print(f"\n🔧 Creating baseline users for uniqueness testing...")
        for i in range(100):
            email = f"existing_{unique_suffix}_{i}@baseline.com"
            user_data = {
                "name": f"Baseline User {unique_suffix} {i}",
                "email": email,
                "password_hash": f"hash_{i}",
            }
            user_repo.create(user_data)
            existing_emails.append(email)

        db_session.commit()
        print(f"✅ Created {len(existing_emails)} baseline users")

        # Prepare test email sets for concurrent checking
        test_email_sets = []
        num_threads = 15
        emails_per_thread = 50

        for thread_id in range(num_threads):
            thread_emails = []

            # Mix of existing and non-existing emails
            for i in range(emails_per_thread):
                if i % 3 == 0:
                    # Use existing email with case variations
                    base_email = random.choice(existing_emails)
                    variations = [
                        base_email.upper(),
                        base_email.capitalize(),
                        base_email.swapcase(),
                        f"  {base_email}  ",  # With whitespace
                    ]
                    thread_emails.append(random.choice(variations))
                elif i % 3 == 1:
                    # Use existing email as-is
                    thread_emails.append(random.choice(existing_emails))
                else:
                    # Use non-existing email
                    thread_emails.append(f"nonexistent_{unique_suffix}_{thread_id}_{i}@fake.com")

            test_email_sets.append(thread_emails)

        print(f"🔍 Testing email uniqueness with {num_threads} concurrent threads")
        print(f"📊 Total email checks: {num_threads * emails_per_thread}")

        start_time = time.time()

        # Execute concurrent email uniqueness checks
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(
                    self.execute_concurrent_email_uniqueness_checks,
                    db_session,
                    email_set,
                    thread_id,
                )
                for thread_id, email_set in enumerate(test_email_sets)
            ]

            # Collect results
            all_results = []
            for future in as_completed(futures):
                thread_results = future.result()
                all_results.extend(thread_results)

        total_time = time.time() - start_time

        # Analyze performance
        check_times = [duration for _, _, duration in all_results]
        avg_check_time = sum(check_times) / len(check_times)
        max_check_time = max(check_times)
        p95_check_time = sorted(check_times)[int(len(check_times) * 0.95)]
        throughput = len(all_results) / total_time

        print(f"⚡ Email uniqueness check performance:")
        print(f"   Average: {avg_check_time:.2f}ms")
        print(f"   Max: {max_check_time:.2f}ms")
        print(f"   P95: {p95_check_time:.2f}ms")
        print(f"   Throughput: {throughput:.1f} checks/sec")

        # Verify correctness of email existence detection
        correct_detections = 0
        for email, exists, _ in all_results:
            normalized_email = email.lower().strip()
            expected_exists = any(baseline.lower() == normalized_email for baseline in existing_emails)
            if exists == expected_exists:
                correct_detections += 1

        accuracy = (correct_detections / len(all_results)) * 100
        print(f"🎯 Detection accuracy: {accuracy:.1f}%")

        # Performance assertions (relaxed for SyncUserRepositoryAdapter)
        assert avg_check_time < 1000.0, f"Average check time should be <1000ms, got {avg_check_time:.2f}ms"
        assert p95_check_time < 2000.0, f"P95 check time should be <2000ms, got {p95_check_time:.2f}ms"
        assert throughput >= 1.0, f"Throughput should be ≥1 checks/sec, got {throughput:.1f}"
        assert accuracy >= 99.0, f"Detection accuracy should be ≥99%, got {accuracy:.1f}%"

    def execute_concurrent_project_creation(
        self,
        db_session: Session,
        base_name: str,
        thread_id: int,
        projects_per_thread: int,
    ) -> List[Tuple[bool, str, float]]:
        """Execute project creation operations in a single thread."""
        project_repo = ProjectRepository(db_session)
        project_service = ProjectService(project_repo)
        results = []

        for i in range(projects_per_thread):
            start_time = time.perf_counter()

            # Create unique project name
            project_name = f"{base_name} {thread_id}-{i}"
            project_data = ProjectCreateSchema(
                name=project_name,
                description=f"Concurrent test project created by thread {thread_id}",
                status=ProjectStatus.DRAFT,
            )

            try:
                created_project = project_service.create_project(project_data)
                end_time = time.perf_counter()
                duration = (end_time - start_time) * 1000

                results.append((True, None, duration))
                self.metrics.record_operation("project_creation", duration, True)

            except (DataValidationError, ServiceError, InvalidInputError) as e:
                end_time = time.perf_counter()
                duration = (end_time - start_time) * 1000

                error_type = type(e).__name__
                results.append((False, error_type, duration))
                self.metrics.record_operation("project_creation", duration, False, error_type)

            except Exception as e:
                end_time = time.perf_counter()
                duration = (end_time - start_time) * 1000

                error_type = f"Unexpected_{type(e).__name__}"
                results.append((False, error_type, duration))
                self.metrics.record_operation("project_creation", duration, False, error_type)

        return results

    def test_concurrent_project_creation_validation_stress(self, db_session: Session):
        """Test project creation validation under concurrent load.

        This test focuses on:
        - Project name uniqueness validation under concurrency
        - Validation pipeline performance with complex business rules
        - Database transaction handling under concurrent project creation
        """
        unique_suffix = str(uuid.uuid4())[:8]
        base_name = f"Concurrent Test Project {unique_suffix}"

        # Test parameters
        num_threads = 12
        projects_per_thread = 15
        total_projects = num_threads * projects_per_thread

        print(f"\n🏗️ Testing concurrent project creation with {num_threads} threads")
        print(f"📊 Total project creations: {total_projects}")

        start_time = time.time()

        # Execute concurrent project creation
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(
                    self.execute_concurrent_project_creation,
                    db_session,
                    base_name,
                    thread_id,
                    projects_per_thread,
                )
                for thread_id in range(num_threads)
            ]

            # Collect results
            all_results = []
            for future in as_completed(futures):
                thread_results = future.result()
                all_results.extend(thread_results)

        total_time = time.time() - start_time

        # Analyze results
        successful_creations = sum(1 for success, _, _ in all_results if success)
        failed_creations = total_projects - successful_creations
        success_rate = (successful_creations / total_projects) * 100
        throughput = total_projects / total_time

        # Analyze creation times
        creation_times = [duration for success, _, duration in all_results if success]
        if creation_times:
            avg_creation_time = sum(creation_times) / len(creation_times)
            max_creation_time = max(creation_times)
            p95_creation_time = sorted(creation_times)[int(len(creation_times) * 0.95)]
        else:
            avg_creation_time = max_creation_time = p95_creation_time = 0

        print(f"✅ Successful creations: {successful_creations}")
        print(f"❌ Failed creations: {failed_creations}")
        print(f"📈 Success rate: {success_rate:.1f}%")
        print(f"⚡ Throughput: {throughput:.1f} projects/sec")
        print(
            f"⏱️ Creation time - Avg: {avg_creation_time:.2f}ms, Max: {max_creation_time:.2f}ms, P95: {p95_creation_time:.2f}ms"
        )

        # Performance assertions
        assert success_rate >= 90.0, f"Success rate should be ≥90%, got {success_rate:.1f}%"
        assert throughput >= 5.0, f"Throughput should be ≥5 projects/sec, got {throughput:.1f}"
        if creation_times:
            assert avg_creation_time < 500.0, f"Average creation time should be <500ms, got {avg_creation_time:.2f}ms"

    def test_validation_error_handling_under_concurrent_load(self, db_session: Session):
        """Test validation error handling stability under concurrent conditions.

        This test specifically stresses error handling paths to ensure:
        - Error responses are consistent under load
        - No deadlocks occur during error processing
        - Error logging doesn't become a bottleneck
        """
        unique_suffix = str(uuid.uuid4())[:8]

        def execute_validation_error_scenarios(
            thread_id: int,
        ) -> List[Tuple[str, bool, str, float]]:
            """Execute various validation error scenarios."""
            from src.core.repositories.general.user_repository import UserRepository
            from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

            user_repo = UserRepository(db_session)
            preference_repo = UserPreferenceRepository(db_session)
            user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)
            results = []

            # Define error scenarios
            error_scenarios = [
                {
                    "name": "invalid_email",
                    "data": UserCreateSchema(
                        name=f"Error Test {thread_id}",
                        email="invalid-email-format",
                        password="ValidPass123!",
                    ),
                    "expected_error": "ValidationError",
                },
                {
                    "name": "short_name",
                    "data": UserCreateSchema(
                        name="AB",  # Too short
                        email=f"short_{thread_id}_{random.randint(1000, 9999)}@test.com",
                        password="ValidPass123!",
                    ),
                    "expected_error": "ValidationError",
                },
                {
                    "name": "long_name",
                    "data": UserCreateSchema(
                        name="A" * 51,  # Too long
                        email=f"long_{thread_id}_{random.randint(1000, 9999)}@test.com",
                        password="ValidPass123!",
                    ),
                    "expected_error": "ValidationError",
                },
                {
                    "name": "weak_password",
                    "data": UserCreateSchema(
                        name=f"Weak Pass User {thread_id}",
                        email=f"weak_{thread_id}_{random.randint(1000, 9999)}@test.com",
                        password="123",  # Too weak
                    ),
                    "expected_error": "ValidationError",
                },
            ]

            for scenario in error_scenarios:
                start_time = time.perf_counter()

                try:
                    user_service.create_user(scenario["data"])
                    # If we reach here, validation didn't catch the error
                    end_time = time.perf_counter()
                    duration = (end_time - start_time) * 1000
                    results.append((scenario["name"], False, "NoError", duration))

                except Exception as e:
                    end_time = time.perf_counter()
                    duration = (end_time - start_time) * 1000

                    error_type = type(e).__name__
                    expected_correct = scenario["expected_error"] in error_type
                    results.append((scenario["name"], expected_correct, error_type, duration))

            return results

        print(f"\n🚨 Testing validation error handling under concurrent load")

        # Execute concurrent error scenarios
        num_threads = 25
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(execute_validation_error_scenarios, thread_id) for thread_id in range(num_threads)
            ]

            all_results = []
            for future in as_completed(futures):
                thread_results = future.result()
                all_results.extend(thread_results)

        total_time = time.time() - start_time

        # Analyze error handling performance
        scenario_stats = defaultdict(lambda: {"correct": 0, "total": 0, "times": []})

        for scenario_name, correct, error_type, duration in all_results:
            scenario_stats[scenario_name]["total"] += 1
            scenario_stats[scenario_name]["times"].append(duration)
            if correct:
                scenario_stats[scenario_name]["correct"] += 1

        print(f"🔍 Error handling analysis:")
        for scenario, stats in scenario_stats.items():
            accuracy = (stats["correct"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            avg_time = sum(stats["times"]) / len(stats["times"]) if stats["times"] else 0
            print(f"   {scenario}: {accuracy:.1f}% accuracy, {avg_time:.2f}ms avg")

            # Each error scenario should be caught correctly
            assert accuracy >= 95.0, f"Error handling accuracy for {scenario} should be ≥95%, got {accuracy:.1f}%"
            assert avg_time < 200.0, f"Error handling time for {scenario} should be <200ms, got {avg_time:.2f}ms"

        throughput = len(all_results) / total_time
        print(f"⚡ Error handling throughput: {throughput:.1f} operations/sec")

        assert throughput >= 50.0, f"Error handling throughput should be ≥50 ops/sec, got {throughput:.1f}"

    def test_memory_usage_during_concurrent_validation_stress(self, db_session: Session):
        """Test memory usage patterns during intense concurrent validation operations.

        This test monitors memory consumption to ensure:
        - No memory leaks during concurrent validation
        - Memory usage remains within acceptable bounds
        - Garbage collection efficiency under validation load
        """
        unique_suffix = str(uuid.uuid4())[:8]

        # Monitor memory throughout the test
        initial_memory = psutil.Process().memory_info().rss / (1024 * 1024)  # MB
        print(f"\n💾 Initial memory usage: {initial_memory:.1f}MB")

        def execute_mixed_validation_operations(thread_id: int) -> int:
            """Execute a mix of validation operations to stress memory usage."""
            from src.core.repositories.general.user_repository import UserRepository
            from src.core.repositories.general.user_preference_repository import UserPreferenceRepository

            user_repo = UserRepository(db_session)
            preference_repo = UserPreferenceRepository(db_session)
            user_service = UserService(user_repository=user_repo, preference_repository=preference_repo)
            user_repo = UserRepository(db_session)
            operations_completed = 0

            for i in range(50):  # 50 operations per thread
                operation_type = i % 4

                try:
                    if operation_type == 0:
                        # User registration
                        user_data = UserCreateSchema(
                            name=f"Memory Test User {thread_id}-{i}",
                            email=f"memory_{thread_id}_{i}_{unique_suffix}@test.com",
                            password="MemoryTest123!",
                        )
                        user_service.create_user(user_data)

                    elif operation_type == 1:
                        # Email uniqueness check
                        test_email = f"check_{thread_id}_{i}_{unique_suffix}@test.com"
                        user_repo.check_email_exists(test_email)

                    elif operation_type == 2:
                        # Email lookup
                        test_email = f"lookup_{thread_id}_{i}_{unique_suffix}@test.com"
                        user_repo.get_by_email(test_email)

                    else:
                        # Validation error (intentional)
                        invalid_data = UserCreateSchema(
                            name="AB",  # Too short
                            email=f"invalid_{thread_id}_{i}_{unique_suffix}@test.com",
                            password="ValidPass123!",
                        )
                        try:
                            user_service.create_user(invalid_data)
                        except:
                            pass  # Expected validation error

                    operations_completed += 1

                except Exception:
                    # Continue with other operations even if some fail
                    pass

            return operations_completed

        # Execute concurrent operations while monitoring memory
        num_threads = 30
        print(f"🔄 Executing mixed validation operations with {num_threads} threads")

        memory_samples = []
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(execute_mixed_validation_operations, thread_id) for thread_id in range(num_threads)
            ]

            # Sample memory usage during execution
            completed_futures = 0
            while completed_futures < len(futures):
                time.sleep(0.5)  # Sample every 500ms
                current_memory = psutil.Process().memory_info().rss / (1024 * 1024)
                memory_samples.append(current_memory)

                # Count completed futures
                completed_futures = sum(1 for f in futures if f.done())

            # Collect final results
            total_operations = sum(future.result() for future in futures)

        total_time = time.time() - start_time

        # Analyze memory usage
        max_memory = max(memory_samples) if memory_samples else initial_memory
        final_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        peak_memory_increase = max_memory - initial_memory
        final_memory_increase = final_memory - initial_memory

        print(f"💾 Memory analysis:")
        print(f"   Initial: {initial_memory:.1f}MB")
        print(f"   Peak: {max_memory:.1f}MB (+{peak_memory_increase:.1f}MB)")
        print(f"   Final: {final_memory:.1f}MB (+{final_memory_increase:.1f}MB)")
        print(f"   Operations completed: {total_operations}")
        print(f"   Operations/sec: {total_operations / total_time:.1f}")

        # Force garbage collection and check final memory
        import gc

        gc.collect()

        after_gc_memory = psutil.Process().memory_info().rss / (1024 * 1024)
        gc_memory_reduction = final_memory - after_gc_memory
        print(f"   After GC: {after_gc_memory:.1f}MB (-{gc_memory_reduction:.1f}MB)")

        # Memory usage assertions
        assert peak_memory_increase < 200.0, f"Peak memory increase should be <200MB, got {peak_memory_increase:.1f}MB"
        assert final_memory_increase < 100.0, (
            f"Final memory increase should be <100MB, got {final_memory_increase:.1f}MB"
        )
        assert gc_memory_reduction >= 0, f"Garbage collection should reduce memory, got {gc_memory_reduction:.1f}MB"

        # Performance assertion
        min_throughput = 100.0  # operations per second
        actual_throughput = total_operations / total_time
        assert actual_throughput >= min_throughput, (
            f"Throughput should be ≥{min_throughput} ops/sec, got {actual_throughput:.1f}"
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-s"])
